import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaCheckCircle,
  FaTimesCircle,
  FaSearch,
  FaFilter,
  FaClipboardList,
  FaExclamationTriangle,
  FaInfoCircle,
} from 'react-icons/fa';
const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { y: 20, opacity: 0 },
  show: {
    y: 0,
    opacity: 1,
  },
};

const ProcedureRequests = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [procedureRequests, setProcedureRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [procedureTypeFilter, setProcedureTypeFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseData, setResponseData] = useState({
    status: 'approved',
    responseNotes: '',
  });
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchProcedureRequests = async () => {
      if (!user || !token) {
        setError('Please log in to view procedure requests.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/procedure-requests`, config);

        if (Array.isArray(response.data)) {
          setProcedureRequests(response.data);
        } else {
          setError('Invalid data received from server');
          setProcedureRequests([]);
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        setError(err.response?.data?.message || 'Failed to load procedure requests');
        setProcedureRequests([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProcedureRequests();
  }, [user, token]);

  const handleSearch = (e) => setSearchTerm(e.target.value);
  const handleStatusFilterChange = (e) => setStatusFilter(e.target.value);
  const handleProcedureTypeFilterChange = (e) => setProcedureTypeFilter(e.target.value);

  // Get unique procedure types for filter dropdown
  const procedureTypes = [...new Set(procedureRequests.map(request => request.procedureType).filter(Boolean))];

  const filteredRequests = procedureRequests
    .filter(request => {
      // Apply status filter
      if (statusFilter !== 'all' && request.status !== statusFilter) {
        return false;
      }

      // Apply procedure type filter
      if (procedureTypeFilter !== 'all' && request.procedureType !== procedureTypeFilter) {
        return false;
      }

      // Apply search filter
      const searchTermLower = searchTerm.toLowerCase();
      return (
        request.patientName?.toLowerCase().includes(searchTermLower) ||
        request.patientNationalId?.includes(searchTerm) ||
        request.studentName?.toLowerCase().includes(searchTermLower) ||
        request.procedureType?.toLowerCase().includes(searchTermLower)
      );
    })
    .sort((a, b) => {
      // Sort by date and time (earliest to latest)
      const dateA = new Date(a.requestDate);
      const dateB = new Date(b.requestDate);
      return dateA - dateB;
    });

  const handleResponseSubmit = async (e) => {
    e.preventDefault();
    if (!selectedRequest) return;

    console.log('Submitting response for request:', selectedRequest);
    console.log('Response data:', responseData);

    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/procedure-requests/${selectedRequest.id || selectedRequest._id}`,
        responseData,
        config
      );

      if (response.data) {
        // Update the procedure request in the list
        setProcedureRequests(prevRequests =>
          prevRequests.map(req =>
            (req.id || req._id) === (selectedRequest.id || selectedRequest._id) ? response.data.procedureRequest : req
          )
        );

        setShowResponseModal(false);
        setSelectedRequest(null);
        setResponseData({
          status: 'approved',
          responseNotes: '',
        });
      }
    } catch (err) {
      console.error('Response error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to respond to procedure request');
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">Procedure Requests</h1>
                <p className="text-gray-600">Review and manage procedure requests from students</p>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="space-y-6 sm:space-y-8"
              >
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-3 sm:py-4 px-4 sm:px-6 text-center font-medium text-xs sm:text-sm text-[#0077B6] border-b-2 border-[#0077B6]"
                    >
                      Request Management
                    </div>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                      <h2 className="text-lg sm:text-xl font-bold text-[#0077B6] flex items-center">
                        <FaClipboardList className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[#0077B6]" />
                        Procedure Requests
                      </h2>
                      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                        <input
                          type="text"
                          placeholder="Search requests..."
                          value={searchTerm}
                          onChange={handleSearch}
                          className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg bg-white text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <select
                          value={statusFilter}
                          onChange={handleStatusFilterChange}
                          className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg bg-white text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="all">All Statuses</option>
                          <option value="pending">Pending</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                        </select>
                        <select
                          value={procedureTypeFilter}
                          onChange={handleProcedureTypeFilterChange}
                          className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg bg-white text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="all">All Procedures</option>
                          {procedureTypes.map(type => (
                            <option key={type} value={type}>{type}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredRequests.map((request) => (
                            <tr
                              key={request.id || request._id}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => {
                                setSelectedRequest(request);
                                if (request.status === 'pending') {
                                  setShowResponseModal(true);
                                }
                              }}
                            >
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                <div className="flex flex-col">
                                  <span>{new Date(request.requestDate).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                  })}</span>
                                  <span className="text-xs text-gray-500">
                                    {new Date(request.requestDate).toLocaleTimeString('en-US', {
                                      hour: '2-digit',
                                      minute: '2-digit',
                                      hour12: true
                                    })}
                                  </span>
                                </div>
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{request.studentName}</td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {request.patientName ?
                                  (request.patientNationalId ?
                                    `${request.patientName} (${request.patientNationalId})` :
                                    request.patientName) :
                                  'No patient specified'}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{request.procedureType}</td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                                <span className={`px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(request.status)}`}>
                                  {request.status}
                                </span>
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                                {request.status === 'pending' && (
                                  <div className="flex justify-end space-x-1 sm:space-x-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedRequest(request);
                                        setResponseData({
                                          status: 'approved',
                                          responseNotes: '',
                                        });
                                        setShowResponseModal(true);
                                      }}
                                      className="text-green-600 hover:text-green-800"
                                      title="Approve Request"
                                    >
                                      <FaCheckCircle className="inline h-4 w-4 sm:h-5 sm:w-5" />
                                    </button>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedRequest(request);
                                        setResponseData({
                                          status: 'rejected',
                                          responseNotes: '',
                                        });
                                        setShowResponseModal(true);
                                      }}
                                      className="text-red-600 hover:text-red-800"
                                      title="Reject Request"
                                    >
                                      <FaTimesCircle className="inline h-4 w-4 sm:h-5 sm:w-5" />
                                    </button>
                                  </div>
                                )}
                                {request.status !== 'pending' && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedRequest(request);
                                    }}
                                    className="text-blue-600 hover:text-blue-800"
                                    title="View Details"
                                  >
                                    <FaInfoCircle className="inline h-4 w-4 sm:h-5 sm:w-5" />
                                  </button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Response Modal */}
      {showResponseModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-[#0077B6]">
                {responseData.status === 'approved' ? 'Approve' : 'Reject'} Request
              </h2>
              <button
                onClick={() => setShowResponseModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleResponseSubmit}>
              <div className="mb-6">
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Student:</span> {selectedRequest.studentName}
                  </p>
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Patient:</span> {
                      selectedRequest.patientName ?
                        (selectedRequest.patientNationalId ?
                          `${selectedRequest.patientName} (${selectedRequest.patientNationalId})` :
                          selectedRequest.patientName) :
                        'No patient specified'
                    }
                  </p>
                  <p className="text-sm text-gray-700 mb-2">
                    <span className="font-medium">Procedure:</span> {selectedRequest.procedureType}
                  </p>
                  <p className="text-sm text-gray-700">
                    <span className="font-medium">Notes:</span> {selectedRequest.notes || 'None'}
                  </p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Response Notes</label>
                  <textarea
                    name="responseNotes"
                    value={responseData.responseNotes}
                    onChange={(e) => setResponseData({ ...responseData, responseNotes: e.target.value })}
                    rows="4"
                    placeholder="Provide any additional information about your decision"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <motion.button
                  type="button"
                  onClick={() => setShowResponseModal(false)}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  type="submit"
                  className={`px-6 py-2 text-white rounded-full font-medium transition-colors shadow-md ${
                    responseData.status === 'approved'
                      ? 'bg-gradient-to-r from-green-500 to-green-700 hover:from-green-600 hover:to-green-800'
                      : 'bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {responseData.status === 'approved' ? 'Approve' : 'Reject'}
                </motion.button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default ProcedureRequests;
