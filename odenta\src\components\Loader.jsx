import React from 'react';

const DentLyzerLoader = () => {
  return (
    <div
      className="flex items-center justify-center h-screen w-full bg-white"
      style={{ direction: 'ltr' }} // Force LTR direction
    >
      <div className="flex items-center justify-center h-screen w-full bg-white">
        <div className="loader flex items-center">
          <svg height="0" width="0" viewBox="0 0 64 64" className="absolute">
            <defs xmlns="http://www.w3.org/2000/svg">
              <linearGradient gradientUnits="userSpaceOnUse" y2="2" x2="0" y1="62" x1="0" id="b">
                <stop stopColor="#973BED"></stop>
                <stop stopColor="#007CFF" offset="1"></stop>
              </linearGradient>
              <linearGradient gradientUnits="userSpaceOnUse" y2="0" x2="0" y1="64" x1="0" id="c">
                <stop stopColor="#FFC800"></stop>
                <stop stopColor="#F0F" offset="1"></stop>
                <animateTransform
                  repeatCount="indefinite"
                  keySplines=".42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1;.42,0,.58,1"
                  keyTimes="0; 0.125; 0.25; 0.375; 0.5; 0.625; 0.75; 0.875; 1"
                  dur="8s"
                  values="0 32 32;-270 32 32;-270 32 32;-540 32 32;-540 32 32;-810 32 32;-810 32 32;-1080 32 32;-1080 32 32"
                  type="rotate"
                  attributeName="gradientTransform"
                ></animateTransform>
              </linearGradient>
              <linearGradient gradientUnits="userSpaceOnUse" y2="2" x2="0" y1="62" x1="0" id="d">
                <stop stopColor="#00E0ED"></stop>
                <stop stopColor="#00DA72" offset="1"></stop>
              </linearGradient>
            </defs>
          </svg>

          {/* O */}
          <svg {...iconProps}>
            <circle cx="32" cy="32" r="20" stroke="url(#d)" className="dash" pathLength="360" />
          </svg>

          {/* D */}
          <svg {...iconProps}>
            <path stroke="url(#b)" d="M12,12 v40 a16,16 0 0,0 16,-16 v-8 a16,16 0 0,0 -16,-16 z" className="dash" pathLength="360" />
          </svg>

          {/* E */}
          <svg {...iconProps}>
            <path stroke="url(#c)" d="M12,12 h32 m-32,0 v40 h32 m-32,-20 h24" className="dash" pathLength="360" />
          </svg>

          {/* N */}
          <svg {...iconProps}>
            <path stroke="url(#b)" d="M12,52 v-40 l20,40 v-40" className="dash" pathLength="360" />
          </svg>

          {/* T */}
          <svg {...iconProps}>
            <path stroke="url(#d)" d="M12,12 h40 m-20,0 v40" className="dash" pathLength="360" />
          </svg>

          {/* A */}
          <svg {...iconProps}>
            <path stroke="url(#c)" d="M12,52 l20,-40 l20,40 m-12,-12 h-16" className="dash" pathLength="360" />
          </svg>

          <style>{`
            .dash {
              animation: dashArray 2s ease-in-out infinite,
                dashOffset 2s linear infinite;
              fill: none;
              stroke-width: 4;
            }

            @keyframes dashArray {
              0% {
                stroke-dasharray: 0 1 359 0;
              }
              50% {
                stroke-dasharray: 0 359 1 0;
              }
              100% {
                stroke-dasharray: 359 1 0 0;
              }
            }

            @keyframes dashOffset {
              0% {
                stroke-dashoffset: 365;
              }
              100% {
                stroke-dashoffset: 5;
              }
            }
          `}</style>
        </div>
      </div>
    </div>
  );
};

const iconProps = {
  xmlns: "http://www.w3.org/2000/svg",
  fill: "none",
  viewBox: "0 0 64 64",
  width: "64",
  height: "64",
  className: "inline-block mx-1"
};

export default DentLyzerLoader;
