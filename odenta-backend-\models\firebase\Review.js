const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Review validation schema for Firebase
const reviewSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  patientId: Joi.string().required(),
  studentId: Joi.string().required(),
  studentName: Joi.string().required(),
  studentUniversity: Joi.string().required(),
  supervisorId: Joi.string().optional(),
  procedureType: Joi.string().required(),
  comment: Joi.string().default('N/A'),
  note: Joi.string().default('N/A'),
  reviewSteps: Joi.array().items(Joi.string()).default([]),
  chartId: Joi.string().optional(),
  teethChart: Joi.object({
    title: Joi.string().required(),
    date: Joi.date().required(),
    teeth: Joi.array().items(
      Joi.object({
        toothNumber: Joi.number().required(),
        procedure: Joi.string().required()
      })
    ).required()
  }).optional(),
  status: Joi.string().valid('pending', 'approved', 'rejected').default('pending'),
  submittedDate: Joi.date().default(() => new Date()),
  reviewedDate: Joi.date().optional(),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Review creation schema (without ID)
const createReviewSchema = reviewSchema.fork(['id'], (schema) => schema.forbidden());

// Review update schema (partial)
const updateReviewSchema = reviewSchema.fork(
  ['patientId', 'studentId', 'procedureType'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for Review operations
const ReviewHelpers = {
  // Validate review data
  validateCreate: (data) => createReviewSchema.validate(data),
  validateUpdate: (data) => updateReviewSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert ObjectId references to strings
    ['patientId', 'studentId', 'supervisorId', 'chartId'].forEach(field => {
      if (transformed[field] && typeof transformed[field] === 'object') {
        transformed[field] = transformed[field].toString();
      }
    });
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    ['submittedDate', 'reviewedDate', 'createdAt', 'updatedAt'].forEach(field => {
      if (prepared[field] && typeof prepared[field] === 'string') {
        prepared[field] = new Date(prepared[field]);
      }
    });
    
    // Handle teeth chart date
    if (prepared.teethChart && prepared.teethChart.date && typeof prepared.teethChart.date === 'string') {
      prepared.teethChart.date = new Date(prepared.teethChart.date);
    }
    
    return prepared;
  },
  
  // Get reviews by status
  getByStatus: (reviews, status) => {
    return reviews.filter(review => review.status === status);
  },
  
  // Get reviews by student
  getByStudent: (reviews, studentId) => {
    return reviews.filter(review => review.studentId === studentId);
  },
  
  // Get reviews by supervisor
  getBySupervisor: (reviews, supervisorId) => {
    return reviews.filter(review => review.supervisorId === supervisorId);
  },
  
  // Update review status
  updateStatus: (reviewData, newStatus, supervisorId = null) => {
    const validStatuses = ['pending', 'approved', 'rejected'];
    if (!validStatuses.includes(newStatus)) {
      throw new Error(`Invalid status: ${newStatus}`);
    }
    
    const updated = {
      ...reviewData,
      status: newStatus,
      reviewedDate: new Date(),
      updatedAt: new Date()
    };
    
    if (supervisorId) {
      updated.supervisorId = supervisorId;
    }
    
    return updated;
  },

  // Get review steps by procedure type
  getReviewStepsByType: (procedureType, subType = '') => {
    const steps = {
      'Operative': [
        { description: 'Patient Assessment', completed: false },
        { description: 'Treatment Planning', completed: false },
        { description: 'Cavity Preparation', completed: false },
        { description: 'Restoration Placement', completed: false },
        { description: 'Finishing and Polishing', completed: false }
      ],
      'Fixed Prosthodontics': [
        { description: 'Patient Assessment', completed: false },
        { description: 'Treatment Planning', completed: false },
        { description: 'Tooth Preparation', completed: false },
        { description: 'Impression Taking', completed: false },
        { description: 'Temporary Restoration', completed: false },
        { description: 'Final Cementation', completed: false }
      ],
      'Removable Prosthodontics': [
        { description: 'Patient Assessment', completed: false },
        { description: 'Treatment Planning', completed: false },
        { description: 'Primary Impression', completed: false },
        { description: 'Secondary Impression', completed: false },
        { description: 'Try-in Appointment', completed: false },
        { description: 'Final Delivery', completed: false }
      ],
      'Endodontics': [
        { description: 'Patient Assessment', completed: false },
        { description: 'Treatment Planning', completed: false },
        { description: 'Access Cavity Preparation', completed: false },
        { description: 'Canal Cleaning and Shaping', completed: false },
        { description: 'Canal Filling', completed: false },
        { description: 'Final Restoration', completed: false }
      ],
      'Periodontics': [
        { description: 'Patient Assessment', completed: false },
        { description: 'Treatment Planning', completed: false },
        { description: 'Scaling and Root Planing', completed: false },
        { description: 'Surgical Procedures (if needed)', completed: false },
        { description: 'Maintenance and Follow-up', completed: false }
      ]
    };

    // Handle subtypes
    if (subType) {
      const baseSteps = steps[procedureType] || [];
      return baseSteps.map(step => ({
        ...step,
        description: `${step.description} (${subType})`
      }));
    }

    return steps[procedureType] || [
      { description: 'Step 1', completed: false },
      { description: 'Step 2', completed: false },
      { description: 'Step 3', completed: false }
    ];
  }
};

module.exports = {
  reviewSchema,
  createReviewSchema,
  updateReviewSchema,
  ReviewHelpers,
  COLLECTION_NAME: COLLECTIONS.REVIEWS
};
