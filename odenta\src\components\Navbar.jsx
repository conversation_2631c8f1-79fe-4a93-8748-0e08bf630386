import React, { useState, useEffect, useCallback, memo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import { motion } from 'framer-motion';
import { FaTooth } from 'react-icons/fa';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = [
  '/',
  '/universities',
  '/about',
  '/contact',
  '/universityServices',
  '/try-ai',
  '/login',
];

// Memoized NavItem component
const NavItem = memo(({ navItem, currentLanguage, onClick }) => {
  const { t } = useTranslation();
  const to =
    navItem === 'Home' ? '/' : navItem === 'Universities' ? '/universities' : `/${navItem.toLowerCase()}`;

  return (
    <motion.li
      variants={{
        hidden: { opacity: 0, y: 20 },
        show: { opacity: 1, y: 0 },
      }}
    >
      <Link
        to={to}
        className="relative text-[#0077B6] hover:text-[#20B2AA] font-medium text-lg transition-colors duration-300 group"
        onClick={onClick}
      >
        {t(`navbar.${navItem}`)}
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
      </Link>
    </motion.li>
  );
});

const Navbar = () => {
  const { t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isServicesDropdownOpen, setIsServicesDropdownOpen] = useState(false);
  const navigate = useNavigate();

  // Secure navigation handler
  const safeNavigate = useCallback((path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  }, [navigate]);

  // Toggle mobile menu
  const toggleMenu = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
    if (isServicesDropdownOpen) setIsServicesDropdownOpen(false);
  }, [isServicesDropdownOpen]);

  // Toggle services dropdown
  const toggleServicesDropdown = useCallback(() => {
    setIsServicesDropdownOpen((prev) => !prev);
  }, []);

  // Change language with error handling
  const changeLanguage = useCallback((lng) => {
    if (!['en', 'ar'].includes(lng)) {
      console.warn('Invalid language:', lng);
      return;
    }
    i18n
      .changeLanguage(lng)
      .then(() => {
        setCurrentLanguage(lng);
        document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
        document.documentElement.lang = lng;
        localStorage.setItem('dentlyzer-language', lng);
      })
      .catch((err) => {
        console.error('Error changing language:', err);
      });
  }, []);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('dentlyzer-language') || 'en';
    changeLanguage(savedLanguage);

    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [changeLanguage]);

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const dropdown = {
    hidden: { opacity: 0, height: 0 },
    show: { opacity: 1, height: 'auto', transition: { duration: 0.3 } },
  };

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring', stiffness: 100 }}
      className={`bg-white py-4 px-6 sticky top-0 z-50 transition-all duration-300 ${
        scrolled ? 'shadow-md' : 'shadow-sm'
      }`}
    >
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="flex items-center"
        >
          {/* <Link to="/" aria-label="Dentlyzer Home">
            <motion.div
              className="w-12 h-12 flex items-center justify-center mr-3"
              whileHover={{ rotate: 10 }}
            >
              <FaTooth className="w-10 h-10 text-[#0077B6]" />
            </motion.div>
          </Link> */}
        <Link to="/">
          <img 
            src="/imgs/odenta-logo.png" // Update this path based on your project structure
            alt="ODenta Logo"
            className="h-[60px] w-auto" // Adjust size as needed
          />
        </Link>
        </motion.div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center">
          <motion.ul
            variants={container}
            initial="hidden"
            animate="show"
            className={`flex items-center space-x-8 ${currentLanguage === 'ar' ? 'space-x-reverse' : ''}`}
          >
            {['Home', 'Universities', 'About', 'Contact'].map((navItem) => (
              <NavItem
                key={navItem}
                navItem={navItem}
                currentLanguage={currentLanguage}
                onClick={() => setIsMenuOpen(false)}
              />
            ))}
            {/* Services Dropdown */}
            <motion.li
              variants={{ hidden: { opacity: 0, y: 20 }, show: { opacity: 1, y: 0 } }}
            >
              <Link
                to="/universityServices"
                className="relative text-[#0077B6] hover:text-[#20B2AA] font-medium text-lg transition-colors duration-300 group"
              >
                {t('navbar.Services')}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
              </Link>
            </motion.li>
          </motion.ul>

          <div className={`flex items-center ${currentLanguage === 'ar' ? 'space-x-reverse' : ''} space-x-4 ml-8`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg"
              aria-label={t('navbar.Login')}
            >
              <Link to="/login">{t('navbar.Login')}</Link>
            </motion.button>
            {currentLanguage === 'ar' ? (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => changeLanguage('en')}
                className="text-[#0077B6] font-medium px-4 py-2 rounded-full transition-all duration-300 hover:bg-[rgba(0,119,182,0.05)]"
                aria-label="Switch to English"
              >
                English
              </motion.button>
            ) : (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => changeLanguage('ar')}
                className="text-[#0077B6] font-medium px-4 py-2 rounded-full transition-all duration-300 hover:bg-[rgba(0,119,182,0.05)]"
                aria-label="Switch to Arabic"
              >
                العربية
              </motion.button>
            )}
            <motion.button
              whileHover={{ scale: 1.1, boxShadow: '0 0 15px rgba(59, 130, 246, 0.6)' }}
              whileTap={{ scale: 0.95 }}
              onClick={() => safeNavigate('/try-ai')}
              className="bg-gradient-to-r from-[#0077B6] via-[#20B2AA] to-[#28A745] text-white px-6 py-2 rounded-full font-bold hover:from-[#0066A0] hover:via-[#1A9E98] hover:to-[#218838] transition-all duration-300 shadow-lg hover:shadow-xl"
              aria-label={t('navbar.TryOurAI')}
            >
              {t('navbar.TryOurAI')}
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="md:hidden text-[#0077B6] text-3xl focus:outline-none"
          onClick={toggleMenu}
          aria-label={t('navbar.ToggleMenu')}
        >
          {isMenuOpen ? '✕' : '☰'}
        </motion.button>
      </div>

      {/* Mobile Menu */}
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: isMenuOpen ? 1 : 0, height: isMenuOpen ? 'auto' : 0 }}
        className={`md:hidden bg-white rounded-lg shadow-xl mt-4 overflow-hidden ${isMenuOpen ? 'p-6' : 'p-0'}`}
      >
        <ul className="space-y-4">
          {['Home', 'Universities', 'About', 'Contact'].map((navItem) => (
            <motion.li
              key={navItem}
              initial={{ x: -20, opacity: 0 }}
              animate={isMenuOpen ? { x: 0, opacity: 1 } : { x: -20, opacity: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Link
                to={navItem === 'Home' ? '/' : navItem === 'Universities' ? '/universities' : `/${navItem.toLowerCase()}`}
                className="block text-[#0077B6] hover:text-[#20B2AA] font-medium text-lg py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                {t(`navbar.${navItem}`)}
              </Link>
            </motion.li>
          ))}
          <motion.li
            initial={{ x: -20, opacity: 0 }}
            animate={isMenuOpen ? { x: 0, opacity: 1 } : { x: -20, opacity: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Link
              to="/universityServices"
              onClick={() => setIsMenuOpen(false)}
              className="block text-[#0077B6] hover:text-[#20B2AA] font-medium text-lg py-2"
            >
              {t('navbar.Services')}
            </Link>
          </motion.li>
        </ul>
        <div className="mt-6 pt-6 border-t border-gray-200 flex flex-col space-y-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg"
            aria-label={t('navbar.Login')}
          >
            <Link to="/login" onClick={() => setIsMenuOpen(false)}>
              {t('navbar.Login')}
            </Link>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => changeLanguage(currentLanguage === 'en' ? 'ar' : 'en')}
            className="w-full text-[#0077B6] font-medium px-4 py-3 rounded-full transition-all duration-300 hover:bg-[rgba(0,119,182,0.05)]"
            aria-label={currentLanguage === 'en' ? 'Switch to Arabic' : 'Switch to English'}
          >
            {currentLanguage === 'en' ? 'العربية' : 'English'}
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              safeNavigate('/try-ai');
              setIsMenuOpen(false);
            }}
            className="w-full bg-gradient-to-r from-[#0077B6] via-[#20B2AA] to-[#28A745] text-white px-6 py-3 rounded-full font-bold hover:from-[#0066A0] hover:via-[#1A9E98] hover:to-[#218838] transition-all duration-300 shadow-lg"
            aria-label={t('navbar.TryOurAI')}
          >
            {t('navbar.TryOurAI')}
          </motion.button>
        </div>
      </motion.div>
    </motion.header>
  );
};

export default Navbar;
