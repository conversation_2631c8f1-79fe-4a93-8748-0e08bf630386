// Firebase Client SDK for development (works with public rules)
const { initializeApp } = require('firebase/app');
const { getFirestore, connectFirestoreEmulator, collection, doc, addDoc, getDocs, getDoc, updateDoc, deleteDoc, query, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAaJKFcxipd7SBS-GQK97ZIFr0oBBEKQOU",
  authDomain: "odenta-82359.firebaseapp.com",
  projectId: "odenta-82359",
  storageBucket: "odenta-82359.firebasestorage.app",
  messagingSenderId: "632651401940",
  appId: "1:632651401940:web:12ccba8515c9648b1d8941",
  measurementId: "G-HFXCKFE42Y"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

console.log('🔥 Firebase Client SDK initialized for development');
console.log(`📡 Project ID: ${firebaseConfig.projectId}`);

// Helper functions using Firebase Client SDK
const ClientFirestoreHelpers = {
  // Create a document
  async create(collectionName, data) {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return { id: docRef.id, ...data };
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  },

  // Get all documents from a collection
  async getAll(collectionName) {
    try {
      const querySnapshot = await getDocs(collection(db, collectionName));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting documents:', error);
      throw error;
    }
  },

  // Get document by ID
  async getById(collectionName, id) {
    try {
      const docRef = doc(db, collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  },

  // Get documents by field value
  async getByField(collectionName, field, value) {
    try {
      const q = query(collection(db, collectionName), where(field, '==', value));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting documents by field:', error);
      throw error;
    }
  },

  // Update a document
  async update(collectionName, id, data) {
    try {
      const docRef = doc(db, collectionName, id);
      await updateDoc(docRef, {
        ...data,
        updatedAt: new Date()
      });
      
      // Return updated document
      const updatedDoc = await this.getById(collectionName, id);
      return updatedDoc;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  },

  // Delete a document
  async delete(collectionName, id) {
    try {
      const docRef = doc(db, collectionName, id);
      await deleteDoc(docRef);
      return { id, deleted: true };
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  },

  // Find documents with where clause
  async find(collectionName, whereClause, orderBy = null, limit = null) {
    try {
      let q = collection(db, collectionName);
      
      if (whereClause) {
        q = query(q, where(whereClause.field, whereClause.operator, whereClause.value));
      }
      
      const querySnapshot = await getDocs(q);
      let results = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Apply limit if specified
      if (limit) {
        results = results.slice(0, limit);
      }

      return results;
    } catch (error) {
      console.error('Error finding documents:', error);
      throw error;
    }
  },

  // Find one document
  async findOne(collectionName, whereClause) {
    const results = await this.find(collectionName, whereClause, null, 1);
    return results.length > 0 ? results[0] : null;
  }
};

module.exports = {
  db,
  ClientFirestoreHelpers,
  connectFirestore: async () => {
    console.log('✅ Connected to Firebase Firestore (Client SDK)');
    console.log(`🌐 Environment: development`);
    return db;
  }
};
