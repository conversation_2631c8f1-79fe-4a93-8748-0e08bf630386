// controllers/analyticsController.js
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const getAnalyticsData = async (req, res) => {
  try {
    const studentId = req.user.studentId || req.user.id;
    const { timeRange = 'month' } = req.query;

    // Calculate date range
    const now = new Date();
    let startDate;
    switch (timeRange) {
      case 'week':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'year':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      case 'month':
      default:
        startDate = new Date(now.setMonth(now.getMonth() - 1));
    }

    // Procedure counts based on treatment sheets
    const treatmentSheets = await FirestoreHelpers.aggregate(COLLECTIONS.PATIENTS, [
      { $match: { drId: studentId }},
      { $unwind: '$treatmentSheets' },
      { $match: {
        'treatmentSheets.createdAt': { $gte: startDate }
      }},
      { $group: {
        _id: '$treatmentSheets.type',
        count: { $sum: 1 }
      }}
    ]);

    const procedures = treatmentSheets.map(sheet => ({
      name: sheet._id || 'Unknown',
      count: sheet.count
    }));

    // Patient demographics
    const patients = await FirestoreHelpers.aggregate(COLLECTIONS.PATIENTS, [
      { $match: { drId: studentId }},
      { $group: {
        _id: {
          $switch: {
            branches: [
              { case: { $lte: ['$age', 12] }, then: 'Children' },
              { case: { $lte: ['$age', 19] }, then: 'Adolescents' },
              { case: { $lte: ['$age', 64] }, then: 'Adults' }
            ],
            default: 'Seniors'
          }
        },
        value: { $sum: 1 }
      }},
      { $project: { name: '$_id', value: 1, _id: 0 }}
    ]);

    // Performance metrics based on treatment sheets
    const sheetsSubmitted = await FirestoreHelpers.aggregate(COLLECTIONS.PATIENTS, [
      { $match: { drId: studentId }},
      { $unwind: '$treatmentSheets' },
      { $match: {
        'treatmentSheets.createdAt': { $gte: startDate }
      }},
      { $count: 'total' }
    ]);

    // Enhanced treatment sheet analytics
    const treatmentSheetAnalytics = await getTreatmentSheetAnalytics(studentId, startDate);

    const performanceMetrics = {
      averageProcedureTime: await getAverageProcedureTime(studentId, startDate),
      studentAccuracy: await getStudentAccuracy(studentId, startDate),
      commonIssues: await getCommonIssues(studentId, startDate),
      sheetsSubmitted: sheetsSubmitted[0]?.total || 0,
      casesCompleted: await FirestoreHelpers.countDocuments(COLLECTIONS.APPOINTMENTS, {
        doctor: studentId,
        status: 'completed',
        date: { $gte: startDate }
      }),
      casesReferred: await FirestoreHelpers.countDocuments(COLLECTIONS.APPOINTMENTS, {
        doctor: studentId,
        status: 'referred',
        date: { $gte: startDate }
      })
    };

    // Time series data
    const timeSeries = await getTimeSeriesData(studentId, timeRange);

    // Get appointments per month for overview
    const appointmentsPerMonth = await getAppointmentsPerMonth(studentId, timeRange);

    res.json({
      procedures,
      patientDemographics: patients,
      performanceMetrics,
      timeSeries,
      appointmentsPerMonth,
      treatmentSheetAnalytics
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ message: 'Server error fetching analytics' });
  }
};

const getAverageProcedureTime = async (studentId, startDate) => {
  const result = await FirestoreHelpers.aggregate(COLLECTIONS.APPOINTMENTS, [
    { $match: { doctor: studentId, status: 'completed', date: { $gte: startDate }}},
    { $group: { 
      _id: null,
      avgTime: { $avg: { $divide: [{ $subtract: ['$endTime', '$startTime'] }, 60000] }}
    }}
  ]);
  return result[0]?.avgTime ? `${Math.round(result[0].avgTime)} mins` : 'N/A';
};

const getStudentAccuracy = async (studentId, startDate) => {
  // Assuming evaluations are stored in appointments
  const result = await FirestoreHelpers.aggregate(COLLECTIONS.APPOINTMENTS, [
    { $match: { doctor: studentId, status: 'completed', date: { $gte: startDate }}},
    { $group: { 
      _id: null,
      avgAccuracy: { $avg: '$evaluation.accuracy' } // Assuming accuracy is a field 0-100
    }}
  ]);
  return result[0]?.avgAccuracy ? `${Math.round(result[0].avgAccuracy)}%` : 'N/A';
};

const getCommonIssues = async (studentId, startDate) => {
  const result = await FirestoreHelpers.aggregate(COLLECTIONS.APPOINTMENTS, [
    { $match: { doctor: studentId, date: { $gte: startDate }}},
    { $unwind: '$evaluation.issues' },
    { $group: { 
      _id: '$evaluation.issues',
      count: { $sum: 1 }
    }},
    { $sort: { count: -1 }},
    { $limit: 3 }
  ]);
  return result.map(r => r._id).filter(Boolean);
};

const getTimeSeriesData = async (studentId, timeRange) => {
  // Get treatment sheet data
  const sheetData = await FirestoreHelpers.aggregate(COLLECTIONS.PATIENTS, [
    { $match: { drId: studentId }},
    { $unwind: '$treatmentSheets' },
    { $match: { 'treatmentSheets.createdAt': { $exists: true } }},
    { $group: {
      _id: {
        month: { $month: '$treatmentSheets.createdAt' },
        ...(timeRange === 'year' && { year: { $year: '$treatmentSheets.createdAt' }})
      },
      sheets: { $sum: 1 },
      patients: { $addToSet: '$nationalId' }
    }},
    { $project: {
      month: { $arrayElemAt: [
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        { $subtract: ['$_id.month', 1] }
      ]},
      sheets: 1,
      patients: { $size: '$patients' }
    }},
    { $sort: { '_id.month': 1 }}
  ]);

  // If no sheet data, create some sample data for the current month
  if (sheetData.length === 0) {
    const currentMonth = new Date().getMonth();
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    sheetData.push({
      month: monthNames[currentMonth],
      sheets: 0,
      patients: 0
    });
  }

  // Get appointment data for comparison
  const appointmentData = await FirestoreHelpers.aggregate(COLLECTIONS.APPOINTMENTS, [
    { $match: { doctor: studentId, status: 'completed' }},
    { $group: {
      _id: {
        month: { $month: '$date' },
        ...(timeRange === 'year' && { year: { $year: '$date' }})
      },
      procedures: { $sum: 1 },
      patients: { $addToSet: '$patientId' }
    }},
    { $project: {
      month: { $arrayElemAt: [
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        { $subtract: ['$_id.month', 1] }
      ]},
      procedures: 1,
      patients: { $size: '$patients' }
    }},
    { $sort: { '_id.month': 1 }}
  ]);

  // Combine the data
  return {
    sheets: sheetData,
    appointments: appointmentData
  };
};

const getAppointmentsPerMonth = async (studentId, timeRange) => {
  const appointmentData = await FirestoreHelpers.aggregate(COLLECTIONS.APPOINTMENTS, [
    { $match: { doctor: studentId }},
    { $group: {
      _id: {
        month: { $month: '$date' },
        ...(timeRange === 'year' && { year: { $year: '$date' }})
      },
      total: { $sum: 1 },
      completed: { $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }},
      pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }},
      cancelled: { $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }}
    }},
    { $project: {
      month: { $arrayElemAt: [
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        { $subtract: ['$_id.month', 1] }
      ]},
      total: 1,
      completed: 1,
      pending: 1,
      cancelled: 1
    }},
    { $sort: { '_id.month': 1 }}
  ]);

  // If no appointment data, create some sample data for the current month
  if (appointmentData.length === 0) {
    const currentMonth = new Date().getMonth();
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    appointmentData.push({
      month: monthNames[currentMonth],
      total: 0,
      completed: 0,
      pending: 0,
      cancelled: 0
    });
  }

  return appointmentData;
};

// New function to get detailed treatment sheet analytics
const getTreatmentSheetAnalytics = async (studentId, startDate) => {
  try {
    // Get treatment sheet completion trends by month
    const completionTrends = await FirestoreHelpers.aggregate(COLLECTIONS.PATIENTS, [
      { $match: { drId: studentId }},
      { $unwind: '$treatmentSheets' },
      { $match: { 'treatmentSheets.createdAt': { $gte: startDate }}},
      { $group: {
        _id: {
          month: { $month: '$treatmentSheets.createdAt' },
          year: { $year: '$treatmentSheets.createdAt' }
        },
        count: { $sum: 1 }
      }},
      { $project: {
        month: { $arrayElemAt: [
          ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          { $subtract: ['$_id.month', 1] }
        ]},
        year: '$_id.year',
        count: 1
      }},
      { $sort: { '_id.year': 1, '_id.month': 1 }}
    ]);

    return {
      completionTrends
    };
  } catch (error) {
    console.error('Error getting treatment sheet analytics:', error);
    return {
      completionTrends: []
    };
  }
};

module.exports = { getAnalyticsData };