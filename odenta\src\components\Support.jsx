import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaEnvelope, FaPhone, FaWhatsapp, FaTooth, FaArrowLeft } from 'react-icons/fa';
import DOMPurify from 'dompurify';

const Support = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [previousPath, setPreviousPath] = useState('/');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState('');
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);

  // Get the previous path from the location state or default to dashboard
  useEffect(() => {
    if (location.state && location.state.from) {
      setPreviousPath(location.state.from);
    } else {
      // If no previous path is provided, try to determine a sensible default
      const user = JSON.parse(localStorage.getItem('user')) || {};
      const role = user.role || 'student';

      switch (role) {
        case 'student':
          setPreviousPath('/student/dashboard');
          break;
        case 'supervisor':
          setPreviousPath('/supervisor/dashboard');
          break;
        case 'admin':
          setPreviousPath('/admin/dashboard');
          break;
        case 'superadmin':
          setPreviousPath('/superadmin/dashboard');
          break;
        case 'dentist':
          setPreviousPath('/dentist/dashboard');
          break;
        default:
          setPreviousPath('/');
      }
    }
  }, [location]);

  // Sanitize input to prevent XSS
  const sanitizeInput = (input) => {
    return DOMPurify.sanitize(input, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitDisabled) return;

    setIsSubmitDisabled(true);
    setFormStatus('');

    // Sanitize form inputs
    const sanitizedFormData = {
      name: sanitizeInput(formData.name),
      email: sanitizeInput(formData.email),
      message: sanitizeInput(formData.message)
    };

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedFormData.email)) {
      setFormStatus('Please enter a valid email address.');
      setIsSubmitDisabled(false);
      return;
    }

    try {
      // Simulate API call
      setTimeout(() => {
        setFormStatus('Your message has been sent successfully!');
        setFormData({ name: '', email: '', message: '' });
        setTimeout(() => {
          setFormStatus('');
          setIsSubmitDisabled(false);
        }, 3000);
      }, 1000);
    } catch (error) {
      setFormStatus('There was an error sending your message. Please try again.');
      setIsSubmitDisabled(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        <div className="mb-8">
          <button
            onClick={() => navigate(previousPath)}
            className="inline-flex items-center text-[#0077B6] hover:text-[#20B2AA] transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Back to Previous Page
          </button>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-lg overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] p-8 text-white text-center">
            <div className="flex justify-center items-center mb-4">
              <h1 className="text-3xl font-bold">
                O<span className="text-white opacity-80">Denta</span>
              </h1>
            </div>
            <p className="text-white text-opacity-90 text-lg max-w-2xl mx-auto">
              We're here to help you with any questions or issues you might have.
              Our support team is ready to assist you.
            </p>
          </div>

          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {/* Contact Information */}
              <div>
                <h2 className="text-2xl font-bold text-[#0077B6] mb-6">Contact Information</h2>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4">
                      <FaEnvelope className="h-6 w-6 text-[#0077B6]" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-[#333333]">Email</h3>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-[#0077B6] hover:text-[#20B2AA] transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4">
                      <FaPhone className="h-6 w-6 text-[#0077B6]" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-[#333333]">Phone</h3>
                      <a
                        href="tel:+201276902211"
                        className="text-[#0077B6] hover:text-[#20B2AA] transition-colors"
                      >
                        +20 ************
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="bg-[rgba(0,119,182,0.1)] rounded-full p-3 mr-4">
                      <FaWhatsapp className="h-6 w-6 text-[#0077B6]" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-[#333333]">WhatsApp</h3>
                      <a
                        href="https://wa.me/201276902211"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[#0077B6] hover:text-[#20B2AA] transition-colors"
                      >
                        +20 ************
                      </a>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-[rgba(0,119,182,0.05)] rounded-lg border border-[rgba(0,119,182,0.1)]">
                  <h3 className="text-xl font-bold text-[#0077B6] mb-3">About Dentlyzer</h3>
                  <p className="text-[#333333] mb-4">
                    Dentlyzer is a comprehensive dental management system designed to streamline
                    dental education and practice. Our platform connects dental students,
                    supervisors, and clinics to provide a seamless experience.
                  </p>
                  <p className="text-[#333333]">
                    Our support team is available Monday through Friday, 9 AM to 5 PM (EET).
                    We typically respond to inquiries within 24 hours.
                  </p>
                </div>
              </div>

              {/* Contact Form */}
              <div>
                <h2 className="text-2xl font-bold text-[#0077B6] mb-6">Send Us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-[#333333] font-medium mb-2">
                      Your Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]"
                      placeholder="Enter your name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-[#333333] font-medium mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]"
                      placeholder="Enter your email"
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-[#333333] font-medium mb-2">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] h-32"
                      placeholder="How can we help you?"
                    ></textarea>
                  </div>
                  {formStatus && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className={`${formStatus.includes('error') ? 'text-red-600' : 'text-green-600'}`}
                    >
                      {formStatus}
                    </motion.p>
                  )}
                  <motion.button
                    whileHover={{ scale: isSubmitDisabled ? 1 : 1.05 }}
                    whileTap={{ scale: isSubmitDisabled ? 1 : 0.95 }}
                    type="submit"
                    disabled={isSubmitDisabled}
                    className={`w-full bg-gradient-to-r ${
                      isSubmitDisabled
                        ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed'
                        : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'
                    } text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md`}
                  >
                    Send Message
                  </motion.button>
                </form>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Support;
