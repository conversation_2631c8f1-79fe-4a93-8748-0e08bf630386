const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const fixStudentIds = async () => {
  try {
    console.log('🔄 Starting student ID fix...');
    
    // Get all reviews
    const allReviews = await FirestoreHelpers.getAll(COLLECTIONS.REVIEWS);
    console.log(`Found ${allReviews.length} reviews to process`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const review of allReviews) {
      // Skip signature storage reviews
      if (review.procedureType === 'Signature Storage' || review.patientId?.nationalId === 'signature-storage') {
        skippedCount++;
        continue;
      }
      
      // Check if this review has the old structure (studentId is a Firestore ID)
      if (review.studentId && review.studentId.length > 20) {
        // This looks like a Firestore ID, we need to fix it
        console.log(`Fixing review ${review._id}: studentId = ${review.studentId}`);
        
        // Find the student by Firestore ID
        const student = await FirestoreHelpers.findById(COLLECTIONS.STUDENTS, review.studentId);
        
        if (student) {
          // Update the review with the correct student ID
          const updatedData = {
            ...review,
            studentId: student.studentId, // Use the actual student ID
            studentFirestoreId: review.studentId, // Store the old Firestore ID
            updatedAt: new Date()
          };
          
          await FirestoreHelpers.update(COLLECTIONS.REVIEWS, review._id, updatedData);
          updatedCount++;
          console.log(`✅ Updated review ${review._id} with student ID: ${student.studentId}`);
        } else {
          console.log(`❌ Student not found for review ${review._id}`);
          skippedCount++;
        }
      } else {
        // Already has the correct structure
        skippedCount++;
      }
    }
    
    console.log(`\n✅ Migration completed:`);
    console.log(`   - Updated: ${updatedCount} reviews`);
    console.log(`   - Skipped: ${skippedCount} reviews`);
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
  }
};

// Run the migration
fixStudentIds(); 