# ODenta Account Management Guide

## Overview
This guide explains how to manage user accounts in the ODenta system, including creating superadmins, admins, supervisors, students, and assistants.

## Account Types

### 1. Superadmin
- **Highest level access**
- Can manage all universities and accounts
- Access to system-wide analytics
- Can create/modify all other account types

### 2. Admin
- **University-level management**
- Can manage users within their university
- Access to university analytics
- Can create students, supervisors, and assistants

### 3. Supervisor
- **Academic oversight**
- Can review student work
- Manage assigned students
- Access to student progress

### 4. Student
- **Primary users**
- Manage patients
- Submit work for review
- Access to learning materials

### 5. Assistant
- **Support role**
- Can add patients for students
- Limited administrative access
- University-specific access

## Creating Accounts

### Superadmin Accounts

#### Method 1: Google OAuth (Recommended)
Pre-configured Google accounts that can sign in directly:
- `<EMAIL>`
- `<EMAIL>`

To add more Google OAuth superadmins:
1. Add the email to the `superadminEmails` array in `authController.js`
2. The account will be created automatically on first Google sign-in

#### Method 2: Manual Creation
```bash
# Create the pre-configured superadmin accounts
npm run create:superadmins

# Or run the full migration (includes superadmins)
npm run migrate:firebase
```

#### Method 3: Direct Database Creation
```javascript
const superadminData = {
  email: '<EMAIL>',
  name: 'New Superadmin',
  role: 'superadmin',
  password: '', // For Google auth
  plainPassword: '',
  googleId: '',
  picture: '',
  createdAt: new Date(),
  updatedAt: new Date()
};

await FirestoreHelpers.create(COLLECTIONS.CONFIGS, superadminData);
```

### Admin Accounts

#### Through Superadmin Dashboard:
1. Login as superadmin
2. Navigate to Account Management
3. Click "Create Admin"
4. Fill in details:
   - Email
   - Name
   - University
   - Password (optional for Google users)

#### Programmatically:
```javascript
const adminData = {
  email: '<EMAIL>',
  name: 'University Admin',
  role: 'admin',
  university: 'UNIVERSITY_ID',
  password: hashedPassword,
  createdAt: new Date(),
  updatedAt: new Date()
};

await FirestoreHelpers.create(COLLECTIONS.ADMINS, adminData);
```

### Supervisor Accounts

#### Through Admin Dashboard:
1. Login as admin
2. Navigate to People Management
3. Click "Add Supervisor"
4. Fill in details and assign to university

#### Programmatically:
```javascript
const supervisorData = {
  email: '<EMAIL>',
  name: 'Dr. Supervisor Name',
  role: 'supervisor',
  university: 'UNIVERSITY_ID',
  password: hashedPassword,
  students: [],
  createdAt: new Date(),
  updatedAt: new Date()
};

await FirestoreHelpers.create(COLLECTIONS.SUPERVISORS, supervisorData);
```

### Student Accounts

#### Through Admin Dashboard:
1. Login as admin
2. Navigate to People Management
3. Click "Add Student"
4. Fill in details including studentId

#### Programmatically:
```javascript
const studentData = {
  studentId: 'STU001',
  email: '<EMAIL>',
  name: 'Student Name',
  role: 'student',
  university: 'UNIVERSITY_ID',
  password: hashedPassword,
  patients: [],
  reviews: [],
  appointments: [],
  createdAt: new Date(),
  updatedAt: new Date()
};

await FirestoreHelpers.create(COLLECTIONS.STUDENTS, studentData);
```

### Assistant Accounts

#### Through Admin Dashboard:
1. Login as admin
2. Navigate to People Management
3. Click "Add Assistant"
4. Fill in details and university assignment

#### Programmatically:
```javascript
const assistantData = {
  email: '<EMAIL>',
  name: 'Assistant Name',
  role: 'assistant',
  university: 'UNIVERSITY_ID',
  password: hashedPassword,
  createdAt: new Date(),
  updatedAt: new Date()
};

await FirestoreHelpers.create(COLLECTIONS.ASSISTANTS, assistantData);
```

## Account Management Scripts

### Available Scripts:
```bash
# Create superadmin accounts
npm run create:superadmins

# Full migration with sample data
npm run migrate:firebase

# Reset all passwords (development only)
# Available through auth routes: POST /api/auth/reset-all-passwords
```

## Google OAuth Setup

### Backend Configuration:
1. Add `GOOGLE_CLIENT_ID` to your `.env` file
2. The backend will automatically handle Google token verification
3. Superadmin emails are whitelisted in the auth controller

### Frontend Configuration:
1. Add `REACT_APP_GOOGLE_CLIENT_ID` to your frontend `.env` file
2. The Google Sign-In button will appear on the login page
3. Users can sign in with their Google accounts

## Security Considerations

### Password Management:
- All passwords are hashed using bcryptjs
- Google OAuth users don't need passwords
- Password reset functionality available

### Access Control:
- Role-based access control (RBAC)
- University-scoped data access
- JWT token authentication

### Firestore Security:
- Use production security rules for live deployment
- Development rules allow all operations (for testing only)

## Troubleshooting

### Common Issues:

1. **Google Sign-In Not Working:**
   - Check `GOOGLE_CLIENT_ID` environment variables
   - Verify Google OAuth is enabled in Firebase Console
   - Check browser console for errors

2. **Account Creation Fails:**
   - Verify Firestore rules allow write operations
   - Check for duplicate email addresses
   - Ensure required fields are provided

3. **Login Issues:**
   - Verify user exists in correct collection
   - Check password hashing
   - Confirm JWT secret is set

### Debug Mode:
Set `NODE_ENV=development` to see detailed error messages.

## Best Practices

1. **Always use the appropriate role hierarchy**
2. **Regularly audit user accounts**
3. **Use Google OAuth for superadmins when possible**
4. **Keep university assignments consistent**
5. **Monitor activity logs for security**

## Support

For additional support or custom account creation needs, contact the development team or refer to the API documentation.
