const { connectFirestore, FirestoreHelpers } = require('./config/firebaseDb');
const { COLLECTIONS } = require('./models/firebase/index');

console.log('🔥 ODenta Firebase Setup');
console.log('='.repeat(50));

const setupFirebase = async () => {
  try {
    // Connect to Firebase
    console.log('📡 Connecting to Firebase...');
    await connectFirestore();
    console.log('✅ Connected to Firebase Firestore');

    // Test basic operations
    console.log('🧪 Testing Firebase operations...');
    
    // Try to read from a collection (this will test if rules allow access)
    try {
      const testData = await FirestoreHelpers.getAll(COLLECTIONS.STUDENTS);
      console.log(`✅ Successfully read from students collection (${testData.length} documents)`);
    } catch (error) {
      if (error.message.includes('PERMISSION_DENIED')) {
        console.log('⚠️  Permission denied - Firestore rules need to be updated');
        console.log('');
        console.log('📋 To fix this:');
        console.log('1. Go to: https://console.firebase.google.com/project/odenta-82359/firestore/rules');
        console.log('2. Replace the rules with:');
        console.log('');
        console.log('rules_version = \'2\';');
        console.log('service cloud.firestore {');
        console.log('  match /databases/{database}/documents {');
        console.log('    match /{document=**} {');
        console.log('      allow read, write: if true;');
        console.log('    }');
        console.log('  }');
        console.log('}');
        console.log('');
        console.log('3. Click "Publish"');
        console.log('4. Run this script again');
        return;
      } else {
        throw error;
      }
    }

    // Test write operation
    try {
      const testDoc = {
        name: 'Test Document',
        createdAt: new Date(),
        isTest: true
      };
      
      const created = await FirestoreHelpers.create('test_collection', testDoc);
      console.log('✅ Successfully created test document');
      
      // Clean up test document
      await FirestoreHelpers.delete('test_collection', created.id);
      console.log('✅ Successfully deleted test document');
      
    } catch (error) {
      console.log('❌ Write operation failed:', error.message);
      return;
    }

    console.log('');
    console.log('🎉 Firebase setup completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Run: node firebaseSeed.js (to add test accounts)');
    console.log('2. Test login with: <EMAIL> / STaiu-2025');
    console.log('3. Set up Google authentication (see FIREBASE_SETUP.md)');
    console.log('');
    console.log('🔗 Useful links:');
    console.log('   Firebase Console: https://console.firebase.google.com/project/odenta-82359');
    console.log('   Firestore Rules: https://console.firebase.google.com/project/odenta-82359/firestore/rules');
    console.log('   Authentication: https://console.firebase.google.com/project/odenta-82359/authentication');

  } catch (error) {
    console.error('❌ Firebase setup failed:', error);
    console.error('');
    console.error('💡 Troubleshooting:');
    console.error('1. Check if you have internet connection');
    console.error('2. Verify Firebase project ID is correct');
    console.error('3. Check Firestore security rules');
    console.error('4. See FIREBASE_SETUP.md for detailed instructions');
  } finally {
    console.log('🏁 Setup process finished');
    process.exit(0);
  }
};

// Run the setup
setupFirebase();
