import React from 'react';
import { motion } from 'framer-motion';

const AppointmentsWidget = ({ 
  filteredAppointments, 
  dayFilter, 
  setDayFilter, 
  hourFilter, 
  setHourFilter, 
  handleViewPatient,
  patients 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] overflow-hidden"
    >
      <div className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <h2 className="text-xl font-bold text-[#0077B6]">Appointments</h2>
          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <select
              value={dayFilter}
              onChange={(e) => setDayFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] bg-white text-sm"
            >
              <option value="today">Today</option>
              <option value="tomorrow">Tomorrow</option>
              <option value="week">This Week</option>
              <option value="all">All Appointments</option>
            </select>
            <select
              value={hourFilter}
              onChange={(e) => setHourFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] bg-white text-sm"
            >
              <option value="">All Hours</option>
              <option value="09:00">09:00 AM</option>
              <option value="10:00">10:00 AM</option>
              <option value="11:00">11:00 AM</option>
              <option value="12:00">12:00 PM</option>
              <option value="13:00">01:00 PM</option>
              <option value="14:00">02:00 PM</option>
              <option value="15:00">03:00 PM</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAppointments.length === 0 ? (
                <tr>
                  <td colSpan="4" className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      <h3 className="text-lg font-medium text-[#0077B6]">No appointments found</h3>
                      <p className="mt-1 text-[#333333]">No appointments scheduled for this period.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredAppointments.map((appt) => (
                  <motion.tr
                    key={appt._id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => {
                      // Try to find the patient in multiple ways
                      let patient = null;
                      
                      // First, try to find by patient object
                      if (appt.patient && typeof appt.patient === 'object') {
                        patient = appt.patient;
                      }
                      // Then try to find by patient ID in the patients list
                      else if (appt.patient) {
                        patient = patients.find(p => p._id === appt.patient || p.nationalId === appt.patient);
                      }
                      // If still no patient found, try to use the patient data from the appointment
                      else if (appt.patientName || appt.fullName) {
                        patient = {
                          fullName: appt.patientName || appt.fullName,
                          nationalId: appt.patient,
                          _id: appt.patient
                        };
                      }
                      
                      if (patient) {
                        handleViewPatient(patient);
                      } else {
                        console.error('No patient found for appointment:', appt);
                      }
                    }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {(() => {
                        try {
                          let date;
                          
                          // Handle different date formats from Firestore
                          if (typeof appt.date === 'string') {
                            date = new Date(appt.date);
                          } else if (appt.date && appt.date.toDate) {
                            // Firestore timestamp
                            date = appt.date.toDate();
                          } else if (appt.date instanceof Date) {
                            date = appt.date;
                          } else if (appt.date && appt.date._seconds) {
                            // Firestore timestamp object
                            date = new Date(appt.date._seconds * 1000);
                          } else {
                            console.warn('Invalid date format:', appt.date);
                            return 'Date not available';
                          }
                          
                          // Check if the date is valid
                          if (isNaN(date.getTime())) {
                            console.warn('Invalid date after parsing:', appt.date);
                            return 'Date not available';
                          }
                          
                          return date.toLocaleDateString('en-US', {
                            weekday: 'short', 
                            month: 'short', 
                            day: 'numeric'
                          });
                        } catch (error) {
                          console.error('Error formatting date:', error);
                          return 'Date not available';
                        }
                      })()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{appt.time}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{appt.patient?.fullName || appt.fullName || appt.patientName || 'Unknown'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        appt.status === 'completed' ? 'bg-green-100 text-green-800' :
                        appt.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {appt.status}
                      </span>
                    </td>
                  </motion.tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );
};

export default AppointmentsWidget;
