// This is a patch file for appointmentController.js
// Find the assignAppointment function and replace it with this updated version

// Assign a student to an appointment
const assignAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const { doctor, doctorModel } = req.body;

    if (!doctor || !doctorModel) {
      return res.status(400).json({ message: 'Doctor ID and doctor model are required' });
    }

    // Validate that the doctor exists
    let doctorDoc;
    if (doctorModel === 'Student') {
      doctorDoc = await Student.findOne({ studentId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Student not found' });
      }
    } else if (doctorModel === 'Dentist') {
      doctorDoc = await Dentist.findOne({ dentistId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Dentist not found' });
      }
    } else {
      return res.status(400).json({ message: 'Invalid doctor model' });
    }

    // Find and update the appointment
    const appointment = await Appointment.findById(id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Check if the student is already assigned to another appointment at the same time
    if (doctorModel === 'Student') {
      const existingAppointment = await Appointment.findOne({
        doctor: doctor,
        doctorModel: 'Student',
        date: appointment.date,
        time: appointment.time,
        status: { $ne: 'cancelled' },
        _id: { $ne: id } // Exclude the current appointment
      });

      if (existingAppointment) {
        return res.status(400).json({ 
          message: 'Student already has an appointment at this time',
          conflict: true
        });
      }
    }

    // Update the appointment
    appointment.doctor = doctor;
    appointment.doctorModel = doctorModel;

    // If assigning to a student, add student name information
    if (doctorModel === 'Student' && doctorDoc) {
      appointment.studentName = doctorDoc.name;
      appointment.studentId = doctorDoc.studentId;
    }

    const updatedAppointment = await appointment.save();

    // If this is a student assignment, update the student's appointments list if needed
    if (doctorModel === 'Student') {
      // Check if the student has an appointments array
      const student = await Student.findOne({ studentId: doctor });
      if (student) {
        // Check if the appointment is already in the student's list
        if (!student.appointments.includes(updatedAppointment._id)) {
          await Student.findOneAndUpdate(
            { studentId: doctor },
            { $push: { appointments: updatedAppointment._id } }
          );
          console.log(`Added appointment ${updatedAppointment._id} to student ${doctor}'s appointments list`);
        }

        // If the appointment has a patient, add the patient to the student's patients list
        if (appointment.patient) {
          // Get the patient document
          const patientDoc = await Patient.findById(appointment.patient);
          if (patientDoc) {
            // Check if the patient is already in the student's patients list
            if (!student.patients.includes(patientDoc._id)) {
              await Student.findOneAndUpdate(
                { studentId: doctor },
                { $push: { patients: patientDoc._id } }
              );
              console.log(`Added patient ${patientDoc._id} to student ${doctor}'s patients list`);
            }
          }
        }
      }
    }

    // Return the updated appointment
    const populatedAppointment = await Appointment.findById(updatedAppointment._id)
      .populate('patient', 'nationalId fullName');

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error assigning appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};
