const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Appointment validation schema for Firebase
const appointmentSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  date: Joi.date().required(),
  time: Joi.string().required(),
  type: Joi.string().required(),
  notes: Joi.string().default(''),
  status: Joi.string().valid('pending', 'completed', 'cancelled').default('pending'),
  patient: Joi.string().allow(null), // Patient ID (optional for non-existing patients)
  doctor: Joi.string().required(), // Store studentId as String
  doctorModel: Joi.string().valid('Student', 'Dentist').default('Student'),
  chiefComplaint: Joi.string().required(),
  isPatientInitiated: Joi.boolean().default(false),
  treatment: Joi.string().default(''),
  duration: Joi.number().default(120),
  university: Joi.string().required(),
  fullName: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  age: Joi.number().required(),
  nationalId: Joi.string().required(),
  occupation: Joi.string().default(''),
  address: Joi.string().default(''),
  studentName: Joi.string().allow(''), // Student name for display
  studentId: Joi.string().allow(''), // Student ID for reference
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Appointment creation schema (without ID)
const createAppointmentSchema = appointmentSchema.fork(['id'], (schema) => schema.forbidden());

// Appointment update schema (partial)
const updateAppointmentSchema = appointmentSchema.fork(
  ['date', 'time', 'type', 'chiefComplaint', 'university', 'fullName', 'phoneNumber', 'age', 'nationalId'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for Appointment operations
const AppointmentHelpers = {
  // Validate appointment data
  validateCreate: (data) => createAppointmentSchema.validate(data),
  validateUpdate: (data) => updateAppointmentSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert patient ObjectId to string
    if (transformed.patient && typeof transformed.patient === 'object') {
      transformed.patient = transformed.patient.toString();
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    ['date', 'createdAt', 'updatedAt'].forEach(field => {
      if (prepared[field] && typeof prepared[field] === 'string') {
        prepared[field] = new Date(prepared[field]);
      }
    });
    
    return prepared;
  },
  
  // Get appointments by status
  getByStatus: (appointments, status) => {
    return appointments.filter(appointment => appointment.status === status);
  },
  
  // Get appointments by date range
  getByDateRange: (appointments, startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return appointmentDate >= start && appointmentDate <= end;
    });
  },
  
  // Get appointments by university
  getByUniversity: (appointments, university) => {
    return appointments.filter(appointment => appointment.university === university);
  },
  
  // Get appointments by doctor
  getByDoctor: (appointments, doctorId) => {
    return appointments.filter(appointment => appointment.doctor === doctorId);
  },
  
  // Update appointment status
  updateStatus: (appointmentData, newStatus) => {
    const validStatuses = ['pending', 'completed', 'cancelled'];
    if (!validStatuses.includes(newStatus)) {
      throw new Error(`Invalid status: ${newStatus}`);
    }
    
    return {
      ...appointmentData,
      status: newStatus,
      updatedAt: new Date()
    };
  }
};

module.exports = {
  appointmentSchema,
  createAppointmentSchema,
  updateAppointmentSchema,
  AppointmentHelpers,
  COLLECTION_NAME: COLLECTIONS.APPOINTMENTS
};
