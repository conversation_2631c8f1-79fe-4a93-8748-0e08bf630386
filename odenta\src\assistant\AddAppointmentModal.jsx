import { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import AssignStudentModal from './AssignStudentModal';
import { FaFilter } from 'react-icons/fa';

const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AddAppointmentModal = ({ isOpen, onClose, onSuccess, universityId, patient, defaultStudentId }) => {
  const today = new Date();
  const defaultDate = today.toISOString().split('T')[0];
  const defaultTime = today.toTimeString().slice(0,5);
  const [formData, setFormData] = useState({
    date: defaultDate,
    time: defaultTime,
    type: '',
    chiefComplaint: '',
    notes: '',
    patientId: patient?.nationalId || '',
    studentId: defaultStudentId || ''
  });
  const [patients, setPatients] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { token } = useAuth();
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [assignedStudent, setAssignedStudent] = useState(null);
  const [assignSearch, setAssignSearch] = useState('');
  const [showProcedureFilter, setShowProcedureFilter] = useState(false);
  const [selectedProcedureType, setSelectedProcedureType] = useState('all');
  const [assignError, setAssignError] = useState('');

  useEffect(() => {
    if (isOpen && universityId) {
      fetchStudents();
      if (!patient) fetchPatients();
    }
    // eslint-disable-next-line
  }, [isOpen, universityId, patient]);

  useEffect(() => {
    // If patient or defaultStudentId changes, update formData
    setFormData(prev => ({
      ...prev,
      patientId: patient?.nationalId || '',
      studentId: defaultStudentId || ''
    }));
    if (defaultStudentId && students.length > 0) {
      const found = students.find(s => s.studentId === defaultStudentId);
      if (found) setAssignedStudent(found);
    }
    // eslint-disable-next-line
  }, [patient, defaultStudentId, students]);

  const fetchPatients = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/patients/university/${encodeURIComponent(universityId)}`,
        config
      );
      setPatients(response.data || []);
    } catch (err) {
      setError('Failed to load patients');
    }
  };

  const fetchStudents = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/students/university/${encodeURIComponent(universityId)}`,
        config
      );
      setStudents(response.data || []);
    } catch (err) {
      setError('Failed to load students');
    }
  };

  const handleAssignStudent = (appointmentId, student) => {
    setAssignedStudent(student);
    setFormData(prev => ({ ...prev, studentId: student.studentId }));
    setShowAssignModal(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const selectedPatient = patient || patients.find(p => p.nationalId === formData.patientId);
      const selectedStudent = assignedStudent; // Use assignedStudent directly
      if (!selectedPatient || !selectedStudent) {
        setError('Please select both patient and student');
        setLoading(false);
        return;
      }
      // Check if student already has an appointment at the same time
      try {
        const appointmentsResponse = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`,
          config
        );
        const allAppointments = appointmentsResponse.data || [];
        const conflictingAppointment = allAppointments.find(appt =>
          appt.doctor === selectedStudent.studentId &&
          appt.doctorModel === 'Student' &&
          appt.date === formData.date &&
          appt.time === formData.time &&
          appt.status !== 'cancelled'
        );
        if (conflictingAppointment) {
          setError('This student already has an appointment at the selected time. Please choose a different time or student.');
          setLoading(false);
          return;
        }
      } catch (checkErr) {
        // Continue with appointment creation if we can't check availability
      }
      const appointmentData = {
        date: formData.date,
        time: formData.time,
        type: formData.type,
        notes: formData.notes,
        patient: selectedPatient.nationalId,
        doctor: selectedStudent.studentId,
        doctorModel: 'Student',
        status: 'pending'
      };
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/appointments`,
        appointmentData,
        config
      );
      setFormData({
        date: defaultDate,
        time: defaultTime,
        type: '',
        chiefComplaint: '',
        notes: '',
        patientId: patient?.nationalId || '',
        studentId: defaultStudentId || ''
      });
      onSuccess(response.data);
      onClose();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create appointment');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold" style={{ color: websiteColorPalette.primary }}>
              Add New Appointment
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-4">
            {!patient && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Patient *
                </label>
                <select
                  name="patientId"
                  value={formData.patientId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Patient</option>
                  {patients.map((p) => (
                    <option key={p.nationalId} value={p.nationalId}>
                      {p.fullName} - {p.nationalId}
                    </option>
                  ))}
                </select>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assigned Student
              </label>
              {assignedStudent ? (
                <div className="flex items-center gap-2">
                  <span className="font-medium text-blue-700">{assignedStudent.name} ({assignedStudent.studentId})</span>
                  <button
                    type="button"
                    className="ml-2 px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 text-xs"
                    onClick={() => setShowAssignModal(true)}
                  >
                    Change
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  onClick={() => setShowAssignModal(true)}
                >
                  Assign Student
                </button>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                required
                min={defaultDate}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time *
              </label>
              <input
                type="time"
                name="time"
                value={formData.time}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Procedure Type (optional)
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Procedure</option>
                <option value="consultation">Consultation</option>
                <option value="cleaning">Cleaning</option>
                <option value="filling">Filling</option>
                <option value="extraction">Extraction</option>
                <option value="root_canal">Root Canal</option>
                <option value="crown">Crown</option>
                <option value="bridge">Bridge</option>
                <option value="implant">Implant</option>
                <option value="orthodontics">Orthodontics</option>
                <option value="surgery">Surgery</option>
                <option value="other">Other</option>
              </select>
            </div>
            {/* Remove Chief Complaint field from the form */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes..."
              />
            </div>
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !assignedStudent}
                className="flex-1 px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005a8b] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Appointment'}
              </button>
            </div>
          </form>
          {showAssignModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                  <h2 className="text-lg font-semibold text-gray-900">Assign Patient to Student</h2>
                  <button
                    onClick={() => setShowAssignModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="p-6">
                  <div className="mb-4 space-y-3">
                    <input
                      type="text"
                      placeholder="Search student by name or ID..."
                      value={assignSearch}
                      onChange={e => setAssignSearch(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    />
                    <div className="flex items-center justify-between">
                      <button
                        type="button"
                        onClick={() => setShowProcedureFilter(!showProcedureFilter)}
                        className="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                      >
                        <FaFilter className="h-3 w-3 mr-2" />
                        {showProcedureFilter ? 'Hide' : 'Show'} Procedure Requests
                      </button>
                      {showProcedureFilter && (
                        <select
                          value={selectedProcedureType}
                          onChange={e => setSelectedProcedureType(e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                        >
                          <option value="all">All Procedures</option>
                          <option value="Operative">Operative</option>
                          <option value="Endodontics">Endodontics</option>
                          <option value="Periodontics">Periodontics</option>
                          <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                          <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                          <option value="Oral Surgery">Oral Surgery</option>
                        </select>
                      )}
                    </div>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {students.length === 0 ? (
                      <div className="text-gray-500">No students found</div>
                    ) : (
                      (() => {
                        const filteredStudents = students.filter(student => {
                          if (!assignSearch.trim()) return true;
                          const search = assignSearch.trim().toLowerCase();
                          return (
                            (student.name && student.name.toLowerCase().includes(search)) ||
                            (student.studentId && student.studentId.toLowerCase().includes(search))
                          );
                        });
                        // Dummy procedureRequests for now, or fetch if needed
                        const procedureRequests = [];
                        const getStudentProcedureRequests = (studentId) => {
                          return procedureRequests.filter(request =>
                            request.studentId === studentId &&
                            request.status === 'pending' &&
                            (selectedProcedureType === 'all' || request.procedureType === selectedProcedureType)
                          );
                        };
                        return filteredStudents.map(student => {
                          const studentRequests = getStudentProcedureRequests(student.studentId);
                          return (
                            <div key={student._id} className="border-b border-gray-100">
                              <div
                                className="p-3 hover:bg-blue-50 cursor-pointer flex justify-between items-center"
                                onClick={() => {
                                  setAssignedStudent(student);
                                  setFormData(prev => ({ ...prev, studentId: student.studentId }));
                                  setShowAssignModal(false);
                                }}
                              >
                                <div className="flex-1">
                                  <span className="font-medium">{student.name}</span>
                                  <span className="text-gray-500 ml-2">({student.studentId})</span>
                                  {studentRequests.length > 0 && (
                                    <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                      {studentRequests.length} pending request{studentRequests.length > 1 ? 's' : ''}
                                    </span>
                                  )}
                                </div>
                              </div>
                              {showProcedureFilter && studentRequests.length > 0 && (
                                <div className="pl-6 pr-3 pb-3 bg-gray-50">
                                  <div className="text-xs text-gray-600 mb-2 font-medium">Pending Procedure Requests:</div>
                                  {studentRequests.map(request => (
                                    <div
                                      key={request.id || request._id}
                                      className="mb-2 p-2 bg-white rounded border border-gray-200 hover:bg-blue-50 cursor-pointer"
                                    >
                                      <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                          <div className="text-sm font-medium text-blue-700">{request.procedureType}</div>
                                          <div className="text-xs text-gray-600">
                                            Requested: {new Date(request.requestDate).toLocaleDateString()}
                                          </div>
                                          {request.notes && (
                                            <div className="text-xs text-gray-500 mt-1 italic">"{request.notes}"</div>
                                          )}
                                        </div>
                                        <div className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                          Click to assign & approve
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        });
                      })()
                    )}
                  </div>
                  {assignError && <div className="text-red-500 mt-2 text-sm">{assignError}</div>}
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default AddAppointmentModal; 
