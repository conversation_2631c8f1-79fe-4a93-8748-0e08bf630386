import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import axios from 'axios';

const Appointment = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedUniversity, setSelectedUniversity] = useState(location.state?.universityId || '');
  const [universities, setUniversities] = useState([]);
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    nationalId: '',
    age: '',
    gender: '',
    chiefComplaint: '',
    occupation: '',
    address: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch universities from DB
  useEffect(() => {
    const fetchUniversities = async () => {
      try {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`);
        const normalizedData = response.data.map((uni) => ({
          ...uni,
          displayName: typeof uni.name === 'object' ? uni.name[i18n.language] || uni.name.en || uni.universityId : uni.universityId,
          universityId: uni.universityId,
        }));
        setUniversities(normalizedData);
      } catch (error) {
        console.error('Error fetching universities:', error);
        setError(t('appointment.FailedLoadUniversities'));
      }
    };
    fetchUniversities();
  }, [t, i18n.language]);

  // Fetch available slots when university/date changes
  useEffect(() => {
    if (selectedUniversity && selectedDate) {
      const fetchAvailableSlots = async () => {
        setIsLoading(true);
        try {
          const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/university-slots`, {
            params: {
              university: selectedUniversity,
              date: selectedDate,
            },
          });
          setAvailableSlots(response.data || []);
        } catch (error) {
          console.error('Error fetching slots:', error);
          setError(
            error.response?.data?.message || t('appointment.FailedLoadSlots')
          );
        } finally {
          setIsLoading(false);
        }
      };
      fetchAvailableSlots();
    } else {
      setAvailableSlots([]);
      setSelectedSlot(null);
    }
  }, [selectedUniversity, selectedDate, t]);

  const handleUniversityChange = (e) => {
    setSelectedUniversity(e.target.value);
    setSelectedSlot(null);
    setAvailableSlots([]);
  };

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
    setSelectedSlot(null);
    setAvailableSlots([]);
  };

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const appointmentData = {
        date: selectedDate,
        time: selectedSlot.split(' - ')[0],
        type: 'checkup',
        patient: formData.nationalId,
        fullName: formData.fullName,
        phoneNumber: formData.phoneNumber,
        age: parseInt(formData.age),
        chiefComplaint: formData.chiefComplaint,
        duration: 60,
        universityClinic: selectedUniversity,
        isPatientInitiated: true,
        occupation: formData.occupation,
        address: formData.address,
      };

      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/appointments/patient`, appointmentData);

      navigate('/university-confirmation', {
        state: {
          appointmentDetails: {
            ...formData,
            timeSlot: selectedSlot,
            date: selectedDate,
            universityClinic: selectedUniversity,
            appointmentId: response.data._id,
          },
        },
      });

      setSelectedSlot(null);
      setAvailableSlots([]);
      setFormData({
        fullName: '',
        phoneNumber: '',
        nationalId: '',
        age: '',
        gender: '',
        chiefComplaint: '',
        occupation: '',
        address: '',
      });
    } catch (error) {
      console.error('Error submitting appointment:', error);
      setError(
        error.response?.data?.message || t('appointment.FailedBookAppointment')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className={`min-h-screen flex flex-col font-sans text-[#333333] bg-[#0077B6] bg-opacity-5 ${
        i18n.language === 'ar' ? 'text-right' : 'text-left'
      }`}
      style={{ fontFamily: i18n.language === 'ar' ? 'Tajawal, Arial, sans-serif' : 'inherit' }}
      dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}
    >
      <Navbar />
      <main className="flex-grow pt-16 md:pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-4 tracking-tight">
              {t('appointment.BookYourAppointment')}
            </h1>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">
              {t('appointment.SelectUniversityDateTime')}
            </p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-100 rounded-xl text-red-800">
              {error}
            </div>
          )}

          <div className="bg-white p-6 md:p-8 rounded-xl shadow-sm border border-[#20B2AA] border-opacity-30">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-[#0077B6] mb-4">
                {t('appointment.SelectUniversity')}
              </h2>
              <select
                value={selectedUniversity}
                onChange={handleUniversityChange}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300 bg-white"
                required
              >
                <option value="">{t('appointment.ChooseUniversity')}</option>
                {universities.map((uni) => (
                  <option key={uni._id} value={uni.universityId}>
                    {uni.displayName}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-semibold text-[#0077B6] mb-4">
                {t('appointment.SelectDate')}
              </h2>
              <input
                type="date"
                value={selectedDate}
                onChange={handleDateChange}
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                required
                disabled={!selectedUniversity}
              />
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-semibold text-[#0077B6] mb-4">
                {t('appointment.AvailableTimeSlots')}
              </h2>
              {isLoading ? (
                <Loader />
              ) : !selectedUniversity ? (
                <p className="text-[#333333] text-opacity-70">{t('appointment.PleaseSelectUniversity')}</p>
              ) : !selectedDate ? (
                <p className="text-[#333333] text-opacity-70">{t('appointment.PleaseSelectDate')}</p>
              ) : availableSlots.length === 0 ? (
                <p className="text-[#333333] text-opacity-70">{t('appointment.NoAvailableSlots')}</p>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {availableSlots.map((slot, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleSlotSelect(slot)}
                      className={`py-3 px-4 rounded-lg border-2 text-center transition-all duration-300 ${
                        selectedSlot === slot
                          ? 'border-[#0077B6] bg-[#0077B6] text-white'
                          : 'border-[#20B2AA] border-opacity-30 bg-[#0077B6] bg-opacity-5 text-[#0077B6] hover:bg-[#0077B6] hover:bg-opacity-10 hover:border-[#0077B6] hover:border-opacity-30'
                      }`}
                    >
                      {slot}
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div>
              <h2 className="text-xl font-semibold text-[#0077B6] mb-4">
                {t('appointment.PatientInformation')}
              </h2>

              {selectedSlot && (
                <div className="mb-4 p-3 bg-[#0077B6] bg-opacity-10 rounded-lg">
                  <p className="text-[#0077B6]">
                    {t('appointment.SelectedSlot')}: <span className="font-medium">{selectedSlot}</span>
                  </p>
                </div>
              )}

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <label htmlFor="fullName" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.FullName')} *
                    </label>
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.PhoneNumber')} *
                    </label>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label htmlFor="nationalId" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.NationalID')} *
                    </label>
                    <input
                      type="text"
                      id="nationalId"
                      name="nationalId"
                      value={formData.nationalId}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label htmlFor="age" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.Age')} *
                    </label>
                    <input
                      type="number"
                      id="age"
                      name="age"
                      min="1"
                      max="120"
                      value={formData.age}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div>
                    <label htmlFor="gender" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.Gender')} *
                    </label>
                    <select
                      id="gender"
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    >
                      <option value="">{t('appointment.ChooseGender')}</option>
                      <option value="male">{t('appointment.Male')}</option>
                      <option value="female">{t('appointment.Female')}</option>
                      <option value="other">{t('appointment.Other')}</option>
                    </select>
                  </div>
                  <div className="sm:col-span-2">
                    <label htmlFor="occupation" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.Occupation')}
                    </label>
                    <input
                      type="text"
                      id="occupation"
                      name="occupation"
                      value={formData.occupation}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div className="sm:col-span-2">
                    <label htmlFor="address" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.Address')}
                    </label>
                    <input
                      type="text"
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                    />
                  </div>
                  <div className="sm:col-span-2">
                    <label htmlFor="chiefComplaint" className="block text-sm font-medium text-[#333333] mb-1">
                      {t('appointment.ChiefComplaint')} *
                    </label>
                    <textarea
                      id="chiefComplaint"
                      name="chiefComplaint"
                      value={formData.chiefComplaint}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg shadow-sm focus:ring-[#0077B6] focus:border-[#0077B6] transition-all duration-300"
                      rows="4"
                    />
                  </div>
                </div>
                <div className="mt-8">
                  <button
                    type="submit"
                    disabled={isSubmitting || !selectedSlot}
                    className={`w-full py-3 px-4 rounded-lg text-white font-semibold transition-all duration-300 ${
                      isSubmitting || !selectedSlot
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'
                    }`}
                  >
                    {isSubmitting ? t('appointment.Submitting') : t('appointment.BookAppointment')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
      <Footer />
      <style jsx>{`
        .loader {
          border: 4px solid #f3f3f3;
          border-top: 4px solid #0077B6;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Appointment;