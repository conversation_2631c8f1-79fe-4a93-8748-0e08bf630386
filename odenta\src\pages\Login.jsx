import React, { useState, useCallback, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import Loader from '../components/Loader';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = [
  '/',
  '/student/dashboard',
  '/supervisor/dashboard',
  '/admin/dashboard',
  '/superadmin/dashboard',
  '/assistant/dashboard',
  '/forgot-password',
];

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  // Hardcoded language (set to 'ar' for Arabic)
  const language = 'en'; // Change to 'ar' for Arabic

  // Initialize Google Sign-In
  useEffect(() => {
    const initializeGoogleSignIn = () => {
      console.log('Initializing Google Sign-In...');
      console.log('Client ID:', process.env.REACT_APP_GOOGLE_CLIENT_ID);
      
      if (window.google && window.google.accounts) {
        try {
          window.google.accounts.id.initialize({
            client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
            callback: handleGoogleResponse,
            auto_select: false,
            cancel_on_tap_outside: true,
            // Use smaller popup style
            prompt_parent_id: 'google-signin-button',
            use_fedcm_for_prompt: false
          });
          console.log('Google Sign-In initialized successfully');
        } catch (error) {
          console.error('Error initializing Google Sign-In:', error);
        }
      } else {
        console.error('Google Identity Services not loaded');
      }
    };

    // Load Google Identity Services script
    if (!window.google) {
      console.log('Loading Google Identity Services script...');
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        console.log('Google Identity Services script loaded');
        initializeGoogleSignIn();
      };
      script.onerror = (error) => {
        console.error('Failed to load Google Identity Services script:', error);
      };
      document.head.appendChild(script);
    } else {
      initializeGoogleSignIn();
    }
  }, []);

  // Render Google Sign-In button when component mounts
  useEffect(() => {
    const renderGoogleButton = () => {
      if (window.google && window.google.accounts && window.google.accounts.id) {
        const googleButton = document.getElementById('google-signin-button');
        if (googleButton) {
          // Clear any existing content
          googleButton.innerHTML = '';
          
          // Render the Google Sign-In button
          window.google.accounts.id.renderButton(googleButton, {
            theme: 'outline',
            size: 'large',
            text: 'continue_with',
            shape: 'rectangular',
            width: '100%',
            // Use smaller popup style
            prompt_parent_id: 'google-signin-button',
            use_fedcm_for_prompt: false
          });
        }
      }
    };

    // Wait a bit for Google Identity Services to load
    const timer = setTimeout(renderGoogleButton, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Secure navigation handler
  const safeNavigate = useCallback((path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  }, [navigate]);

  // Handle Google Sign-In response
  const handleGoogleResponse = async (response) => {
    console.log('Google Sign-In response received');
    setGoogleLoading(true);
    setError('');

    try {
      console.log('Sending Google token to backend...');
      const result = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/google-login`, {
        token: response.credential,
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      });

      const { token, user } = result.data;
      console.log('Google login response:', result.data);
      await login(token, user);
      const redirectPath = getDashboardPath(user.role);
      safeNavigate(redirectPath);
    } catch (err) {
      console.error('Google login error:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);
      
      let errorMessage = 'Google sign-in failed. Please try again.';
      
      if (err.response?.status === 403) {
        errorMessage = 'Access denied. Your Google account is not registered in the system.';
      } else if (err.response?.status === 401) {
        errorMessage = 'Authentication failed. Please try again.';
      } else if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        errorMessage = 'Request timed out. Please check your connection and try again.';
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      }
      
      setError(
        language === 'ar'
          ? errorMessage
          : errorMessage
      );
      setGoogleLoading(false);
    }
  };

  // Handle Google Sign-In button click
  const handleGoogleSignIn = () => {
    console.log('Google Sign-In button clicked');
    if (window.google && window.google.accounts && window.google.accounts.id) {
      try {
        // Render the Google Sign-In button with smaller popup style
        const googleButton = document.getElementById('google-signin-button');
        if (googleButton) {
          // Clear any existing content
          googleButton.innerHTML = '';
          
          // Render the Google Sign-In button
          window.google.accounts.id.renderButton(googleButton, {
            theme: 'outline',
            size: 'large',
            text: 'continue_with',
            shape: 'rectangular',
            width: '100%',
            // Use smaller popup style
            prompt_parent_id: 'google-signin-button',
            use_fedcm_for_prompt: false
          });
        } else {
          // Fallback to prompt if button element not found
          window.google.accounts.id.prompt((notification) => {
            if (notification.isNotDisplayed()) {
              console.error('Google Sign-In not displayed:', notification.getNotDisplayedReason());
              setError('Google Sign-In is not available. Please try again.');
            } else if (notification.isSkippedMoment()) {
              console.log('Google Sign-In skipped:', notification.getSkippedReason());
            } else if (notification.isDismissedMoment()) {
              console.log('Google Sign-In dismissed:', notification.getDismissedReason());
            }
          });
        }
      } catch (error) {
        console.error('Error rendering Google Sign-In button:', error);
        setError('Failed to start Google Sign-In. Please try again.');
      }
    } else {
      console.error('Google Identity Services not available');
      setError('Google Sign-In is not available. Please refresh the page and try again.');
    }
  };

  // Reset Google authentication state when error occurs
  const resetGoogleAuth = () => {
    setError('');
    setGoogleLoading(false);
    // Reinitialize Google Sign-In
    if (window.google && window.google.accounts) {
      try {
        window.google.accounts.id.initialize({
          client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
          callback: handleGoogleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          // Use smaller popup style
          prompt_parent_id: 'google-signin-button',
          use_fedcm_for_prompt: false
        });
      } catch (error) {
        console.error('Error reinitializing Google Sign-In:', error);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/auth/login`, {
        email,
        password,
      });

      const { token, user } = response.data;
      console.log('Login response:', response.data);
      await login(token, user);
      const redirectPath = getDashboardPath(user.role);
      safeNavigate(redirectPath);
    } catch (err) {
      setError(
        language === 'ar'
          ? err.response?.data?.message || 'فشل تسجيل الدخول. حاول مرة أخرى.'
          : err.response?.data?.message || 'Login failed. Please try again.'
      );
      setLoading(false);
    }
  };

  const getDashboardPath = (role) => {
    switch (role) {
      case 'student':
        return '/student/dashboard';
      case 'supervisor':
        return '/supervisor/dashboard';
      case 'admin':
        return '/admin/dashboard';
      case 'superadmin':
        return '/superadmin/dashboard';
      case 'assistant':
        return '/assistant/dashboard';
      case 'dentist':
        return '/dentist/dashboard';
      default:
        return '/';
    }
  };

  // Animation variants to match Home.jsx
  const container = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading || googleLoading) {
    return <Loader />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] p-4 sm:p-6 relative overflow-hidden">
      {/* Login Card */}
      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className="bg-white bg-opacity-95 rounded-2xl shadow-xl w-full max-w-md p-4 sm:p-6 md:p-8 border border-[rgba(0,119,182,0.2)] z-10"
        style={{ direction: language === 'ar' ? 'rtl' : 'ltr' }}
      >
        <div className="flex justify-center items-center w-full py-4">
          <Link to="/">
            <img 
              src="/imgs/odenta-logo2.jpg"
              alt="ODenta Logo"
              className="h-8 sm:h-10 w-auto"
            />
          </Link>
        </div>

        <motion.h2
          variants={item}
          className="text-xl sm:text-2xl md:text-3xl font-bold text-[#0077B6] text-center mb-2"
        >
          {language === 'ar' ? 'مرحبًا بعودتك' : 'Welcome Back'}
        </motion.h2>
        <motion.p
          variants={item}
          className="text-[#333333] text-center mb-6 text-sm sm:text-base"
        >
          {language === 'ar'
            ? 'سجل الدخول للوصول إلى لوحة التحكم الخاصة بك'
            : 'Sign in to access your dashboard'}
        </motion.p>

        {error && (
          <motion.div
            variants={item}
            className="mb-6 p-3 sm:p-4 bg-red-50 text-red-700 rounded-lg text-sm border border-red-200"
          >
            <div className="flex items-start">
              <svg className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div className="flex-1">
                <p className="text-sm">{error}</p>
                {error.includes('Google') && (
                  <button
                    onClick={resetGoogleAuth}
                    className="mt-2 text-xs text-red-600 hover:text-red-800 underline"
                  >
                    Try again
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <motion.div variants={item}>
            <label
              htmlFor="email"
              className={`block text-sm font-medium text-[#333333] mb-1 ${
                language === 'ar' ? 'text-right' : 'text-left'
              }`}
            >
              {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base"
              placeholder={language === 'ar' ? '<EMAIL>' : '<EMAIL>'}
              aria-label={language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
            />
          </motion.div>

          <motion.div variants={item}>
            <label
              htmlFor="password"
              className={`block text-sm font-medium text-[#333333] mb-1 ${
                language === 'ar' ? 'text-right' : 'text-left'
              }`}
            >
              {language === 'ar' ? 'كلمة المرور' : 'Password'}
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base pr-10"
                placeholder={language === 'ar' ? '••••••••' : '••••••••'}
                aria-label={language === 'ar' ? 'كلمة المرور' : 'Password'}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-[#333333] hover:text-[#0077B6]"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <FaEyeSlash className="h-4 w-4 sm:h-5 sm:w-5" />
                ) : (
                  <FaEye className="h-4 w-4 sm:h-5 sm:w-5" />
                )}
              </button>
            </div>
          </motion.div>

          <motion.div
            variants={item}
            className={`text-sm ${language === 'ar' ? 'text-right' : 'text-right'}`}
          >
            <Link
              to="/forgot-password"
              onClick={() => safeNavigate('/forgot-password')}
              className="text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors"
              aria-label={language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?'}
            >
              {language === 'ar' ? 'هل نسيت كلمة المرور؟' : 'Forgot your password?'}
            </Link>
          </motion.div>

          <motion.button
            variants={item}
            type="submit"
            disabled={loading}
            className={`w-full bg-gradient-to-r ${
              loading
                ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed'
                : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'
            } text-white px-4 sm:px-6 py-2 sm:py-3 rounded-full font-medium transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 text-sm sm:text-base`}
            whileHover={{ scale: loading ? 1 : 1.02 }}
            whileTap={{ scale: loading ? 1 : 0.98 }}
            aria-label={
              loading
                ? language === 'ar'
                  ? 'جارٍ تسجيل الدخول...'
                  : 'Signing In...'
                : language === 'ar'
                ? 'تسجيل الدخول'
                : 'Sign In'
            }
          >
            {loading
              ? language === 'ar'
                ? 'جارٍ تسجيل الدخول...'
                : 'Signing In...'
              : language === 'ar'
              ? 'تسجيل الدخول'
              : 'Sign In'}
          </motion.button>
        </form>

        {/* Divider */}
        <motion.div variants={item} className="mt-4 sm:mt-6 mb-4 sm:mb-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">
                {language === 'ar' ? 'أو' : 'or'}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Google Sign-In Button */}
        <motion.div
          variants={item}
          className="w-full"
        >
          <div
            id="google-signin-button"
            className={`w-full flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 border border-gray-300 rounded-full shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#20B2AA] transition-all duration-300 ${
              (loading || googleLoading) ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {/* Google button will be rendered here automatically */}
            {googleLoading && (
              <div className="flex items-center">
                <FcGoogle className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3" />
                {language === 'ar' ? 'جارٍ تسجيل الدخول...' : 'Signing in...'}
              </div>
            )}
          </div>
        </motion.div>

        <motion.div variants={item} className="mt-4 sm:mt-6 text-center">
          <Link
            to="/"
            onClick={() => safeNavigate('/')}
            className="text-[#0077B6] hover:text-[#20B2AA] font-medium transition-colors flex items-center justify-center text-sm sm:text-base"
            aria-label={language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'}
          >
            <span className={language === 'ar' ? 'ml-1' : 'mr-1'}>
              {language === 'ar' ? 'العودة إلى الرئيسية' : 'Back to Home'}
            </span>
            <svg
              className={`w-3 h-3 sm:w-4 sm:h-4 ${language === 'ar' ? 'transform rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Login;