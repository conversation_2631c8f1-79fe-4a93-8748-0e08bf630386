import { useState, useEffect } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { FaPlus, FaSearch, FaCalendar, FaClock, FaTimes } from 'react-icons/fa';
import Loader from '../components/Loader';

const Appointments = () => {
  const { user, token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAppointmentDetails, setShowAppointmentDetails] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [appointments, setAppointments] = useState([]);
  const [availableSlots, setAvailableSlots] = useState([]);
  const [slotsLoading, setSlotsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { nationalId } = useParams();

  const [newAppointment, setNewAppointment] = useState({
    date: '',
    time: '',
    type: 'checkup',
    notes: '',
    status: 'pending',
    patient: nationalId,
    doctor: user?.studentId || '',
    doctorModel: 'Student',
    chiefComplaint: '',
  });

  // Log user and token for debugging
  useEffect(() => {
    console.log('User:', user, 'Token:', token, 'NationalId:', nationalId);
  }, [user, token, nationalId]);

  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        console.log('Fetching appointments for nationalId:', nationalId);
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/patient/${nationalId}`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        console.log('Appointments fetched:', response.data);
        setAppointments(response.data || []);
      } catch (err) {
        console.error('Error fetching appointments:', err.response?.data || err.message);
        setError(err.response?.data?.message || 'Failed to load appointments');
      } finally {
        setLoading(false);
      }
    };
    if (token && nationalId) fetchAppointments();
  }, [nationalId, token]);

  useEffect(() => {
    const fetchAvailableSlots = async () => {
      if (!newAppointment.date || !user?.studentId) {
        console.log('Skipping fetch: missing date or studentId', {
          date: newAppointment.date,
          studentId: user?.studentId,
        });
        return;
      }

      setSlotsLoading(true);
      try {
        const formattedDate = new Date(newAppointment.date).toISOString().split('T')[0];
        console.log('Fetching slots with params:', {
          doctorId: user.studentId,
          doctorModel: 'Student',
          date: formattedDate,
        });

        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/available-slots`, {
          params: {
            doctorId: user.studentId,
            doctorModel: 'Student',
            date: formattedDate,
          },
          headers: { Authorization: `Bearer ${token}` },
        });

        console.log('Slots fetched:', response.data);
        setAvailableSlots(response.data || []);
      } catch (err) {
        console.error('Error fetching slots:', err.response?.data || err.message);
        setError(err.response?.data?.message || 'Failed to load available slots');
        setAvailableSlots([]);
      } finally {
        setSlotsLoading(false);
      }
    };
    fetchAvailableSlots();
  }, [newAppointment.date, user?.studentId, token]);

  const validateForm = () => {
    if (!newAppointment.date) return 'Date is required';
    if (!newAppointment.time) return 'Time is required';
    if (!newAppointment.type) return 'Appointment type is required';
    if (!newAppointment.chiefComplaint) return 'Chief complaint is required';
    if (!newAppointment.doctor) return 'Doctor ID is required';
    if (!newAppointment.patient) return 'Patient ID is required';
    return '';
  };

  const handleAddAppointment = async (e) => {
    e.preventDefault();
    if (!user?.studentId) {
      setError('You must be logged in as a student to add an appointment.');
      return;
    }
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }
    try {
      console.log('Creating appointment:', newAppointment);
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/appointments`, newAppointment, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log('Appointment created:', response.data);
      setAppointments([response.data, ...appointments]);
      setNewAppointment({
        date: '',
        time: '',
        type: 'checkup',
        notes: '',
        status: 'pending',
        patient: nationalId,
        doctor: user.studentId,
        doctorModel: 'Student',
        chiefComplaint: '',
      });
      setShowAddForm(false);
      setError('');
    } catch (err) {
      console.error('Error creating appointment:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Failed to add appointment');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log('Input changed:', { name, value });
    setNewAppointment({ ...newAppointment, [name]: value });
  };

  const filteredAppointments = appointments.filter((appt) =>
    appt.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    appt.notes?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateValue) => {
    try {
      let date;
      
      // Handle different date formats from Firestore
      if (typeof dateValue === 'string') {
        date = new Date(dateValue);
      } else if (dateValue && dateValue.toDate) {
        // Firestore timestamp
        date = dateValue.toDate();
      } else if (dateValue instanceof Date) {
        date = dateValue;
      } else if (dateValue && dateValue._seconds) {
        // Firestore timestamp object
        date = new Date(dateValue._seconds * 1000);
      } else {
        console.warn('Invalid date format:', dateValue);
        return 'Date not available';
      }
      
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date after parsing:', dateValue);
        return 'Date not available';
      }
      
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date not available';
    }
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    return `${hour > 12 ? hour - 12 : hour}:${minutes} ${hour >= 12 ? 'PM' : 'AM'}`;
  };

  const deleteAppointment = async (id) => {
    try {
      console.log('Deleting appointment:', id);
      await axios.delete(`${process.env.REACT_APP_API_URL}/api/appointments/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setAppointments(appointments.filter((appt) => appt._id !== id));
      setShowAppointmentDetails(null);
    } catch (err) {
      console.error('Error deleting appointment:', err.response?.data || err.message);
      setError(err.response?.data?.message || 'Failed to delete appointment');
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (!user || !user.studentId) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100"
              >
                <div className="text-[#0077B6] mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Access Denied</h3>
                <p className="text-gray-600 mb-6">Please log in as a student to view this page.</p>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6 p-4 bg-red-100 text-[#333333] rounded-lg"
              >
                {error}
              </motion.div>
            )}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Appointments
                  </h1>
                  <p className="text-gray-600">Manage patient appointments</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowAddForm(true)}
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaPlus className="h-5 w-5 mr-2" />
                  New Appointment
                </motion.button>
              </div>
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="relative mb-6"
              >
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search appointments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:bg-white sm:text-sm transition-all duration-200"
                />
              </motion.div>
              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
              >
                {filteredAppointments.length === 0 ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="p-8 text-center"
                  >
                    <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">No appointments found</h3>
                    <p className="text-gray-500">
                      {searchTerm ? 'Try a different search term' : 'Get started by adding a new appointment'}
                    </p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowAddForm(true)}
                      className="mt-6 inline-flex items-center px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md hover:shadow-lg"
                    >
                      <FaPlus className="h-5 w-5 mr-2" />
                      Add Appointment
                    </motion.button>
                  </motion.div>
                ) : (
                  <ul className="divide-y divide-gray-200">
                    {filteredAppointments.map((appt) => (
                      <motion.li
                        key={appt._id}
                        variants={item}
                        className="hover:bg-gray-50 transition-colors cursor-pointer"
                        onClick={() => setShowAppointmentDetails(appt)}
                      >
                        <div className="px-6 py-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div
                                className={`h-3 w-3 rounded-full ${
                                  appt.status === 'completed'
                                    ? 'bg-[#28A745]'
                                    : appt.status === 'cancelled'
                                    ? 'bg-red-500'
                                    : 'bg-[#0077B6]'
                                }`}
                              ></div>
                              <h3 className="text-lg font-medium text-gray-800">{appt.type}</h3>
                            </div>
                            <div className="text-sm text-gray-500">{formatDate(appt.date)}</div>
                          </div>
                          <div className="mt-2 flex justify-between items-center">
                            <p className="text-sm text-gray-600">
                              <span className="font-medium">Time:</span> {formatTime(appt.time)}
                            </p>
                          </div>
                        </div>
                      </motion.li>
                    ))}
                  </ul>
                )}
              </motion.div>
            </motion.div>
            {showAddForm && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
                >
                  <div className="p-6 border-b flex justify-between items-center bg-[#0077B6]/10">
                    <h2 className="text-xl font-semibold text-[#0077B6]">New Appointment</h2>
                    <button
                      onClick={() => setShowAddForm(false)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <FaTimes className="h-6 w-6" />
                    </button>
                  </div>
                  <form onSubmit={handleAddAppointment} className="p-6 space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Date*</label>
                      <div className="relative">
                        <FaCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="date"
                          name="date"
                          value={newAppointment.date}
                          onChange={handleInputChange}
                          className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Time*</label>
                      <div className="relative">
                        <FaClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <select
                          name="time"
                          value={newAppointment.time}
                          onChange={handleInputChange}
                          className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                          disabled={slotsLoading}
                        >
                          <option value="">Select a time</option>
                          {slotsLoading ? (
                            <option value="" disabled>
                              Loading slots...
                            </option>
                          ) : availableSlots.length === 0 ? (
                            <option value="" disabled>
                              No available slots
                            </option>
                          ) : (
                            availableSlots.map((slot) => (
                              <option key={slot} value={slot.split(' - ')[0]}>
                                {slot}
                              </option>
                            ))
                          )}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Type*</label>
                      <select
                        name="type"
                        value={newAppointment.type}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        required
                      >
                        <option value="checkup">Checkup</option>
                        <option value="cleaning">Cleaning</option>
                        <option value="filling">Filling</option>
                        <option value="extraction">Extraction</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Chief Complaint*</label>
                      <textarea
                        name="chiefComplaint"
                        value={newAppointment.chiefComplaint}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        rows="3"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Notes (Optional)</label>
                      <textarea
                        name="notes"
                        value={newAppointment.notes}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        rows="3"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Doctor (Student ID)*</label>
                      <input
                        type="text"
                        name="doctor"
                        value={newAppointment.doctor}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        disabled
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1.5">Patient ID*</label>
                      <input
                        type="text"
                        name="patient"
                        value={newAppointment.patient}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        disabled
                        required
                      />
                    </div>
                    <div className="flex justify-end space-x-4">
                      <motion.button
                        type="button"
                        onClick={() => setShowAddForm(false)}
                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="submit"
                        className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        disabled={slotsLoading}
                      >
                        Save
                      </motion.button>
                    </div>
                  </form>
                </motion.div>
              </div>
            )}
            {showAppointmentDetails && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="bg-white rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
                >
                  <div className="p-6 border-b flex justify-between items-center bg-[#0077B6]/10">
                    <h2 className="text-xl font-semibold text-[#0077B6]">Appointment Details</h2>
                    <button
                      onClick={() => setShowAppointmentDetails(null)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <FaTimes className="h-6 w-6" />
                    </button>
                  </div>
                  <div className="p-6 space-y-6">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-gray-800">{showAppointmentDetails.type}</h3>
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium ${
                          showAppointmentDetails.status === 'completed'
                            ? 'bg-[#28A745]/20 text-[#28A745]'
                            : showAppointmentDetails.status === 'cancelled'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-[#0077B6]/20 text-[#0077B6]'
                        }`}
                      >
                        {showAppointmentDetails.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Date</p>
                        <p className="text-sm text-gray-900">{formatDate(showAppointmentDetails.date)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Time</p>
                        <p className="text-sm text-gray-900">{formatTime(showAppointmentDetails.time)}</p>
                      </div>
                    </div>
                    {showAppointmentDetails.chiefComplaint && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Chief Complaint</p>
                        <p className="text-sm text-gray-900">{showAppointmentDetails.chiefComplaint}</p>
                      </div>
                    )}
                    {showAppointmentDetails.notes && (
                      <div>
                        <p className="text-sm font-medium text-gray-500">Notes</p>
                        <p className="text-sm text-gray-900">{showAppointmentDetails.notes}</p>
                      </div>
                    )}
                    <div className="flex justify-end gap-3">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => deleteAppointment(showAppointmentDetails._id)}
                        className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full hover:from-red-600 hover:to-red-700 font-medium transition-colors shadow-md"
                      >
                        Delete
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Appointments;