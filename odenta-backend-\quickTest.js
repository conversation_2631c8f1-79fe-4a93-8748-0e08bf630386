const { ClientFirestoreHelpers, connectFirestore } = require('./config/firebaseClient');

console.log('🔥 Quick Firebase Test');

const test = async () => {
  try {
    await connectFirestore();
    
    // Test basic operation
    const testDoc = { name: 'Test', timestamp: new Date() };
    const created = await ClientFirestoreHelpers.create('test', testDoc);
    console.log('✅ Create successful:', created.id);
    
    const retrieved = await ClientFirestoreHelpers.getById('test', created.id);
    console.log('✅ Read successful:', retrieved.name);
    
    await ClientFirestoreHelpers.delete('test', created.id);
    console.log('✅ Delete successful');
    
    console.log('🎉 Firebase is working!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  process.exit(0);
};

test();
