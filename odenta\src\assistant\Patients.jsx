import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt, FaPlus, FaDownload, FaUserPlus, FaFilter, FaEdit, FaTrash, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import AddAppointmentModal from './AddAppointmentModal';
import useOptimizedData from '../hooks/useOptimizedData';

// Website color palette - Updated to match the system design
const websiteColorPalette = {
  primary: '#0077B6',      // Main blue
  secondary: '#20B2AA',    // Teal
  background: '#FFFFFF',   // White
  text: '#333333',         // Dark gray
  accent: '#28A745',       // Green
  lightBlue: '#E3F2FD',    // Light blue background
  darkBlue: '#1565C0',     // Darker blue for hover states
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827'
  }
};


// Helper to generate a TEMP-<random 8 digit number> National ID
function generateTempNationalId(existingIds = []) {
  let tempId, exists = true, attempts = 0;
  while (exists && attempts < 10) {
    tempId = `TEMP-${Math.floor(10000000 + Math.random() * 90000000)}`;
    exists = existingIds.includes(tempId);
    attempts++;
  }
  return tempId;
}

// Helper to check if a nationalId exists for a specific student in the backend
async function checkNationalIdExistsForStudent(apiUrl, token, nationalId, drId) {
  try {
    // If no student assigned or unassigned, no conflict possible
    if (!drId || drId === 'unassigned' || drId === 'N/A' || drId === '') return false;

    const config = token ? { headers: { Authorization: `Bearer ${token}` } } : {};
    // Get all patients to check for conflicts
    const res = await fetch(`${apiUrl}/api/admin/patients`, config);
    if (res.status === 200) {
      const patients = await res.json();
      // Check if any patient has the same nationalId AND drId combination
      return patients.some(patient => patient.nationalId === nationalId && patient.drId === drId);
    }
    // If error, treat as exists to be safe
    return true;
  } catch (e) {
    return true;
  }
}

const AssistantPatients = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patients, setPatients] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Students pagination state
  const [studentsPagination, setStudentsPagination] = useState({
    page: 1,
    limit: 100, // Load more students at once for dropdown
    total: 0,
    totalPages: 0
  });
  const [formData, setFormData] = useState({
    nationalId: '',
    fullName: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    drId: '',
    registrationDate: new Date().toISOString().split('T')[0], // Add registration date with today's date
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: ''
    }
  });
  const [studentSearch, setStudentSearch] = useState('');
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [assigningPatient, setAssigningPatient] = useState(null);
  const [assignSearch, setAssignSearch] = useState('');
  const [assignLoading, setAssignLoading] = useState(false);
  const [assignError, setAssignError] = useState('');
  const { user, token } = useAuth();

  // Use optimized data hook
  const {
    fetchPatients,
    fetchStudents,
    fetchProcedureRequests,
    fetchPatientDetails,
    invalidateCache
  } = useOptimizedData();

  const [showStudentAssignPopup, setShowStudentAssignPopup] = useState(false);
  const [procedureRequests, setProcedureRequests] = useState([]);
  const [showProcedureFilter, setShowProcedureFilter] = useState(false);
  const [selectedProcedureType, setSelectedProcedureType] = useState('all');
  const [showAddAppointmentModal, setShowAddAppointmentModal] = useState(false);
  const [appointmentPatient, setAppointmentPatient] = useState(null);
  const [appointmentDefaultStudentId, setAppointmentDefaultStudentId] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingPatient, setEditingPatient] = useState(null);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [deletingPatient, setDeletingPatient] = useState(null);
  const [showDeleteSuccessModal, setShowDeleteSuccessModal] = useState(false);
  const [editFormData, setEditFormData] = useState({
    nationalId: '',
    fullName: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    drId: '',
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: ''
    }
  });

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [studentFilter, setStudentFilter] = useState('all');
  const [sortBy, setSortBy] = useState('registrationDate');
  const [sortOrder, setSortOrder] = useState('desc');

  // CSV Download functionality
  const downloadCSV = () => {
    if (patients.length === 0) {
      setError('No patients to download');
      return;
    }

    const csvHeaders = [
      'Full Name',
      'National ID',
      'Phone Number',
      'Gender',
      'Age',
      'Address',
      'Occupation',
      'Assigned Student',
      'Student ID',
      'Chief Complaint',
      'Current Medications',
      'Chronic Diseases'
    ];

    const csvData = patients.map(patient => {
      const assignedStudent = students.find(s => s.studentId === patient.drId);
      return [
        patient.fullName || '',
        patient.nationalId || '',
        patient.phoneNumber || '',
        patient.gender || '',
        patient.age || '',
        patient.address || '',
        patient.occupation || '',
        assignedStudent?.name || 'Unassigned',
        patient.drId || 'Unassigned',
        patient.medicalInfo?.chiefComplaint || '',
        patient.medicalInfo?.currentMedications || '',
        Array.isArray(patient.medicalInfo?.chronicDiseases)
          ? patient.medicalInfo.chronicDiseases.join('; ')
          : ''
      ];
    });

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row =>
        row.map(field =>
          typeof field === 'string' && field.includes(',')
            ? `"${field.replace(/"/g, '""')}"`
            : field
        ).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `patients_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setSuccess('Patients data downloaded successfully!');
    setTimeout(() => setSuccess(''), 3000);
  };

  // When National ID field loses focus and is empty, generate a TEMP National ID
  const handleNationalIdBlur = () => {
    if (!formData.nationalId || formData.nationalId.trim() === '') {
      const existingIds = patients.map(p => p.nationalId);
      const tempId = generateTempNationalId(existingIds);
      setFormData(prev => ({ ...prev, nationalId: tempId }));
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  // Optimized function to fetch data with caching and pagination
  const fetchData = useCallback(async (options = {}) => {
    if (!user || !token) {
      setError('Please log in to view data.');
      setLoading(false);
      return;
    }

    try {
      setRefreshing(true);
      setError('');

      console.log('Fetching data for assistant:', user);

      // Fetch data using optimized hooks with caching
      const [patientsData, studentsData, procedureRequestsData] = await Promise.all([
        fetchPatients({
          page: pagination.page,
          limit: pagination.limit,
          search: searchTerm,
          studentFilter,
          sortBy,
          sortOrder,
          useCache: !options.forceRefresh
        }),
        fetchStudents({
          page: studentsPagination.page,
          limit: studentsPagination.limit,
          useCache: !options.forceRefresh
        }),
        fetchProcedureRequests(!options.forceRefresh)
      ]);

      console.log('Patients data:', patientsData);
      console.log('Students data:', studentsData);
      console.log('Procedure requests data:', procedureRequestsData);

      // Handle patients data (now includes pagination info)
      if (patientsData && patientsData.patients) {
        setPatients(patientsData.patients);
        setPagination(patientsData.pagination);
      } else {
        // Fallback for old API response format (no pagination)
        const patientsArray = Array.isArray(patientsData) ? patientsData : [];
        setPatients(patientsArray);
        setPagination({
          page: 1,
          limit: patientsArray.length,
          total: patientsArray.length,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false
        });
      }

      // Handle students data
      if (studentsData && studentsData.students) {
        setStudents(studentsData.students);
        setStudentsPagination(studentsData.pagination);
      } else {
        // Fallback for old API response format (no pagination)
        const studentsArray = Array.isArray(studentsData) ? studentsData : [];
        setStudents(studentsArray);
        setStudentsPagination({
          page: 1,
          limit: studentsArray.length,
          total: studentsArray.length,
          totalPages: 1
        });
      }

      setProcedureRequests(Array.isArray(procedureRequestsData) ? procedureRequestsData : []);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to fetch data');
      setLoading(false);
    } finally {
      setRefreshing(false);
    }
  }, [user, token, fetchPatients, fetchStudents, fetchProcedureRequests, pagination.page, pagination.limit, searchTerm, studentFilter, sortBy, sortOrder, studentsPagination.page, studentsPagination.limit]);

  // Initial data fetch and when filters change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }));
      } else {
        fetchData();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, studentFilter, sortBy, sortOrder]);

  // Listen for patient assignment notifications
  useEffect(() => {
    const checkPatientAssignment = () => {
      const assignedData = localStorage.getItem('patientAssigned');
      if (assignedData) {
        try {
          const data = JSON.parse(assignedData);
          const timeDiff = Date.now() - data.timestamp;
          
          // Only process if the assignment happened in the last 10 seconds
          if (timeDiff < 10000) {
            console.log('Patient assignment detected:', data);
            setSuccess(`Patient ${data.patientName} was successfully assigned to ${data.studentName}!`);
            
            // Refresh the data
            fetchData();
            
            // Clear the notification
            localStorage.removeItem('patientAssigned');
            
            // Clear success message after 5 seconds
            setTimeout(() => {
              setSuccess('');
            }, 5000);
          }
        } catch (error) {
          console.error('Error parsing patient assignment data:', error);
        }
      }
    };

    // Check immediately
    checkPatientAssignment();
    
    // Set up interval to check periodically
    const interval = setInterval(checkPatientAssignment, 2000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  // Fetch detailed patient info when a patient is selected (with caching)
  useEffect(() => {
    const fetchPatientDetailsOptimized = async () => {
      if (selectedPerson && selectedPerson.nationalId) {
        try {
          const details = await fetchPatientDetails(selectedPerson.nationalId, true);
          setPatientDetails(details);
        } catch (err) {
          console.error('Error fetching patient details:', err);
          setPatientDetails(selectedPerson);
        }
      }
    };

    fetchPatientDetailsOptimized();
  }, [selectedPerson, fetchPatientDetails]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('medical-')) {
      const medicalField = name.replace('medical-', '');
      setFormData(prev => ({
        ...prev,
        medicalInfo: { ...prev.medicalInfo, [medicalField]: value }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleChronicDiseaseChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => {
      const updatedDiseases = checked
        ? [...prev.medicalInfo.chronicDiseases, value]
        : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value);

      return {
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chronicDiseases: updatedDiseases }
      };
    });
  };

  const handleAddPatient = () => {
    setFormData({
      nationalId: '',
      fullName: '',
      phoneNumber: '',
      gender: '',
      age: '',
      address: '',
      occupation: '',
      drId: '',
      registrationDate: new Date().toISOString().split('T')[0], // Add registration date with today's date
      medicalInfo: {
        chronicDiseases: [],
        recentSurgicalProcedures: '',
        currentMedications: '',
        chiefComplaint: ''
      }
    });
    setError(''); // Clear any previous errors
    setSuccess(''); // Clear any previous success messages
    setShowAddModal(true);
  };

  const handleSubmitPatient = async (e) => {
    e.preventDefault();
    
    // Validate required fields - only name is required
    if (!formData.fullName || formData.fullName.trim() === '') {
      setError('Patient name is required');
      return;
    }
    
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      let nationalId = formData.nationalId && formData.nationalId.trim() !== ''
        ? formData.nationalId.trim()
        : null;
      // If nationalId is empty, generate a unique TEMP-<random> value
      if (!nationalId) {
        let tempId, exists = true, attempts = 0;
        while (exists && attempts < 5) {
          tempId = generateTempNationalId([]); // local check not needed, backend is source of truth
          // Show the generated value in the input for user visibility
          setFormData(prev => ({ ...prev, nationalId: tempId }));
          // Check if this nationalId exists for the same student (if student is assigned)
          exists = await checkNationalIdExistsForStudent(process.env.REACT_APP_API_URL, token, tempId, formData.drId);
          attempts++;
        }
        if (exists) {
          setError('Could not generate a unique temporary National ID for this student. Please try again.');
          return;
        }
        nationalId = tempId;
      } else {
        // If user entered a nationalId, check with backend if it exists for the same student
        const exists = await checkNationalIdExistsForStudent(process.env.REACT_APP_API_URL, token, nationalId, formData.drId);
        if (exists) {
          setError('A patient with this National ID is already assigned to this student. Please use a different National ID.');
          return;
        }
      }
      const patientData = {
        nationalId: String(nationalId),
        fullName: formData.fullName,
        phoneNumber: formData.phoneNumber || '************',
        gender: formData.gender || 'other',
        age: formData.age ? parseInt(formData.age, 10) : 18,
        address: formData.address || '',
        occupation: formData.occupation || '',
        drId: formData.drId && formData.drId.trim() !== '' ? formData.drId : 'unassigned',
        registrationDate: formData.registrationDate || new Date().toISOString().split('T')[0],
        medicalInfo: {
          chronicDiseases: formData.medicalInfo.chronicDiseases || [],
          recentSurgicalProcedures: formData.medicalInfo.recentSurgicalProcedures || '',
          currentMedications: formData.medicalInfo.currentMedications || '',
          chiefComplaint: formData.medicalInfo.chiefComplaint || ''
        }
      };

      console.log('Creating patient with data:', patientData);
      console.log('Form data validation:', {
        nationalId: formData.nationalId,
        fullName: formData.fullName,
        phoneNumber: formData.phoneNumber,
        gender: formData.gender,
        age: formData.age,
        address: formData.address,
        occupation: formData.occupation,
        drId: formData.drId
      });

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/patients`,
        patientData,
        config
      );

      console.log('Patient creation response:', response.data);

      if (response.data && response.data.patient) {
        // Find the assigned student to add student information
        const assignedStudent = students.find(s => s.studentId === response.data.patient.drId);
        const newPatient = {
          ...response.data.patient,
          studentName: assignedStudent?.name || (response.data.patient.drId === 'unassigned' ? 'Unassigned' : 'Unknown Student')
        };

        console.log('Adding new patient to state:', newPatient);
        setPatients(prevPatients => [...prevPatients, newPatient]);
        setShowAddModal(false);
        setFormData({
          nationalId: '',
          fullName: '',
          phoneNumber: '',
          gender: '',
          age: '',
          address: '',
          occupation: '',
          drId: '',
          registrationDate: new Date().toISOString().split('T')[0], // Add registration date with today's date
          medicalInfo: {
            chronicDiseases: [],
            recentSurgicalProcedures: '',
            currentMedications: '',
            chiefComplaint: ''
          }
        });
        setError(''); // Clear any previous errors
        setSuccess('Patient added successfully!');

        // Invalidate cache and refresh the data
        invalidateCache('patients');
        setTimeout(() => {
          fetchData({ forceRefresh: true });
        }, 1000);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      }
    } catch (err) {
      console.error('Error creating patient:', err);
      console.error('Error response:', err.response?.data);
      const errorMessage = err.response?.data?.message || 'Failed to create patient';
      const validationErrors = err.response?.data?.errors || [];

      // Handle specific validation errors
      if (err.response?.status === 400 && validationErrors.length > 0) {
        setError(`Validation error: ${validationErrors.join(', ')}`);
      } else if (errorMessage.includes('already exists')) {
        setError('A patient with this National ID already exists for this student. Please use a different National ID.');
      } else {
        setError(errorMessage);
      }
    }
  };

  // Assignment logic
  const openAssignModal = (patient) => {
    setAssigningPatient(patient);
    setAssignSearch('');
    setAssignError('');
    setShowAssignModal(true);
  };

  const handleEditPatient = (patient) => {
    setEditingPatient(patient);
    setEditFormData({
      nationalId: patient.nationalId || '',
      fullName: patient.fullName || '',
      phoneNumber: patient.phoneNumber || '',
      gender: patient.gender || '',
      age: patient.age ? patient.age.toString() : '',
      address: patient.address || '',
      occupation: patient.occupation || '',
      drId: patient.drId || '',
      registrationDate: patient.registrationDate || new Date().toISOString().split('T')[0],
      medicalInfo: {
        chronicDiseases: patient.medicalInfo?.chronicDiseases || [],
        recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',
        currentMedications: patient.medicalInfo?.currentMedications || '',
        chiefComplaint: patient.medicalInfo?.chiefComplaint || ''
      }
    });
    setShowEditModal(true);
  };

  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('medicalInfo.')) {
      const field = name.split('.')[1];
      setEditFormData(prev => ({
        ...prev,
        medicalInfo: {
          ...prev.medicalInfo,
          [field]: value
        }
      }));
    } else {
      setEditFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleEditChronicDiseaseChange = (e) => {
    const { value, checked } = e.target;
    setEditFormData(prev => ({
      ...prev,
      medicalInfo: {
        ...prev.medicalInfo,
        chronicDiseases: checked
          ? [...prev.medicalInfo.chronicDiseases, value]
          : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value)
      }
    }));
  };

  // Patients are now filtered and sorted server-side, so we just use them directly

  const handleSubmitEdit = async (e) => {
    e.preventDefault();
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      
      const patientData = {
        ...editFormData,
        age: editFormData.age ? parseInt(editFormData.age, 10) : 18,
      };

      console.log('Updating patient with data:', patientData);
      
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${editingPatient.nationalId}`,
        patientData,
        config
      );

      if (response.data && response.data.patient) {
        // Update the patient in the local state
        setPatients(prevPatients => 
          prevPatients.map(p => 
            p.nationalId === editingPatient.nationalId 
              ? { ...response.data.patient, studentName: p.studentName }
              : p
          )
        );
        
        setShowEditModal(false);
        setEditingPatient(null);
        setEditFormData({
          nationalId: '',
          fullName: '',
          phoneNumber: '',
          gender: '',
          age: '',
          address: '',
          occupation: '',
          drId: '',
          registrationDate: new Date().toISOString().split('T')[0],
          medicalInfo: {
            chronicDiseases: [],
            recentSurgicalProcedures: '',
            currentMedications: '',
            chiefComplaint: ''
          }
        });
        setError('');
        setSuccess('Patient updated successfully!');

        // Invalidate cache to ensure fresh data
        invalidateCache('patients');
        invalidateCache('patient', editingPatient.nationalId);

        setTimeout(() => setSuccess(''), 3000);
      }
    } catch (err) {
      console.error('Error updating patient:', err);
      setError(err.response?.data?.message || 'Failed to update patient');
    }
  };

  // Delete patient functions
  const handleDeletePatient = (patient) => {
    setDeletingPatient(patient);
    setShowDeleteConfirmModal(true);
  };

  const confirmDeletePatient = async () => {
    if (!deletingPatient) return;
    
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      
      await axios.delete(
        `${process.env.REACT_APP_API_URL}/api/patients/${deletingPatient.nationalId}`,
        config
      );

      setShowDeleteConfirmModal(false);
      setDeletingPatient(null);
      setShowDeleteSuccessModal(true);
      
      // Invalidate cache and refresh data
      invalidateCache('patients');
      fetchData({ forceRefresh: true });
      
      setTimeout(() => {
        setShowDeleteSuccessModal(false);
      }, 3000);
    } catch (err) {
      console.error('Error deleting patient:', err);
      setError(err.response?.data?.message || 'Failed to delete patient');
      setShowDeleteConfirmModal(false);
      setDeletingPatient(null);
    }
  };

  const cancelDeletePatient = () => {
    setShowDeleteConfirmModal(false);
    setDeletingPatient(null);
  };

  const handleAssignStudent = async (studentId, procedureRequestId = null) => {
    if (!assigningPatient) return;
    setAssignLoading(true);
    setAssignError('');
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };

      // Update patient with new drId
      await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${assigningPatient.nationalId}`,
        { drId: studentId },
        config
      );

      // If this assignment is related to a procedure request, approve it
      if (procedureRequestId) {
        try {
          await axios.put(
            `${process.env.REACT_APP_API_URL}/api/procedure-requests/${procedureRequestId}`,
            {
              status: 'approved',
              responseNotes: `Patient ${assigningPatient.fullName} assigned to student for requested procedure.`
            },
            config
          );
        } catch (procErr) {
          console.error('Error updating procedure request:', procErr);
          // Don't fail the assignment if procedure request update fails
        }
      }

      setShowAssignModal(false);
      setAssigningPatient(null);
      setAssignSearch('');
      setAssignLoading(false);
      setAssignError('');
      setSelectedProcedureType('all');
      setShowProcedureFilter(false);
      setSuccess(procedureRequestId ?
        'Patient assigned successfully and procedure request approved!' :
        'Patient assigned successfully!'
      );

      // Invalidate cache and refresh data
      invalidateCache('patients');
      fetchData({ forceRefresh: true });
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setAssignError(err.response?.data?.message || 'Failed to assign patient');
      setAssignLoading(false);
    }
  };



  const renderModalContent = () => {
    if (!selectedPerson || !patientDetails) return null;
    
    return (
      <div className="space-y-6">
        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
          <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
            <FaUserAlt className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
            Personal Information
          </h4>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Full Name</h4>
                <p className="text-sm text-gray-900 mt-1 font-medium">{patientDetails.fullName}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.nationalId}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Gender</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.gender}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Age</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.age}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Phone Number</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.phoneNumber}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Address</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.address}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Occupation</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.occupation}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Assigned Student</h4>
                <p className="text-sm text-gray-900 mt-1 font-medium">
                  {patientDetails.drId === 'unassigned' || !patientDetails.drId || patientDetails.drId === 'N/A'
                    ? 'Unassigned'
                    : (patientDetails.studentName || students.find((s) => s.studentId === patientDetails.drId)?.name || 'Unknown Student')
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Medical Information */}
        <div className={`bg-green-50 p-5 rounded-xl shadow-sm border border-green-100`}>
          <h4 className={`text-lg font-semibold text-[${websiteColorPalette.accent}] mb-4 flex items-center`}>
            <FaNotesMedical className={`h-5 w-5 mr-2 text-[${websiteColorPalette.accent}]`} />
            Medical Information
          </h4>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Medical History</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.medicalHistory || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Allergies</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.allergies || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Current Medications</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.currentMedications || 'None'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Chief Complaint</h4>
                <p className="text-sm text-gray-900 mt-1">{patientDetails.chiefComplaint || 'None'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Appointments */}
        {patientDetails.appointments && patientDetails.appointments.length > 0 && (
          <div className={`bg-purple-50 p-5 rounded-xl shadow-sm border border-purple-100`}>
            <h4 className={`text-lg font-semibold text-purple-600 mb-4 flex items-center`}>
              <FaCalendarAlt className="h-5 w-5 mr-2 text-purple-600" />
              Appointments
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="space-y-3">
                {patientDetails.appointments.map((appointment, index) => (
                  <div key={index} className="border-l-4 border-purple-400 pl-4 py-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {new Date(appointment.date).toLocaleDateString()} at {appointment.time}
                        </p>
                        <p className="text-sm text-gray-600">{appointment.reason || 'General consultation'}</p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {appointment.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen" style={{ backgroundColor: websiteColorPalette.gray[50] }}>
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6" style={{
          background: `linear-gradient(135deg, ${websiteColorPalette.lightBlue} 0%, ${websiteColorPalette.background} 100%)`
        }}>
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <p className="text-green-700 font-medium">{success}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-4" style={{ backgroundColor: websiteColorPalette.primary }}>
                    <FaUsers className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold" style={{ color: websiteColorPalette.primary }}>
                      Patients Management
                    </h1>
                    <p className="text-sm sm:text-base" style={{ color: websiteColorPalette.gray[600] }}>
                      View, manage, and organize all patients in your university
                    </p>
                  </div>
                </div>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
              >
                <motion.div
                  variants={item}
                  className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                  style={{ border: `1px solid ${websiteColorPalette.gray[200]}` }}
                >
                  <div className="px-6 py-4" style={{ backgroundColor: websiteColorPalette.lightBlue, borderBottom: `3px solid ${websiteColorPalette.primary}` }}>
                    <div className="flex items-center justify-center">
                      <FaUsers className="h-5 w-5 mr-3" style={{ color: websiteColorPalette.primary }} />
                      <h2 className="text-lg font-bold" style={{ color: websiteColorPalette.primary }}>
                        Patient Management Dashboard
                      </h2>
                    </div>
                  </div>
                  <div className="p-4 sm:p-6">
                    {/* Compact Layout */}
                    <div className="w-full space-y-3">
                      {/* Search and Filter Controls */}
                      <div className="rounded-xl shadow-sm p-4 mb-4" style={{
                        backgroundColor: websiteColorPalette.background,
                        border: `1px solid ${websiteColorPalette.gray[200]}`
                      }}>
                        <div className="flex flex-col sm:flex-row gap-4 items-center">
                          {/* Search Bar */}
                          <div className="flex-1 min-w-0">
                            <div className="relative">
                              <input
                                type="text"
                                placeholder="Search patients by name, ID, or phone..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 rounded-lg text-sm bg-white transition-all duration-200 focus:outline-none focus:ring-2"
                                style={{
                                  border: `1px solid ${websiteColorPalette.gray[300]}`,
                                  focusRingColor: websiteColorPalette.primary
                                }}
                              />
                              <svg className="absolute left-3 top-3.5 h-4 w-4" style={{ color: websiteColorPalette.gray[400] }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                              </svg>
                            </div>
                          </div>

                          {/* Filter Controls */}
                          <div className="flex items-center gap-3">
                            {/* Student Filter */}
                            <div className="relative">
                              <select
                                value={studentFilter}
                                onChange={(e) => setStudentFilter(e.target.value)}
                                className="px-4 py-3 rounded-lg text-sm bg-white appearance-none pr-10 min-w-[160px] focus:outline-none focus:ring-2 transition-all duration-200"
                                style={{
                                  border: `1px solid ${websiteColorPalette.gray[300]}`,
                                  focusRingColor: websiteColorPalette.primary
                                }}
                              >
                                <option value="all">All Students</option>
                                <option value="unassigned">Unassigned</option>
                                {students.map(student => (
                                  <option key={student.studentId} value={student.studentId}>
                                    {student.name}
                                  </option>
                                ))}
                              </select>
                              <FaFilter className="absolute right-3 top-3.5 h-4 w-4 pointer-events-none" style={{ color: websiteColorPalette.gray[400] }} />
                            </div>

                            {/* Sort By */}
                            <div className="relative">
                              <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                                className="px-4 py-3 rounded-lg text-sm bg-white appearance-none pr-10 min-w-[160px] focus:outline-none focus:ring-2 transition-all duration-200"
                                style={{
                                  border: `1px solid ${websiteColorPalette.gray[300]}`,
                                  focusRingColor: websiteColorPalette.primary
                                }}
                              >
                                <option value="registrationDate">Registration Date</option>
                                <option value="fullName">Name</option>
                                <option value="nationalId">National ID</option>
                              </select>
                              <svg className="absolute right-3 top-3.5 h-4 w-4 pointer-events-none" style={{ color: websiteColorPalette.gray[400] }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                              </svg>
                            </div>

                            {/* Sort Order Toggle */}
                            <button
                              onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                              className="px-4 py-3 rounded-lg text-sm bg-white flex items-center gap-2 transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2"
                              style={{
                                border: `1px solid ${websiteColorPalette.gray[300]}`,
                                color: websiteColorPalette.primary,
                                focusRingColor: websiteColorPalette.primary
                              }}
                            >
                              {sortOrder === 'desc' ? (
                                <>
                                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                                  </svg>
                                  <span className="hidden sm:inline">Newest</span>
                                </>
                              ) : (
                                <>
                                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                  </svg>
                                  <span className="hidden sm:inline">Oldest</span>
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Header and Actions */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: websiteColorPalette.lightBlue }}>
                              <FaUsers className="h-5 w-5" style={{ color: websiteColorPalette.primary }} />
                            </div>
                            <div>
                              <h2 className="text-xl font-bold" style={{ color: websiteColorPalette.primary }}>
                                All Patients
                              </h2>
                              <p className="text-sm" style={{ color: websiteColorPalette.gray[600] }}>
                                Manage patient records and assignments
                              </p>
                            </div>
                          </div>
                          <div className="px-3 py-1 rounded-full text-xs font-medium" style={{
                            backgroundColor: websiteColorPalette.lightBlue,
                            color: websiteColorPalette.primary
                          }}>
                            {patients.length} of {pagination.total} patients
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <button
                            onClick={handleAddPatient}
                            className="px-4 py-2 rounded-lg text-white font-medium flex items-center gap-2 transition-all duration-200 hover:shadow-lg transform hover:scale-105"
                            style={{ backgroundColor: websiteColorPalette.primary }}
                          >
                            <FaPlus className="h-4 w-4" />
                            <span>Add Patient</span>
                          </button>
                          <button
                            onClick={downloadCSV}
                            className="px-4 py-2 rounded-lg text-white font-medium flex items-center gap-2 transition-all duration-200 hover:shadow-lg transform hover:scale-105"
                            style={{ backgroundColor: websiteColorPalette.secondary }}
                          >
                            <FaDownload className="h-4 w-4" />
                            <span>Export CSV</span>
                          </button>
                          <button
                            onClick={() => {
                              invalidateCache('patients');
                              fetchData({ forceRefresh: true });
                            }}
                            disabled={refreshing}
                            className="px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                            style={{
                              border: `1px solid ${websiteColorPalette.gray[300]}`,
                              backgroundColor: websiteColorPalette.background,
                              color: websiteColorPalette.gray[700]
                            }}
                          >
                            <svg className={`h-4 w-4 mr-1.5 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="overflow-x-auto rounded-2xl shadow-lg" style={{ border: `1px solid ${websiteColorPalette.gray[200]}` }}>
                      <table className="min-w-full">
                        <thead style={{ backgroundColor: websiteColorPalette.primary }}>
                          <tr>
                            <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                              <div className="flex items-center gap-2">
                                <FaUserAlt className="h-4 w-4" />
                                Patient Name
                              </div>
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">National ID</th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                              <div className="flex items-center gap-2">
                                <FaCalendarAlt className="h-4 w-4" />
                                Registration Date
                              </div>
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">Assigned Student</th>
                            <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white">
                          {patients.map((person, index) => (
                            <motion.tr
                              key={person._id}
                              variants={item}
                              className="transition-all duration-200 hover:shadow-md cursor-pointer"
                              style={{
                                backgroundColor: index % 2 === 0 ? websiteColorPalette.background : websiteColorPalette.gray[50],
                                borderBottom: `1px solid ${websiteColorPalette.gray[200]}`
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = websiteColorPalette.lightBlue;
                                e.currentTarget.style.transform = 'translateY(-1px)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = index % 2 === 0 ? websiteColorPalette.background : websiteColorPalette.gray[50];
                                e.currentTarget.style.transform = 'translateY(0)';
                              }}
                            >
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold cursor-pointer transition-colors"
                                  onClick={() => setSelectedPerson(person)}
                                  style={{ color: websiteColorPalette.gray[900] }}
                                  onMouseEnter={(e) => e.target.style.color = websiteColorPalette.primary}
                                  onMouseLeave={(e) => e.target.style.color = websiteColorPalette.gray[900]}
                              >
                                <div className="flex items-center">
                                  <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3 shadow-sm"
                                       style={{ backgroundColor: websiteColorPalette.lightBlue }}>
                                    <span className="font-bold text-sm" style={{ color: websiteColorPalette.primary }}>
                                      {person.fullName.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <div className="font-semibold">{person.fullName}</div>
                                    <div className="text-xs" style={{ color: websiteColorPalette.gray[500] }}>
                                      {person.phoneNumber || 'No phone'}
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm cursor-pointer transition-colors"
                                  onClick={() => setSelectedPerson(person)}
                                  style={{ color: websiteColorPalette.gray[600] }}
                                  onMouseEnter={(e) => e.target.style.color = websiteColorPalette.primary}
                                  onMouseLeave={(e) => e.target.style.color = websiteColorPalette.gray[600]}
                              >
                                <code className="px-3 py-1 rounded-lg text-xs font-mono font-medium"
                                      style={{
                                        backgroundColor: websiteColorPalette.gray[100],
                                        color: websiteColorPalette.gray[700]
                                      }}>
                                  {person.nationalId}
                                </code>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 cursor-pointer hover:text-blue-600 transition-colors" onClick={() => setSelectedPerson(person)}>
                                <div className="flex items-center">
                                  <svg className="h-4 w-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                                  </svg>
                                  <span className="font-medium">
                                    {(() => {
                                      try {
                                        // First try to use registrationDate if available
                                        if (person.registrationDate) {
                                          const regDate = new Date(person.registrationDate);
                                          if (!isNaN(regDate.getTime())) {
                                            return regDate.toLocaleDateString('en-US', {
                                              year: 'numeric',
                                              month: 'short',
                                              day: 'numeric'
                                            });
                                          }
                                        }
                                        
                                        // Fallback to createdAt
                                        if (!person.createdAt) return 'N/A';
                                        
                                        let date;
                                        
                                        // Handle Firestore Timestamp
                                        if (person.createdAt.seconds && typeof person.createdAt.seconds === 'number') {
                                          // Create date in local timezone to avoid UTC conversion issues
                                          const timestamp = person.createdAt.seconds * 1000;
                                          date = new Date(timestamp);
                                          // Adjust for timezone to get the correct local date
                                          const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
                                          date = localDate;
                                        } else if (person.createdAt.toDate && typeof person.createdAt.toDate === 'function') {
                                          date = person.createdAt.toDate();
                                        } else if (person.createdAt instanceof Date) {
                                          date = person.createdAt;
                                        } else if (typeof person.createdAt === 'string') {
                                          date = new Date(person.createdAt);
                                        } else if (person.createdAt._seconds) {
                                          // Alternative Firestore timestamp format
                                          const timestamp = person.createdAt._seconds * 1000;
                                          date = new Date(timestamp);
                                          const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
                                          date = localDate;
                                        } else if (person.createdAt.nanoseconds) {
                                          // Another Firestore timestamp format
                                          const timestamp = person.createdAt.seconds * 1000;
                                          date = new Date(timestamp);
                                          const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
                                          date = localDate;
                                        } else {
                                          console.log('Unknown date format:', person.createdAt);
                                          return 'N/A';
                                        }
                                        
                                        if (isNaN(date.getTime())) {
                                          console.log('Invalid date:', person.createdAt);
                                          return 'N/A';
                                        }
                                        
                                        return date.toLocaleDateString('en-US', {
                                          year: 'numeric',
                                          month: 'short',
                                          day: 'numeric'
                                        });
                                      } catch (error) {
                                        console.log('Date parsing error:', error, 'for:', person.createdAt);
                                        return 'N/A';
                                      }
                                    })()}
                                  </span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                <div className="flex items-center justify-between">
                                  <span className="cursor-pointer hover:text-blue-600 transition-colors" onClick={() => setSelectedPerson(person)}>
                                    {person.studentName || students.find((s) => s.studentId === person.drId)?.name || (
                                      <span className="text-orange-600 font-medium">❌ Unassigned</span>
                                    )}
                                  </span>
                                  <div className="flex items-center gap-2">
                                    {(person.drId === 'N/A' || person.drId === 'unassigned' || !person.drId || !students.find(s => s.studentId === person.drId)) && (
                                      <button
                                        className="px-3 py-1.5 bg-gradient-to-r from-teal-500 to-emerald-500 text-white rounded-lg text-xs font-semibold shadow-sm hover:from-teal-600 hover:to-emerald-600 transition-all duration-200 flex items-center gap-1 hover:shadow-md transform hover:scale-105"
                                        onClick={e => { e.stopPropagation(); openAssignModal(person); }}
                                      >
                                        <FaUserPlus className="h-3 w-3" />
                                        Assign
                                      </button>
                                    )}
                                    <button
                                      className="px-3 py-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg text-xs font-semibold shadow-sm hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 flex items-center gap-1 hover:shadow-md transform hover:scale-105"
                                      onClick={e => {
                                        e.stopPropagation();
                                        setAppointmentPatient(person);
                                        setAppointmentDefaultStudentId(person.drId && person.drId !== 'N/A' && person.drId !== 'unassigned' ? person.drId : null);
                                        setShowAddAppointmentModal(true);
                                      }}
                                    >
                                      <FaCalendarAlt className="h-3 w-3" />
                                      Appointment
                                    </button>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                <div className="flex items-center gap-2">
                                  <button
                                    className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg text-xs font-semibold shadow-sm hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 hover:shadow-md transform hover:scale-105"
                                    onClick={e => { e.stopPropagation(); handleEditPatient(person); }}
                                    title="Edit Patient"
                                  >
                                    <FaEdit className="h-4 w-4" />
                                  </button>
                                  <button
                                    className="p-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg text-xs font-semibold shadow-sm hover:from-red-600 hover:to-pink-600 transition-all duration-200 hover:shadow-md transform hover:scale-105"
                                    onClick={e => { e.stopPropagation(); handleDeletePatient(person); }}
                                    title="Delete Patient"
                                  >
                                    <FaTrash className="h-4 w-4" />
                                  </button>
                                </div>
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Pagination Controls */}
                    {pagination.totalPages > 1 && (
                      <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4 px-6 py-4 rounded-b-2xl"
                           style={{ backgroundColor: websiteColorPalette.gray[50], borderTop: `1px solid ${websiteColorPalette.gray[200]}` }}>
                        <div className="flex items-center text-sm font-medium" style={{ color: websiteColorPalette.gray[700] }}>
                          <span>
                            Showing <span style={{ color: websiteColorPalette.primary }}>{((pagination.page - 1) * pagination.limit) + 1}</span> to <span style={{ color: websiteColorPalette.primary }}>{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of <span style={{ color: websiteColorPalette.primary }}>{pagination.total}</span> results
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                            disabled={!pagination.hasPrevPage || loading}
                            className="px-4 py-2 text-sm font-medium rounded-lg flex items-center gap-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-md"
                            style={{
                              backgroundColor: websiteColorPalette.background,
                              border: `1px solid ${websiteColorPalette.gray[300]}`,
                              color: websiteColorPalette.gray[700]
                            }}
                          >
                            <FaChevronLeft className="h-4 w-4" />
                            Previous
                          </button>

                          <div className="flex items-center space-x-1">
                            {[...Array(Math.min(5, pagination.totalPages))].map((_, index) => {
                              const pageNumber = Math.max(1, pagination.page - 2) + index;
                              if (pageNumber > pagination.totalPages) return null;

                              return (
                                <button
                                  key={pageNumber}
                                  onClick={() => setPagination(prev => ({ ...prev, page: pageNumber }))}
                                  disabled={loading}
                                  className="px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-md"
                                  style={{
                                    backgroundColor: pageNumber === pagination.page ? websiteColorPalette.primary : websiteColorPalette.background,
                                    color: pageNumber === pagination.page ? 'white' : websiteColorPalette.gray[700],
                                    border: `1px solid ${pageNumber === pagination.page ? websiteColorPalette.primary : websiteColorPalette.gray[300]}`
                                  }}
                                >
                                  {pageNumber}
                                </button>
                              );
                            })}
                          </div>

                          <button
                            onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                            disabled={!pagination.hasNextPage || loading}
                            className="px-4 py-2 text-sm font-medium rounded-lg flex items-center gap-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-md"
                            style={{
                              backgroundColor: websiteColorPalette.background,
                              border: `1px solid ${websiteColorPalette.gray[300]}`,
                              color: websiteColorPalette.gray[700]
                            }}
                          >
                            Next
                            <FaChevronRight className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Patient Details Modal */}
      {selectedPerson && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Patient Details</h2>
              <button
                onClick={() => {
                  setSelectedPerson(null);
                  setPatientDetails(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              {renderModalContent()}
              {/* Assign button in details modal if not assigned */}
              {(selectedPerson.drId === 'N/A' || selectedPerson.drId === 'unassigned' || !selectedPerson.drId || !students.find(s => s.studentId === selectedPerson.drId)) && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <button
                    className="px-6 py-3 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 flex items-center"
                    style={{ background: websiteColorPalette.secondary, filter: 'brightness(0.95)' }}
                    onClick={() => openAssignModal(selectedPerson)}
                  >
                    <FaUserPlus className="h-4 w-4 mr-2" />
                    Assign to Student
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Add Patient Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Add New Patient</h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitPatient} className="p-6 space-y-6">
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">
                Patient Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Full Name*</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">National ID</label>
                  <input
                    type="text"
                    name="nationalId"
                    value={formData.nationalId}
                    onChange={handleInputChange}
                    onBlur={handleNationalIdBlur}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter National ID (optional)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Registration Date</label>
                  <input
                    type="date"
                    name="registrationDate"
                    value={formData.registrationDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Phone Number</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter phone number (optional)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Gender</label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  >
                    <option value="">Select Gender (optional)</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Age</label>
                  <input
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter age (optional)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Address</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter address (optional)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Occupation</label>
                  <input
                    type="text"
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter occupation (optional)"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Assign to Student</label>
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                      onClick={() => setShowStudentAssignPopup(true)}
                    >
                      {formData.drId && students.find(s => s.studentId === formData.drId)
                        ? `Assigned: ${students.find(s => s.studentId === formData.drId).name}`
                        : 'Assign to Student'}
                    </button>
                    {formData.drId && (
                      <button
                        type="button"
                        className="text-xs text-red-500 underline"
                        onClick={() => setFormData(prev => ({ ...prev, drId: '' }))}
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Medical Information Section */}
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">
                Medical Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Chief Complaint</label>
                  <textarea
                    name="medical-chiefComplaint"
                    value={formData.medicalInfo.chiefComplaint}
                    onChange={handleInputChange}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Describe the patient's main complaint or reason for visit"
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Current Medications</label>
                  <textarea
                    name="medical-currentMedications"
                    value={formData.medicalInfo.currentMedications}
                    onChange={handleInputChange}
                    rows="2"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="List any current medications the patient is taking"
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Recent Surgical Procedures</label>
                  <textarea
                    name="medical-recentSurgicalProcedures"
                    value={formData.medicalInfo.recentSurgicalProcedures}
                    onChange={handleInputChange}
                    rows="2"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="List any recent surgical procedures"
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Chronic Diseases</label>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {[
                      'Diabetes',
                      'Hypertension',
                      'Heart Disease',
                      'Asthma',
                      'Arthritis',
                      'Cancer',
                      'Kidney Disease',
                      'Liver Disease',
                      'Thyroid Disease',
                      'Depression',
                      'Anxiety',
                      'Other'
                    ].map((disease) => (
                      <label key={disease} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          value={disease}
                          checked={formData.medicalInfo.chronicDiseases.includes(disease)}
                          onChange={handleChronicDiseaseChange}
                          className="rounded border-gray-300 text-[#0077B6] focus:ring-[#20B2AA]"
                        />
                        <span className="text-sm text-gray-700">{disease}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 text-white rounded-lg transition-colors hover:brightness-110"
                  style={{ backgroundColor: websiteColorPalette.primary }}
                >
                  Add Patient
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Assign Student Modal */}
      {showAssignModal && assigningPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">Assign Patient to Student</h2>
              <button
                onClick={() => setShowAssignModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              <div className="mb-4 space-y-3">
                <input
                  type="text"
                  placeholder="Search student by name or ID..."
                  value={assignSearch}
                  onChange={e => setAssignSearch(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                />

                <div className="flex items-center justify-between">
                  <button
                    type="button"
                    onClick={() => setShowProcedureFilter(!showProcedureFilter)}
                    className="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <FaFilter className="h-3 w-3 mr-2" />
                    {showProcedureFilter ? 'Hide' : 'Show'} Procedure Requests
                  </button>

                  {showProcedureFilter && (
                    <select
                      value={selectedProcedureType}
                      onChange={e => setSelectedProcedureType(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    >
                      <option value="all">All Procedures</option>
                      <option value="Operative">Operative</option>
                      <option value="Endodontics">Endodontics</option>
                      <option value="Periodontics">Periodontics</option>
                      <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                      <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                      <option value="Oral Surgery">Oral Surgery</option>
                    </select>
                  )}
                </div>
              </div>

              <div className="max-h-64 overflow-y-auto">
                {students.length === 0 ? (
                  <div className="text-gray-500">No students found</div>
                ) : (
                  (() => {
                    // Filter students based on search
                    const filteredStudents = students.filter(student => {
                      if (!assignSearch.trim()) return true;
                      const search = assignSearch.trim().toLowerCase();
                      return (
                        (student.name && student.name.toLowerCase().includes(search)) ||
                        (student.studentId && student.studentId.toLowerCase().includes(search))
                      );
                    });

                    // Get procedure requests for filtered students
                    const getStudentProcedureRequests = (studentId) => {
                      return procedureRequests.filter(request =>
                        request.studentId === studentId &&
                        request.status === 'pending' &&
                        (selectedProcedureType === 'all' || request.procedureType === selectedProcedureType)
                      );
                    };

                    return filteredStudents.map(student => {
                      const studentRequests = getStudentProcedureRequests(student.studentId);

                      return (
                        <div key={student._id} className="border-b border-gray-100">
                          {/* Student Info */}
                          <div
                            className="p-3 hover:bg-blue-50 cursor-pointer flex justify-between items-center"
                            onClick={() => handleAssignStudent(student.studentId)}
                          >
                            <div className="flex-1">
                              <span className="font-medium">{student.name}</span>
                              <span className="text-gray-500 ml-2">({student.studentId})</span>
                              {studentRequests.length > 0 && (
                                <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                  {studentRequests.length} pending request{studentRequests.length > 1 ? 's' : ''}
                                </span>
                              )}
                            </div>
                            {assignLoading && <span className="ml-2 text-xs text-blue-500">Assigning...</span>}
                          </div>

                          {/* Show procedure requests if filter is enabled */}
                          {showProcedureFilter && studentRequests.length > 0 && (
                            <div className="pl-6 pr-3 pb-3 bg-gray-50">
                              <div className="text-xs text-gray-600 mb-2 font-medium">Pending Procedure Requests:</div>
                              {studentRequests.map(request => (
                                <div
                                  key={request.id || request._id}
                                  className="mb-2 p-2 bg-white rounded border border-gray-200 hover:bg-blue-50 cursor-pointer"
                                  onClick={() => handleAssignStudent(student.studentId, request.id || request._id)}
                                >
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <div className="text-sm font-medium text-blue-700">{request.procedureType}</div>
                                      <div className="text-xs text-gray-600">
                                        Requested: {new Date(request.requestDate).toLocaleDateString()}
                                      </div>
                                      {request.notes && (
                                        <div className="text-xs text-gray-500 mt-1 italic">"{request.notes}"</div>
                                      )}
                                    </div>
                                    <div className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                      Click to assign & approve
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    });
                  })()
                )}
              </div>
              {assignError && <div className="text-red-500 mt-2 text-sm">{assignError}</div>}
            </div>
          </div>
        </div>
      )}

      {/* Student Assign Popup for Add Patient */}
      {showStudentAssignPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">Assign to Student</h2>
              <button
                onClick={() => setShowStudentAssignPopup(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6">
              <div className="mb-4 space-y-3">
                <input
                  type="text"
                  placeholder="Search student by name or ID..."
                  value={studentSearch}
                  onChange={e => setStudentSearch(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                />

                <div className="flex items-center justify-between">
                  <button
                    type="button"
                    onClick={() => setShowProcedureFilter(!showProcedureFilter)}
                    className="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                  >
                    <FaFilter className="h-3 w-3 mr-2" />
                    {showProcedureFilter ? 'Hide' : 'Show'} Procedure Requests
                  </button>

                  {showProcedureFilter && (
                    <select
                      value={selectedProcedureType}
                      onChange={e => setSelectedProcedureType(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    >
                      <option value="all">All Procedures</option>
                      <option value="Operative">Operative</option>
                      <option value="Endodontics">Endodontics</option>
                      <option value="Periodontics">Periodontics</option>
                      <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                      <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                      <option value="Oral Surgery">Oral Surgery</option>
                    </select>
                  )}
                </div>
              </div>

              <div className="max-h-64 overflow-y-auto">
                {students.length === 0 ? (
                  <div className="text-gray-500">No students found</div>
                ) : (
                  (() => {
                    // Filter students based on search
                    const filteredStudents = students.filter(student => {
                      if (!studentSearch.trim()) return true;
                      const search = studentSearch.trim().toLowerCase();
                      return (
                        (student.name && student.name.toLowerCase().includes(search)) ||
                        (student.studentId && student.studentId.toLowerCase().includes(search))
                      );
                    });

                    // Get procedure requests for filtered students
                    const getStudentProcedureRequests = (studentId) => {
                      return procedureRequests.filter(request =>
                        request.studentId === studentId &&
                        request.status === 'pending' &&
                        (selectedProcedureType === 'all' || request.procedureType === selectedProcedureType)
                      );
                    };

                    return filteredStudents.map(student => {
                      const studentRequests = getStudentProcedureRequests(student.studentId);

                      return (
                        <div key={student._id} className="border-b border-gray-100">
                          {/* Student Info */}
                          <div
                            className="p-3 hover:bg-blue-50 cursor-pointer flex justify-between items-center"
                            onClick={() => {
                              setFormData(prev => ({ ...prev, drId: student.studentId }));
                              setShowStudentAssignPopup(false);
                            }}
                          >
                            <div className="flex-1">
                              <span className="font-medium">{student.name}</span>
                              <span className="text-gray-500 ml-2">({student.studentId})</span>
                              {studentRequests.length > 0 && (
                                <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                                  {studentRequests.length} pending request{studentRequests.length > 1 ? 's' : ''}
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Show procedure requests if filter is enabled */}
                          {showProcedureFilter && studentRequests.length > 0 && (
                            <div className="pl-6 pr-3 pb-3 bg-gray-50">
                              <div className="text-xs text-gray-600 mb-2 font-medium">Pending Procedure Requests:</div>
                              {studentRequests.map(request => (
                                <div
                                  key={request.id || request._id}
                                  className="mb-2 p-2 bg-white rounded border border-gray-200 hover:bg-blue-50 cursor-pointer"
                                  onClick={() => {
                                    setFormData(prev => ({ ...prev, drId: student.studentId }));
                                    setShowStudentAssignPopup(false);
                                  }}
                                >
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <div className="text-sm font-medium text-blue-700">{request.procedureType}</div>
                                      <div className="text-xs text-gray-600">
                                        Requested: {new Date(request.requestDate).toLocaleDateString()}
                                      </div>
                                      {request.notes && (
                                        <div className="text-xs text-gray-500 mt-1 italic">"{request.notes}"</div>
                                      )}
                                    </div>
                                    <div className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                      Select for procedure
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    });
                  })()
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Patient Modal */}
      {showEditModal && editingPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">Edit Patient</h2>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmitEdit} className="p-6 space-y-6">
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">
                Patient Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Full Name*</label>
                  <input
                    type="text"
                    name="fullName"
                    value={editFormData.fullName}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">National ID</label>
                  <input
                    type="text"
                    name="nationalId"
                    value={editFormData.nationalId}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Registration Date</label>
                  <input
                    type="date"
                    name="registrationDate"
                    value={editFormData.registrationDate}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Phone Number</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={editFormData.phoneNumber}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Gender</label>
                  <select
                    name="gender"
                    value={editFormData.gender}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Age</label>
                  <input
                    type="number"
                    name="age"
                    value={editFormData.age}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter age"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Address</label>
                  <input
                    type="text"
                    name="address"
                    value={editFormData.address}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Occupation</label>
                  <input
                    type="text"
                    name="occupation"
                    value={editFormData.occupation}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    placeholder="Enter occupation"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Assigned Student</label>
                  <select
                    name="drId"
                    value={editFormData.drId}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  >
                    <option value="unassigned">Unassigned</option>
                    {students.map(student => (
                      <option key={student.studentId} value={student.studentId}>
                        {student.name} ({student.studentId})
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">
                Medical Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Chief Complaint</label>
                  <textarea
                    name="medicalInfo.chiefComplaint"
                    value={editFormData.medicalInfo.chiefComplaint}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    rows="3"
                    placeholder="Enter chief complaint"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Current Medications</label>
                  <textarea
                    name="medicalInfo.currentMedications"
                    value={editFormData.medicalInfo.currentMedications}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    rows="3"
                    placeholder="Enter current medications"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Recent Surgical Procedures</label>
                  <textarea
                    name="medicalInfo.recentSurgicalProcedures"
                    value={editFormData.medicalInfo.recentSurgicalProcedures}
                    onChange={handleEditInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    rows="3"
                    placeholder="Enter recent surgical procedures"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Chronic Diseases</label>
                  <div className="space-y-2">
                    {['Diabetes', 'Hypertension', 'Heart Disease', 'Asthma', 'Cancer', 'Other'].map(disease => (
                      <label key={disease} className="flex items-center">
                        <input
                          type="checkbox"
                          value={disease}
                          checked={editFormData.medicalInfo.chronicDiseases.includes(disease)}
                          onChange={handleEditChronicDiseaseChange}
                          className="mr-2"
                        />
                        <span className="text-sm">{disease}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Update Patient
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showAddAppointmentModal && appointmentPatient && (
        <AddAppointmentModal
          isOpen={showAddAppointmentModal}
          onClose={() => setShowAddAppointmentModal(false)}
          onSuccess={() => {
            setShowAddAppointmentModal(false);
            fetchData();
          }}
          patient={appointmentPatient}
          defaultStudentId={appointmentDefaultStudentId}
          universityId={user?.university || user?.affiliation?.id}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmModal && deletingPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <FaTrash className="h-8 w-8 text-red-500" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-gray-900">Confirm Delete</h3>
                  <p className="text-sm text-gray-500">This action cannot be undone.</p>
                </div>
              </div>
              <div className="mb-6">
                <p className="text-gray-700">
                  Are you sure you want to delete patient <span className="font-semibold">{deletingPatient.fullName}</span>?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  This will permanently remove all patient data including medical records, appointments, and treatment sheets.
                </p>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeletePatient}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeletePatient}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Delete Patient
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Success Modal */}
      {showDeleteSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-gray-900">Patient Deleted</h3>
                  <p className="text-sm text-gray-500">The patient has been successfully removed.</p>
                </div>
              </div>
              <div className="flex justify-end">
                <button
                  onClick={() => setShowDeleteSuccessModal(false)}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssistantPatients;
