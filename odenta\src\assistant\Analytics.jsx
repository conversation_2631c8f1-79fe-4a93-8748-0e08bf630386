import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { 
  FaUsers, 
  FaUserGraduate, 
  FaVenusMars, 
  FaChartBar, 
  FaChartArea, 
  FaCalendarAlt,
  FaUserAlt,
  FaNotesMedical
} from 'react-icons/fa';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import AssistantSidebar from './AssistantSidebar';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { y: 20, opacity: 0 },
  show: {
    y: 0,
    opacity: 1,
  },
};

// Chart colors
const COLORS = ['#0077B6', '#00A6ED', '#FF6B35', '#F7931E', '#7FDBFF', '#39CCCC'];

const Analytics = () => {
  const [patientsAnalytics, setPatientsAnalytics] = useState({
    total: 0,
    assigned: 0,
    unassigned: 0,
    newThisMonth: 0,
    male: 0,
    female: 0,
    ageGroups: {},
    studentDistribution: {},
    monthlyGrowth: [],
    genderDistribution: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/assistant/analytics`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setPatientsAnalytics(response.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Prepare data for charts
  const genderData = [
    { name: 'Male', value: patientsAnalytics.male, color: '#0077B6' },
    { name: 'Female', value: patientsAnalytics.female, color: '#00A6ED' }
  ];

  const ageGroupData = Object.entries(patientsAnalytics.ageGroups || {}).map(([ageGroup, count], index) => ({
    name: ageGroup,
    value: count,
    color: COLORS[index % COLORS.length]
  }));

  const studentDistributionData = Object.entries(patientsAnalytics.studentDistribution || {}).map(([student, count], index) => ({
    name: student,
    patients: count,
    color: COLORS[index % COLORS.length]
  }));

  const monthlyGrowthData = patientsAnalytics.monthlyGrowth?.map((month, index) => ({
    month: month.month,
    patients: month.count,
    color: COLORS[index % COLORS.length]
  })) || [];

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Analytics
                  </h1>
                  <p className="text-gray-600">View insights and statistics for patient management</p>
                </div>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="space-y-6"
              >
                {/* Patient Analytics Card */}
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-4 px-6 text-center font-medium text-sm text-[#0077B6] border-b-2 border-[#0077B6]">
                      Patient Analytics
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                      <h2 className="text-xl font-bold text-[#0077B6] flex items-center">
                        <FaUsers className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Patient Overview
                      </h2>
                    </div>

                    {/* Analytics Cards */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                      <div className="p-4 rounded-lg bg-[rgba(0,119,182,0.1)] border border-[rgba(0,119,182,0.2)]">
                        <p className="text-sm font-medium text-gray-600">Total Patients</p>
                        <p className="text-2xl font-bold text-[#0077B6]">{patientsAnalytics.total}</p>
                      </div>
                      <div className="p-4 rounded-lg bg-[rgba(0,119,182,0.1)] border border-[rgba(0,119,182,0.2)]">
                        <p className="text-sm font-medium text-gray-600">Assigned</p>
                        <p className="text-2xl font-bold text-[#0077B6]">{patientsAnalytics.assigned}</p>
                      </div>
                      <div className="p-4 rounded-lg bg-[rgba(0,119,182,0.1)] border border-[rgba(0,119,182,0.2)]">
                        <p className="text-sm font-medium text-gray-600">Unassigned</p>
                        <p className="text-2xl font-bold text-[#0077B6]">{patientsAnalytics.unassigned}</p>
                      </div>
                      <div className="p-4 rounded-lg bg-[rgba(0,119,182,0.1)] border border-[rgba(0,119,182,0.2)]">
                        <p className="text-sm font-medium text-gray-600">New This Month</p>
                        <p className="text-2xl font-bold text-[#0077B6]">{patientsAnalytics.newThisMonth}</p>
                      </div>
                    </div>

                    {/* Charts Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                      {/* Gender Distribution Chart */}
                      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h3 className="text-lg font-medium mb-4 flex items-center text-[#0077B6]">
                          <FaVenusMars className="h-5 w-5 mr-2 text-[#0077B6]" />
                          Gender Distribution
                        </h3>
                        <ResponsiveContainer width="100%" height={200}>
                          <PieChart>
                            <Pie
                              data={genderData}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {genderData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>

                      {/* Age Distribution Chart */}
                      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h3 className="text-lg font-medium mb-4 flex items-center text-[#0077B6]">
                          <FaChartBar className="h-5 w-5 mr-2 text-[#0077B6]" />
                          Age Distribution
                        </h3>
                        <ResponsiveContainer width="100%" height={200}>
                          <BarChart data={ageGroupData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="value" fill="#0077B6" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </div>

                    {/* Monthly Growth Chart */}
                    <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 mb-8">
                      <h3 className="text-lg font-medium mb-4 flex items-center text-[#0077B6]">
                        <FaChartArea className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Monthly Growth
                      </h3>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={monthlyGrowthData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Area type="monotone" dataKey="patients" stroke="#0077B6" fill="#0077B6" fillOpacity={0.3} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </motion.div>

                {/* Student Analytics Card */}
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-4 px-6 text-center font-medium text-sm text-[#0077B6] border-b-2 border-[#0077B6]">
                      Student Analytics
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                      <h2 className="text-xl font-bold text-[#0077B6] flex items-center">
                        <FaUserGraduate className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Student Performance
                      </h2>
                    </div>

                    {/* Student Distribution Chart */}
                    <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                      <h3 className="text-lg font-medium mb-4 flex items-center text-[#0077B6]">
                        <FaChartBar className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Patient Assignment by Student
                      </h3>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={studentDistributionData} layout="horizontal">
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="name" type="category" width={100} />
                          <Tooltip />
                          <Bar dataKey="patients" fill="#0077B6" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Analytics;
