const mongoose = require('mongoose');
require('dotenv').config();

async function updateIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/dentlyzer');
    console.log('Connected to MongoDB');

    // Get the Dentist collection
    const db = mongoose.connection.db;
    const dentistCollection = db.collection('dentists');
    
    // Drop the existing email index
    await dentistCollection.dropIndex('email_1');
    console.log('Dropped the unique email index');
    
    // Create a new non-unique index on email for better query performance
    await dentistCollection.createIndex({ email: 1 }, { unique: false, background: true });
    console.log('Created new non-unique email index');

    // Get all indexes to verify
    const indexes = await dentistCollection.indexes();
    console.log('Current indexes on dentists collection:', JSON.stringify(indexes, null, 2));

    // Close the connection
    await mongoose.connection.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

updateIndexes();
