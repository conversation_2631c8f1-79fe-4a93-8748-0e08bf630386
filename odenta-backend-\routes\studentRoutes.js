const express = require('express');
const router = express.Router();
const { getMyPatients, sendMessage, getAnalytics, getAllStudents, getStudentsByUniversity } = require('../controllers/studentController');
const { submitReview } = require('../controllers/reviewController'); // Import submitReview
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Public route - no authentication required
router.get('/', getAllStudents);

// Protected routes
router.get('/patients', auth, role('student'), getMyPatients);
router.post('/message', auth, role('student'), (req, res) => 
  sendMessage(req.app.get('io'))(req, res));
router.get('/analytics', auth, role('student'), getAnalytics);

// Get students by university (for assistants)
router.get('/university/:university', auth, role('assistant', 'admin', 'superadmin'), getStudentsByUniversity);

// New route for submitting a teeth chart
router.post('/teeth-chart', auth, role('student'), async (req, res) => {
  req.body.studentId = req.user.id; // Automatically set studentId from auth
  await submitReview(req, res); // Reuse submitReview
});

module.exports = router;