const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { PatientHelpers } = require('../models/firebase/Patient');
const { StudentHelpers } = require('../models/firebase/Student');
const { ProcedureRequestHelpers, createProcedureRequestSchema, updateProcedureRequestSchema } = require('../models/firebase/ProcedureRequest');

// Helper function to parse various date formats
const parseDate = (dateField) => {
  if (!dateField) return new Date(0);
  
  try {
    // If it's already a Date object
    if (dateField instanceof Date) {
      return dateField;
    }
    
    // If it's a Firestore timestamp with toDate method
    if (dateField && typeof dateField.toDate === 'function') {
      return dateField.toDate();
    }
    
    // If it's a Firestore timestamp object with _seconds
    if (dateField && typeof dateField === 'object' && dateField._seconds) {
      return new Date(dateField._seconds * 1000);
    }
    
    // If it's a string, try to parse it
    if (typeof dateField === 'string') {
      return new Date(dateField);
    }
    
    // If it's a number (timestamp)
    if (typeof dateField === 'number') {
      return new Date(dateField);
    }
    
    return new Date(0);
  } catch (error) {
    console.error('Error parsing date for sorting:', error);
    return new Date(0);
  }
};

// Validation schema for creating a procedure request
const procedureRequestValidationSchema = Joi.object({
  procedureType: Joi.string().valid(
    'Periodontics',
    'Endodontics',
    'Oral Surgery',
    'Fixed Prosthodontics',
    'Removable Prosthodontics',
    'Operative'
  ).required(),
  patientNationalId: Joi.string().allow(''),
  patientName: Joi.string().allow(''),
  notes: Joi.string().allow(''),
});

// Validation schema for updating a procedure request
const updateProcedureRequestValidationSchema = Joi.object({
  status: Joi.string().valid('approved', 'rejected').required(),
  responseNotes: Joi.string().allow(''),
});

// Create a new procedure request
const createProcedureRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = procedureRequestValidationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { procedureType, patientNationalId, patientName, notes } = req.body;

    // Find the student using Firebase
    const students = await FirestoreHelpers.getByField(COLLECTIONS.STUDENTS, 'studentId', req.user.studentId);
    if (!students || students.length === 0) {
      return res.status(404).json({ message: 'Student not found' });
    }
    const student = students[0];

    // If patientNationalId is provided, verify the patient exists
    let patientFullName = patientName || '';
    if (patientNationalId) {
      const patients = await FirestoreHelpers.getByField(COLLECTIONS.PATIENTS, 'nationalId', patientNationalId);
      if (patients && patients.length > 0) {
        patientFullName = patients[0].fullName;
      }
    }

    // Create the procedure request data
    const procedureRequestData = {
      studentId: req.user.studentId,
      studentName: req.user.name,
      procedureType,
      patientNationalId: patientNationalId || '',
      patientName: patientFullName,
      notes: notes || '',
      status: 'pending',
      requestDate: new Date()
    };

    // Validate and prepare data for Firestore
    const { error: validationError } = createProcedureRequestSchema.validate(procedureRequestData);
    if (validationError) {
      return res.status(400).json({ message: validationError.details[0].message });
    }

    const preparedData = ProcedureRequestHelpers.prepareForFirestore(procedureRequestData);
    const procedureRequest = await FirestoreHelpers.create(COLLECTIONS.PROCEDURE_REQUESTS, preparedData);

    res.status(201).json({
      message: 'Procedure request created successfully',
      procedureRequest: ProcedureRequestHelpers.formatForResponse(procedureRequest)
    });
  } catch (error) {
    console.error('Error creating procedure request:', error);
    res.status(500).json({
      message: 'Server error while creating procedure request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all procedure requests for a student
const getStudentProcedureRequests = async (req, res) => {
  try {
    const procedureRequests = await FirestoreHelpers.getByField(
      COLLECTIONS.PROCEDURE_REQUESTS,
      'studentId',
      req.user.studentId
    );

    // Sort by requestDate descending
    const sortedRequests = procedureRequests.sort((a, b) => {
      const dateA = parseDate(a.requestDate);
      const dateB = parseDate(b.requestDate);
      return dateB - dateA;
    });

    const formattedRequests = sortedRequests.map(request =>
      ProcedureRequestHelpers.formatForResponse(request)
    );

    res.json(formattedRequests);
  } catch (error) {
    console.error('Error fetching student procedure requests:', error);
    res.status(500).json({
      message: 'Server error while fetching procedure requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all procedure requests (for assistants)
const getAllProcedureRequests = async (req, res) => {
  try {
    const procedureRequests = await FirestoreHelpers.getAll(COLLECTIONS.PROCEDURE_REQUESTS);

    // Sort by requestDate descending
    const sortedRequests = procedureRequests.sort((a, b) => {
      const dateA = parseDate(a.requestDate);
      const dateB = parseDate(b.requestDate);
      return dateB - dateA;
    });

    const formattedRequests = sortedRequests.map(request =>
      ProcedureRequestHelpers.formatForResponse(request)
    );

    res.json(formattedRequests);
  } catch (error) {
    console.error('Error fetching all procedure requests:', error);
    res.status(500).json({
      message: 'Server error while fetching procedure requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update a procedure request (approve/reject)
const updateProcedureRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = updateProcedureRequestValidationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { status, responseNotes } = req.body;
    const { requestId } = req.params;

    console.log('Updating procedure request:', { requestId, status, responseNotes });

    // Get the procedure request from Firebase
    const procedureRequest = await FirestoreHelpers.findById(COLLECTIONS.PROCEDURE_REQUESTS, requestId);
    if (!procedureRequest) {
      return res.status(404).json({ message: 'Procedure request not found' });
    }

    console.log('Found procedure request:', procedureRequest);

    // Prepare update data
    const updateData = {
      status,
      responseNotes: responseNotes || '',
      responseDate: new Date(),
      responderId: req.user.id,
      responderName: req.user.name
    };

    console.log('Update data:', updateData);

    // Update the procedure request in Firebase
    const updatedProcedureRequest = await FirestoreHelpers.update(
      COLLECTIONS.PROCEDURE_REQUESTS,
      requestId,
      updateData
    );

    console.log('Updated procedure request:', updatedProcedureRequest);

    // Format the response
    const formattedRequest = ProcedureRequestHelpers.formatForResponse(updatedProcedureRequest);
    console.log('Formatted request:', formattedRequest);

    res.json({
      message: `Procedure request ${status}`,
      procedureRequest: formattedRequest
    });
  } catch (error) {
    console.error('Error updating procedure request:', error);
    res.status(500).json({
      message: 'Server error while updating procedure request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createProcedureRequest,
  getStudentProcedureRequests,
  getAllProcedureRequests,
  updateProcedureRequest
};
