rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function getUserId() {
      return request.auth.uid;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return getUserRole() == 'admin';
    }
    
    function isSupervisor() {
      return getUserRole() == 'supervisor';
    }
    
    function isStudent() {
      return getUserRole() == 'student';
    }
    
    function isAssistant() {
      return getUserRole() == 'assistant';
    }
    
    function isSuperadmin() {
      return getUserRole() == 'superadmin';
    }
    
    // Universities collection
    match /universities/{universityId} {
      allow read: if isAuthenticated();
      allow write: if isSuperadmin() || isAdmin();
    }
    
    // Students collection
    match /students/{studentId} {
      allow read: if isAuthenticated();
      allow write: if isSuperadmin() || isAdmin() || isSupervisor() || isOwner(studentId);
    }
    
    // Supervisors collection
    match /supervisors/{supervisorId} {
      allow read: if isAuthenticated();
      allow write: if isSuperadmin() || isAdmin() || isOwner(supervisorId);
    }
    
    // Admins collection
    match /admins/{adminId} {
      allow read: if isAuthenticated();
      allow write: if isSuperadmin() || isOwner(adminId);
    }
    
    // Assistants collection
    match /assistants/{assistantId} {
      allow read: if isAuthenticated();
      allow write: if isSuperadmin() || isAdmin() || isOwner(assistantId);
    }
    
    // Patients collection
    match /patients/{patientId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated(); // Students, assistants, admins can manage patients
    }
    
    // Appointments collection
    match /appointments/{appointmentId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated(); // All authenticated users can manage appointments
    }
    
    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if isAuthenticated();
      allow write: if isStudent() || isSupervisor() || isAdmin();
    }
    
    // Teeth Charts collection
    match /teethCharts/{chartId} {
      allow read: if isAuthenticated();
      allow write: if isStudent() || isSupervisor() || isAdmin();
    }
    
    // Lab Requests collection
    match /labRequests/{requestId} {
      allow read: if isAuthenticated();
      allow write: if isStudent() || isAssistant() || isAdmin();
    }
    
    // Procedure Requests collection
    match /procedureRequests/{requestId} {
      allow read: if isAuthenticated();
      allow write: if isStudent() || isAssistant() || isAdmin();
    }
    
    // News collection
    match /news/{newsId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isSuperadmin();
    }
    
    // Activity Logs collection
    match /activityLogs/{logId} {
      allow read: if isAdmin() || isSuperadmin();
      allow write: if isAuthenticated(); // All users can create logs
    }
    
    // Payments collection
    match /payments/{paymentId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isSuperadmin();
    }
    
    // Configs collection (for superadmin data)
    match /configs/{configId} {
      allow read: if isSuperadmin();
      allow write: if isSuperadmin();
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
