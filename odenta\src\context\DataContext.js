import React, { createContext, useContext, useReducer, useCallback } from 'react';
import useOptimizedData from '../hooks/useOptimizedData';
import { useAuth } from './AuthContext';

// Data context for global state management with caching
const DataContext = createContext();

// Action types
const DATA_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_PATIENTS: 'SET_PATIENTS',
  SET_STUDENTS: 'SET_STUDENTS',
  SET_PROCEDURE_REQUESTS: 'SET_PROCEDURE_REQUESTS',
  SET_PATIENT_DETAILS: 'SET_PATIENT_DETAILS',
  CLEAR_DATA: 'CLEAR_DATA'
};

// Initial state
const initialState = {
  loading: false,
  error: null,
  patients: {
    data: [],
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false
    },
    lastFetch: null
  },
  students: {
    data: [],
    pagination: {
      page: 1,
      limit: 50,
      total: 0,
      totalPages: 0
    },
    lastFetch: null
  },
  procedureRequests: {
    data: [],
    lastFetch: null
  },
  patientDetails: new Map() // Cache patient details by nationalId
};

// Reducer function
function dataReducer(state, action) {
  switch (action.type) {
    case DATA_ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case DATA_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    case DATA_ACTIONS.SET_PATIENTS:
      return {
        ...state,
        patients: {
          ...action.payload,
          lastFetch: Date.now()
        },
        loading: false,
        error: null
      };
    
    case DATA_ACTIONS.SET_STUDENTS:
      return {
        ...state,
        students: {
          ...action.payload,
          lastFetch: Date.now()
        },
        loading: false,
        error: null
      };
    
    case DATA_ACTIONS.SET_PROCEDURE_REQUESTS:
      return {
        ...state,
        procedureRequests: {
          data: action.payload,
          lastFetch: Date.now()
        },
        loading: false,
        error: null
      };
    
    case DATA_ACTIONS.SET_PATIENT_DETAILS:
      const newPatientDetails = new Map(state.patientDetails);
      newPatientDetails.set(action.payload.nationalId, {
        data: action.payload.data,
        lastFetch: Date.now()
      });
      return {
        ...state,
        patientDetails: newPatientDetails,
        loading: false,
        error: null
      };
    
    case DATA_ACTIONS.CLEAR_DATA:
      return initialState;
    
    default:
      return state;
  }
}

// Data provider component
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);
  const { user, token } = useAuth();
  const {
    fetchPatients,
    fetchStudents,
    fetchProcedureRequests,
    fetchPatientDetails,
    invalidateCache
  } = useOptimizedData();

  // Check if data is stale (older than 5 minutes)
  const isDataStale = useCallback((lastFetch, maxAge = 5 * 60 * 1000) => {
    return !lastFetch || (Date.now() - lastFetch) > maxAge;
  }, []);

  // Get patients with caching and pagination
  const getPatients = useCallback(async (options = {}) => {
    const {
      page = 1,
      limit = 20,
      search = '',
      studentFilter = 'all',
      sortBy = 'registrationDate',
      sortOrder = 'desc',
      forceRefresh = false
    } = options;

    // Check if we have fresh data and same parameters
    const currentParams = { page, limit, search, studentFilter, sortBy, sortOrder };
    const isSameParams = JSON.stringify(currentParams) === JSON.stringify(state.patients.lastParams);
    
    if (!forceRefresh && isSameParams && !isDataStale(state.patients.lastFetch) && state.patients.data.length > 0) {
      return state.patients;
    }

    try {
      dispatch({ type: DATA_ACTIONS.SET_LOADING, payload: true });
      
      const result = await fetchPatients({
        ...options,
        useCache: !forceRefresh
      });

      const patientsData = {
        data: result.patients || result,
        pagination: result.pagination || {
          page: 1,
          limit: (result.patients || result).length,
          total: (result.patients || result).length,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false
        },
        lastParams: currentParams
      };

      dispatch({ type: DATA_ACTIONS.SET_PATIENTS, payload: patientsData });
      return patientsData;
    } catch (error) {
      dispatch({ type: DATA_ACTIONS.SET_ERROR, payload: error.message });
      throw error;
    }
  }, [state.patients, fetchPatients, isDataStale]);

  // Get students with caching
  const getStudents = useCallback(async (options = {}) => {
    const {
      page = 1,
      limit = 50,
      search = '',
      forceRefresh = false
    } = options;

    if (!forceRefresh && !isDataStale(state.students.lastFetch) && state.students.data.length > 0) {
      return state.students;
    }

    try {
      dispatch({ type: DATA_ACTIONS.SET_LOADING, payload: true });
      
      const result = await fetchStudents({
        ...options,
        useCache: !forceRefresh
      });

      const studentsData = {
        data: result.students || result,
        pagination: result.pagination || {
          page: 1,
          limit: (result.students || result).length,
          total: (result.students || result).length,
          totalPages: 1
        }
      };

      dispatch({ type: DATA_ACTIONS.SET_STUDENTS, payload: studentsData });
      return studentsData;
    } catch (error) {
      dispatch({ type: DATA_ACTIONS.SET_ERROR, payload: error.message });
      throw error;
    }
  }, [state.students, fetchStudents, isDataStale]);

  // Get procedure requests with caching
  const getProcedureRequests = useCallback(async (forceRefresh = false) => {
    if (!forceRefresh && !isDataStale(state.procedureRequests.lastFetch) && state.procedureRequests.data.length > 0) {
      return state.procedureRequests.data;
    }

    try {
      dispatch({ type: DATA_ACTIONS.SET_LOADING, payload: true });
      
      const result = await fetchProcedureRequests(!forceRefresh);
      
      dispatch({ type: DATA_ACTIONS.SET_PROCEDURE_REQUESTS, payload: result });
      return result;
    } catch (error) {
      dispatch({ type: DATA_ACTIONS.SET_ERROR, payload: error.message });
      throw error;
    }
  }, [state.procedureRequests, fetchProcedureRequests, isDataStale]);

  // Get patient details with caching
  const getPatientDetails = useCallback(async (nationalId, forceRefresh = false) => {
    const cached = state.patientDetails.get(nationalId);
    
    if (!forceRefresh && cached && !isDataStale(cached.lastFetch)) {
      return cached.data;
    }

    try {
      dispatch({ type: DATA_ACTIONS.SET_LOADING, payload: true });
      
      const result = await fetchPatientDetails(nationalId, !forceRefresh);
      
      dispatch({ 
        type: DATA_ACTIONS.SET_PATIENT_DETAILS, 
        payload: { nationalId, data: result }
      });
      
      return result;
    } catch (error) {
      dispatch({ type: DATA_ACTIONS.SET_ERROR, payload: error.message });
      throw error;
    }
  }, [state.patientDetails, fetchPatientDetails, isDataStale]);

  // Clear all data (useful for logout)
  const clearData = useCallback(() => {
    dispatch({ type: DATA_ACTIONS.CLEAR_DATA });
    invalidateCache('all');
  }, [invalidateCache]);

  // Invalidate specific data types
  const invalidateData = useCallback((type, ...args) => {
    switch (type) {
      case 'patients':
        dispatch({ 
          type: DATA_ACTIONS.SET_PATIENTS, 
          payload: { ...initialState.patients }
        });
        break;
      case 'students':
        dispatch({ 
          type: DATA_ACTIONS.SET_STUDENTS, 
          payload: { ...initialState.students }
        });
        break;
      case 'procedure-requests':
        dispatch({ 
          type: DATA_ACTIONS.SET_PROCEDURE_REQUESTS, 
          payload: []
        });
        break;
      case 'patient-details':
        if (args[0]) {
          const newPatientDetails = new Map(state.patientDetails);
          newPatientDetails.delete(args[0]);
          dispatch({
            type: DATA_ACTIONS.SET_PATIENT_DETAILS,
            payload: { nationalId: args[0], data: null }
          });
        }
        break;
      case 'all':
        clearData();
        break;
    }
    
    invalidateCache(type, ...args);
  }, [state.patientDetails, invalidateCache, clearData]);

  const value = {
    // State
    ...state,
    
    // Actions
    getPatients,
    getStudents,
    getProcedureRequests,
    getPatientDetails,
    clearData,
    invalidateData,
    
    // Utilities
    isDataStale
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the data context
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
