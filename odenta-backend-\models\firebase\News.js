const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// News validation schema for Firebase
const newsSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  title: Joi.string().required(),
  content: Joi.string().required(),
  author: Joi.string().required(),
  authorId: Joi.string().required(),
  category: Joi.string().valid('announcement', 'update', 'event', 'general').default('general'),
  tags: Joi.array().items(Joi.string()).default([]),
  isPublished: Joi.boolean().default(true),
  publishedAt: Joi.date().default(() => new Date()),
  imageUrl: Joi.string().allow(''),
  university: Joi.string().required(),
  priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
  expiresAt: Joi.date().optional(),
  views: Joi.number().default(0),
  likes: Joi.array().items(Joi.string()).default([]), // Array of user IDs who liked
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// News creation schema (without ID)
const createNewsSchema = newsSchema.fork(['id'], (schema) => schema.forbidden());

// News update schema (partial)
const updateNewsSchema = newsSchema.fork(
  ['title', 'content', 'author', 'authorId', 'university'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for news operations
const NewsHelpers = {
  // Validate news data
  validateCreate: (data) => {
    return createNewsSchema.validate(data);
  },
  
  validateUpdate: (data) => {
    return updateNewsSchema.validate(data);
  },
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.publishedAt && typeof prepared.publishedAt === 'string') {
      prepared.publishedAt = new Date(prepared.publishedAt);
    }
    if (prepared.expiresAt && typeof prepared.expiresAt === 'string') {
      prepared.expiresAt = new Date(prepared.expiresAt);
    }
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    return prepared;
  },
  
  // Format for client response
  formatForResponse: (newsData) => {
    return {
      ...newsData,
      publishedAt: newsData.publishedAt?.toISOString(),
      expiresAt: newsData.expiresAt?.toISOString(),
      createdAt: newsData.createdAt?.toISOString(),
      updatedAt: newsData.updatedAt?.toISOString()
    };
  },
  
  // Check if news is expired
  isExpired: (newsData) => {
    if (!newsData.expiresAt) return false;
    const expiryDate = newsData.expiresAt.toDate ? newsData.expiresAt.toDate() : new Date(newsData.expiresAt);
    return expiryDate < new Date();
  },
  
  // Increment view count
  incrementViews: async (newsId) => {
    const { FirestoreHelpers } = require('../../config/firebaseDb');
    const news = await FirestoreHelpers.findById(COLLECTIONS.NEWS, newsId);
    if (news) {
      await FirestoreHelpers.update(COLLECTIONS.NEWS, newsId, {
        views: (news.views || 0) + 1,
        updatedAt: new Date()
      });
    }
  },
  
  // Toggle like
  toggleLike: async (newsId, userId) => {
    const { FirestoreHelpers } = require('../../config/firebaseDb');
    const news = await FirestoreHelpers.findById(COLLECTIONS.NEWS, newsId);
    if (news) {
      const likes = news.likes || [];
      const userIndex = likes.indexOf(userId);
      
      if (userIndex > -1) {
        // Remove like
        likes.splice(userIndex, 1);
      } else {
        // Add like
        likes.push(userId);
      }
      
      await FirestoreHelpers.update(COLLECTIONS.NEWS, newsId, {
        likes,
        updatedAt: new Date()
      });
      
      return { liked: userIndex === -1, totalLikes: likes.length };
    }
    return null;
  }
};

module.exports = {
  newsSchema,
  createNewsSchema,
  updateNewsSchema,
  NewsHelpers,
  COLLECTION_NAME: COLLECTIONS.NEWS
};
