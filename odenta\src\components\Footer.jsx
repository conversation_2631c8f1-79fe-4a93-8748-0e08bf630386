import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaTooth } from 'react-icons/fa';

// Sanitize email input
const sanitizeEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) ? email : null;
};

// Valid social media URLs
const SOCIAL_MEDIA_LINKS = [
  { href: 'https://facebook.com/odenta', platform: 'Facebook' },
  { href: 'https://instagram.com/odenta', platform: 'Instagram' },
  { href: 'https://linkedin.com/company/odenta', platform: 'LinkedIn' },
  { href: 'https://youtube.com/odenta', platform: 'YouTube' },
];

const Footer = () => {
  const { t, i18n } = useTranslation();

  // Handle newsletter subscription
  const handleSubscribe = useCallback((e) => {
    e.preventDefault();
    const email = e.target.elements.email.value;
    const sanitizedEmail = sanitizeEmail(email);
    if (sanitizedEmail) {
      // Replace with actual API call
      console.log('Subscribing:', sanitizedEmail);
      e.target.reset();
    } else {
      console.warn('Invalid email:', email);
    }
  }, []);

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  return (
    <motion.footer
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={`bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white py-16 px-6 border-t border-gray-100 ${
        i18n.language === 'ar' ? 'text-right' : 'text-left'
      }`}
    >
      <div className="max-w-7xl mx-auto">
        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 ${
            i18n.language === 'ar' ? 'rtl' : 'ltr'
          }`}
        >
          {/* Company Info */}
          <motion.div variants={item} className="flex flex-col">
            <div className={`flex items-center mb-6 ${i18n.language === 'ar' ? 'flex-row-reverse' : ''}`}>
              {/* <motion.div
                className={`w-12 h-12 flex items-center justify-center ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'}`}
                whileHover={{ rotate: 10 }}
              >
                <FaTooth className="w-10 h-10 text-[#0077B6]" />
              </motion.div>
              <h2 className="text-2xl font-bold text-[#0077B6]">
                DENT<span className="text-[#20B2AA]">LYZER</span>
              </h2> */}
        <Link to="/">
          <img 
            src="/imgs/odenta-logo2.jpg" // Update this path based on your project structure
            alt="ODenta Logo"
            className="h-10 w-auto" // Adjust size as needed
          />
        </Link>
            </div>
            <p className="text-[#333333] text-lg mb-6">{t('footer.CompanyDescription')}</p>
            <div className={`flex ${i18n.language === 'ar' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              {SOCIAL_MEDIA_LINKS.map(({ href, platform }, index) => (
                <motion.a
                  key={index}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-500 hover:text-[#0077B6] transition-colors duration-300`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={t(`footer.SocialMedia.${platform}`)}
                >
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    {platform === 'Facebook' && (
                      <path
                        fillRule="evenodd"
                        d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                        clipRule="evenodd"
                      />
                    )}
                    {platform === 'Instagram' && (
                      <path
                        fillRule="evenodd"
                        d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                        clipRule="evenodd"
                      />
                    )}
                    {platform === 'LinkedIn' && (
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                    )}
                    {platform === 'YouTube' && (
                      <path
                        fillRule="evenodd"
                        d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                        clipRule="evenodd"
                      />
                    )}
                  </svg>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={item} className="flex flex-col">
            <h3 className="text-xl font-bold text-[#0077B6] mb-6">{t('footer.QuickLinks')}</h3>
            <div className="space-y-4">
              {['Home', 'About Us', 'Contact'].map((link, index) => (
                <motion.div key={index} whileHover={{ x: i18n.language === 'ar' ? -5 : 5 }}>
                  <Link
                    to={['/', '/about', '/contact'][index]}
                    className="text-[#333333] hover:text-[#0077B6] text-lg relative group"
                  >
                    {t(`footer.Links.${link.replace(/\s/g, '')}`)}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </motion.div>
              ))}
              <div>
                <span className="text-[#333333] text-lg font-medium">{t('footer.Links.Services')}</span>
                <div className="mt-2 space-y-2 pl-4">
                  <motion.div whileHover={{ x: i18n.language === 'ar' ? -5 : 5 }}>
                    <Link
                      to="/universityServices"
                      className="text-[#333333] hover:text-[#0077B6] text-lg relative group"
                    >
                      {t('footer.Links.UniversityServices')}
                      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={item} className="flex flex-col">
            <h3 className="text-xl font-bold text-[#0077B6] mb-6">{t('footer.ContactUs')}</h3>
            <div className="space-y-4 text-[#333333] text-lg">
              {[
                {
                  icon: (
                    <svg
                      className={`w-5 h-5 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  ),
                  text: t('footer.Address'),
                },
                {
                  icon: (
                    <svg
                      className={`w-5 h-5 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  ),
                  text: `${t('footer.Phone')}: +20`,
                },
                {
                  icon: (
                    <svg
                      className={`w-5 h-5 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  ),
                  text: `${t('footer.Email')}: <EMAIL>`,
                },
                {
                  icon: (
                    <svg
                      className={`w-5 h-5 ${i18n.language === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  ),
                  text: t('footer.WorkingHours'),
                },
              ].map((contact, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center ${i18n.language === 'ar' ? 'flex-row-reverse' : ''}`}
                  whileHover={{ scale: 1.02 }}
                >
                  {contact.icon}
                  <span>{contact.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Newsletter */}
          <motion.div variants={item} className="flex flex-col">
            <h3 className="text-xl font-bold text-[#0077B6] mb-6">{t('footer.Newsletter')}</h3>
            <p className="text-[#333333] text-lg mb-6">{t('footer.SubscribeDescription')}</p>
            <form onSubmit={handleSubscribe} className="flex flex-col space-y-4">
              <motion.input
                type="email"
                name="email"
                placeholder={t('footer.YourEmail')}
                className="px-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:border-transparent bg-white shadow-sm"
                required
                whileFocus={{ scale: 1.02 }}
                aria-label={t('footer.YourEmail')}
              />
              <motion.button
                type="submit"
                className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                aria-label={t('footer.Subscribe')}
              >
                {t('footer.Subscribe')}
              </motion.button>
            </form>
          </motion.div>
        </motion.div>

        {/* Copyright */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className={`mt-16 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center ${
            i18n.language === 'ar' ? 'rtl' : 'ltr'
          }`}
        >
          <div className="text-gray-500 text-sm mb-4 md:mb-0">
            <p>© {new Date().getFullYear()} ODenta. {t('footer.AllRightsReserved')}</p>
          </div>
          <div className={`flex ${i18n.language === 'ar' ? 'space-x-reverse space-x-6' : 'space-x-6'}`}>
            {['Privacy Policy', 'Terms of Service', 'Cookie Policy'].map((policy, index) => (
              <motion.div key={index} whileHover={{ y: -2 }}>
                <Link
                  to={['/privacy', '/terms', '/cookies'][index]}
                  className="text-gray-500 hover:text-[#0077B6] text-sm relative group"
                >
                  {t(`footer.Policies.${policy.replace(/\s/g, '')}`)}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default Footer;
