const { initializeFirebase, getFirestore } = require('./firebase');
const config = require('./config');

let db;

const connectFirestore = async () => {
  try {
    // Initialize Firebase
    initializeFirebase();
    
    // Get Firestore instance
    db = getFirestore();
    
    console.log(`✅ Connected to Firebase Firestore - Project: odenta-82359`);
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
    
    return db;
  } catch (error) {
    console.error('❌ Firebase Firestore connection error:', error.message);
    process.exit(1);
  }
};

// Get the database instance
const getDb = () => {
  if (!db) {
    throw new Error('Database not initialized. Call connectFirestore() first.');
  }
  return db;
};

// Helper functions for common Firestore operations
const FirestoreHelpers = {
  // Create a document
  async create(collection, data, docId = null) {
    const db = getDb();
    const collectionRef = db.collection(collection);
    
    if (docId) {
      await collectionRef.doc(docId).set(data);
      return { id: docId, ...data };
    } else {
      const docRef = await collectionRef.add(data);
      return { id: docRef.id, ...data };
    }
  },

  // Find a document by ID
  async findById(collection, id) {
    const db = getDb();
    const doc = await db.collection(collection).doc(id).get();
    
    if (!doc.exists) {
      return null;
    }
    
    return { id: doc.id, ...doc.data() };
  },

  // Find documents with query
  async find(collection, whereClause = null, orderBy = null, limit = null) {
    const db = getDb();
    let query = db.collection(collection);
    
    // Handle multiple where clauses
    if (whereClause) {
      if (Array.isArray(whereClause)) {
        // Multiple where clauses
        whereClause.forEach(clause => {
          if (clause && clause.field && clause.operator && clause.value !== undefined) {
            query = query.where(clause.field, clause.operator, clause.value);
          }
        });
      } else if (typeof whereClause === 'object' && whereClause.field && whereClause.operator && whereClause.value !== undefined) {
        // Single where clause
        query = query.where(whereClause.field, whereClause.operator, whereClause.value);
      }
    }
    
    if (orderBy) {
      query = query.orderBy(orderBy.field, orderBy.direction || 'asc');
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  },

  // Find one document
  async findOne(collection, whereClause) {
    const results = await this.find(collection, whereClause, null, 1);
    return results.length > 0 ? results[0] : null;
  },

  // Get documents by field value
  async getByField(collection, field, value) {
    const db = getDb();
    const snapshot = await db.collection(collection).where(field, '==', value).get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  },

  // Get all documents in a collection
  async getAll(collection) {
    const db = getDb();
    const snapshot = await db.collection(collection).get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  },

  // Update a document
  async update(collection, id, data) {
    const db = getDb();
    await db.collection(collection).doc(id).update(data);
    
    // Fetch the updated document to return complete data
    const updatedDoc = await db.collection(collection).doc(id).get();
    if (!updatedDoc.exists) {
      throw new Error('Document not found after update');
    }
    
    return { id: updatedDoc.id, ...updatedDoc.data() };
  },

  // Delete a document
  async delete(collection, id) {
    const db = getDb();
    await db.collection(collection).doc(id).delete();
    return { id };
  },

  // Count documents
  async count(collection, whereClause = null) {
    const db = getDb();
    let query = db.collection(collection);
    
    if (whereClause) {
      query = query.where(whereClause.field, whereClause.operator, whereClause.value);
    }
    
    const snapshot = await query.get();
    return snapshot.size;
  },

  // Batch operations
  async batchWrite(operations) {
    const db = getDb();
    const batch = db.batch();
    
    operations.forEach(op => {
      const docRef = db.collection(op.collection).doc(op.id);
      
      switch (op.type) {
        case 'set':
          batch.set(docRef, op.data);
          break;
        case 'update':
          batch.update(docRef, op.data);
          break;
        case 'delete':
          batch.delete(docRef);
          break;
      }
    });
    
    await batch.commit();
  },

  // Transaction
  async runTransaction(updateFunction) {
    const db = getDb();
    return await db.runTransaction(updateFunction);
  },

  // Additional methods for compatibility
  async createDocument(collection, data) {
    return await this.create(collection, data);
  },

  async getDocument(collection, id) {
    return await this.findById(collection, id);
  },

  async getDocuments(collection, whereClause = null) {
    return await this.find(collection, whereClause);
  },

  async getAllDocuments(collection) {
    return await this.getAll(collection);
  },

  async updateDocument(collection, id, data) {
    return await this.update(collection, id, data);
  },

  async deleteDocument(collection, id) {
    return await this.delete(collection, id);
  },

  async countDocuments(collection, whereClause = null) {
    return await this.count(collection, whereClause);
  },

  async getDocumentById(collection, id) {
    return await this.findById(collection, id);
  },

  async addDocument(collection, data) {
    return await this.create(collection, data);
  },

  // Collection reference for direct access
  collection(collectionName) {
    const db = getDb();
    return db.collection(collectionName);
  },

  // Aggregate-like functionality (simplified for Firestore)
  async aggregate(collection, pipeline) {
    // Firestore doesn't have aggregation pipeline like MongoDB
    // This is a simplified implementation
    const db = getDb();
    let query = db.collection(collection);
    
    // Apply basic filtering if present
    for (const stage of pipeline) {
      if (stage.$match) {
        for (const [field, value] of Object.entries(stage.$match)) {
          if (typeof value === 'object' && value.$gte) {
            query = query.where(field, '>=', value.$gte);
          } else if (typeof value === 'object' && value.$lte) {
            query = query.where(field, '<=', value.$lte);
          } else {
            query = query.where(field, '==', value);
          }
        }
      }
    }
    
    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }
};

module.exports = {
  connectFirestore,
  getDb,
  FirestoreHelpers
};
