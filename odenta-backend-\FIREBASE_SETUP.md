# Firebase Setup Guide for ODenta Backend

## Current Status
✅ Server is running successfully on port 5000
✅ All API routes enabled (auth, patients, students, supervisors, admin, appointments, reviews, dental, analytics, universities, accounts, lab-requests, procedure-requests, news)
⚠️ Firebase authentication needs proper credentials for full functionality
🔥 Project ID: odenta-82359

## Firebase Authentication Setup

### Option 1: Service Account Key (Recommended for Production)

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com/
   - Select your project: `odenta-82359`

2. **Generate Service Account Key**
   - Go to Project Settings (gear icon) → Service Accounts
   - Click "Generate new private key"
   - Download the JSON file

3. **Add to Environment Variables**
   - Copy the entire JSON content
   - Add to your `.env` file:
   ```env
   FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"odenta-82359",...}
   ```

### Option 2: Individual Environment Variables

Add the following to your `.env` file:

```env
# Firebase Configuration (optional - for production with service account)
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"odenta-82359",...}

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here

# Other existing environment variables
NODE_ENV=development
PORT=5000
JWT_SECRET=your_jwt_secret
JWT_ACCESS_EXPIRATION=24h
FRONTEND_URL=http://localhost:3000
MAX_FILE_SIZE=50mb
UPLOAD_PATH=./uploads
```

## Firebase Setup Steps

### 1. Install Dependencies
```bash
npm install firebase-admin
```

### 2. Initialize Firebase (Development)
For development, the app will use the default Firebase configuration. Make sure you have:
- Firebase project ID: `odenta-82359`
- Proper Firebase rules configured

### 3. Run Migration
```bash
npm run migrate:firebase
```

This will:
- Initialize Firebase connection
- Create sample data including:
  - 1 University (AIU)
  - 1 Admin user
  - 1 Student user  
  - 1 Supervisor user

### 4. Test Login Credentials
After migration, you can test with these credentials:
- **Admin**: <EMAIL> / admin123
- **Student**: <EMAIL> / student123
- **Supervisor**: <EMAIL> / supervisor123

### 5. Google OAuth Superadmins
The following accounts can sign in using Google OAuth:
- **Superadmin**: <EMAIL> (Google Sign-In)
- **Superadmin**: <EMAIL> (Google Sign-In)

## Firebase Collections Structure

The following Firestore collections will be created:

- `patients` - Patient records
- `students` - Student accounts
- `supervisors` - Supervisor accounts
- `admins` - Admin accounts
- `assistants` - Assistant accounts
- `universities` - University information
- `appointments` - Appointment records
- `reviews` - Review submissions
- `teethCharts` - Dental charts
- `labRequests` - Lab request records
- `procedureRequests` - Procedure requests
- `news` - News articles
- `activityLogs` - System activity logs
- `payments` - Payment records
- `configs` - System configurations

## Key Changes from MongoDB

1. **Document IDs**: Firestore uses auto-generated IDs instead of ObjectIds
2. **References**: String IDs instead of ObjectId references
3. **Queries**: Different query syntax using field/operator/value structure
4. **Transactions**: Firebase transaction syntax
5. **Validation**: Joi schemas for data validation

## Production Setup

For production deployment:

1. Generate a Firebase service account key
2. Add the service account JSON to `FIREBASE_SERVICE_ACCOUNT_KEY` environment variable
3. Ensure proper Firebase security rules are configured
4. Set up proper indexing for queries

## Troubleshooting

### Common Issues:

1. **Permission Denied**: Check Firebase security rules
2. **Connection Failed**: Verify project ID and credentials
3. **Validation Errors**: Check data format against Joi schemas

### Debug Mode:
Set `NODE_ENV=development` to see detailed error messages.

## Migration Notes

- All existing MongoDB operations have been converted to Firestore operations
- Password hashing remains the same (bcryptjs)
- File uploads still use Cloudinary
- Socket.io functionality preserved
- JWT authentication unchanged

## Next Steps

1. Test all API endpoints
2. Verify data integrity
3. Update frontend API calls if needed
4. Configure production Firebase rules
5. Set up monitoring and logging
