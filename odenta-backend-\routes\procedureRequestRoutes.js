const express = require('express');
const router = express.Router();
const {
  createProcedureRequest,
  getStudentProcedureRequests,
  getAllProcedureRequests,
  updateProcedureRequest
} = require('../controllers/procedureRequestController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Student routes
router.post('/', auth, role('student'), createProcedureRequest);
router.get('/student', auth, role('student'), getStudentProcedureRequests);

// Assistant routes
router.get('/', auth, role('assistant'), getAllProcedureRequests);
router.put('/:requestId', auth, role('assistant'), updateProcedureRequest);

module.exports = router;
