const express = require('express');
const router = express.Router();
const universityController = require('../controllers/universityController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Routes
router.get('/', universityController.getAllUniversities);
router.get('/:id', universityController.getUniversityById);
router.post('/', auth, role('superadmin'), universityController.createUniversity);
router.put('/:id', auth, role('superadmin'), universityController.updateUniversity);
router.delete('/:id', auth, role('superadmin'), universityController.deleteUniversity);

module.exports = router;