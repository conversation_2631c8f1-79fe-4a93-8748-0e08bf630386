import React, { useState, useEffect, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import './Home.css'; // Import the CSS file
import {
  FaTooth,
  FaXRay,
  FaUniversity,
  FaChartLine,
  FaCalendarAlt,
} from 'react-icons/fa';
import { RiAiGenerate } from 'react-icons/ri';
import { MdHealthAndSafety, MdOutlineSupervisorAccount } from 'react-icons/md';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = [
  '/universityServices',
  '/universities',
  '/try-ai',
];

// Memoized FeatureCard component to optimize rendering
const FeatureCard = memo(({ feature, index, navigate }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    viewport={{ once: true }}
    className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-[#20B2AA] group"
  >
    <div className="bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mb-6 group-hover:bg-[rgba(32,178,170,0.15)] transition-colors duration-300">
      {feature.icon}
    </div>
    <h3 className="text-xl font-bold text-[#0077B6] mb-3">{feature.title}</h3>
    <p className="text-[#333333] mb-4">{feature.desc}</p>
    {feature.cta && (
      <button
        onClick={() => navigate(feature.link)}
        className="text-[#20B2AA] hover:text-[#0077B6] font-medium flex items-center"
        aria-label={feature.cta}
      >
        {feature.cta} →
      </button>
    )}
  </motion.div>
));

// Memoized AIItem component
const AIItem = memo(({ item, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ delay: index * 0.1 }}
    viewport={{ once: true }}
    className="flex items-start"
  >
    <div className="bg-[rgba(32,178,170,0.15)] rounded-full p-2 mr-4 flex-shrink-0">
      {item.icon}
    </div>
    <div>
      <h4 className="font-bold text-[#0077B6]">{item.title}</h4>
      <p className="text-[#333333]">{item.desc}</p>
    </div>
  </motion.div>
));

const Home = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Secure navigation handler
  const safeNavigate = (path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 2500);
    return () => clearTimeout(timer); // Prevent memory leaks
  }, []);

  if (isLoading) return <Loader />;

  return (
    <div className="font-sans text-gray-800 bg-white min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-radial overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-32 flex flex-col lg:flex-row items-center relative z-10">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:w-1/2 mb-16 lg:mb-0 lg:pr-16"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#0077B6] mb-6 leading-tight">
              {t('hero.revolutionizing')}{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
                {t('hero.dentalCareWithAI')}
              </span>
            </h1>
            <p className="text-xl text-[#333333] mb-8 max-w-lg">{t('hero.description')}</p>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => safeNavigate('/universityServices')}
                className="bg-gradient-to-r from-[#0077B6] to-[#0099CC] text-white px-8 py-4 rounded-full font-medium hover:shadow-xl transition-all duration-300 shadow-lg flex items-center"
                aria-label={t('hero.forUniversities')}
              >
                <FaUniversity className="mr-2" />
                {t('hero.forUniversities')}
              </motion.button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:w-1/2 relative flex justify-end"
          >
            <div className="hero-image-container">
              {/* Decorative elements */}
              <motion.div
                className="absolute w-6 h-6 bg-[#0077B6] rounded-full -top-4 -right-4 z-10"
                animate={{
                  y: [0, -15, 0],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div
                className="absolute w-4 h-4 bg-[#20B2AA] rounded-full bottom-4 -left-4 z-10"
                animate={{
                  y: [0, 10, 0],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              />

              {/* Background circles */}
              <div className="hero-circle hero-outer-circle" />
              <div className="hero-circle hero-inner-circle" />

              {/* Hero image */}
              <motion.div
                className="relative z-0"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <motion.img
                  src="/imgs/hero_3.jpg"
                  alt={t('hero.dentalCareWithAI')}
                  className="w-full rounded-[50%] hero-image-oval"
                  loading="lazy"
                  initial={{ rotate: -5 }}
                  animate={{
                    rotate: 0,
                    scale: [0.95, 1]
                  }}
                  transition={{
                    duration: 1.2,
                    ease: "easeOut"
                  }}
                />


              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('features.comprehensiveDental')}{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
                {t('features.management')}
              </span>
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">{t('features.description')}</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <FaXRay className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.aiXray'),
                desc: t('features.aiXrayDesc'),
              },
              {
                icon: <FaUniversity className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.studentTools'),
                desc: t('features.studentToolsDesc'),
                cta: t('features.exploreUniversity'),
                link: '/universityServices',
              },
              {
                icon: <FaTooth className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.digitalCharting'),
                desc: t('features.digitalChartingDesc'),
              },
              {
                icon: <MdOutlineSupervisorAccount className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.supervisorReview'),
                desc: t('features.supervisorReviewDesc'),
              },
              {
                icon: <FaChartLine className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.analytics'),
                desc: t('features.analyticsDesc'),
              },
              {
                icon: <FaCalendarAlt className="h-6 w-6 text-[#0077B6]" />,
                title: t('features.appointments'),
                desc: t('features.appointmentsDesc'),
              },
            ].map((feature, index) => (
              <FeatureCard
                key={index}
                feature={feature}
                index={index}
                navigate={safeNavigate}
              />
            ))}
          </div>
        </div>
      </section>

      {/* AI Technology Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col lg:flex-row items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2 mb-12 lg:mb-0 lg:pr-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-6">
                {t('ai.poweredBy')}{' '}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
                  {t('ai.advancedAI')}
                </span>
              </h2>
              <p className="text-xl text-[#333333] mb-8">{t('ai.description')}</p>

              <div className="space-y-6">
                {[
                  {
                    icon: <RiAiGenerate className="h-5 w-5 text-[#0077B6]" />,
                    title: t('ai.realTimeAnalysis'),
                    desc: t('ai.realTimeAnalysisDesc'),
                  },
                  {
                    icon: <MdHealthAndSafety className="h-5 w-5 text-[#0077B6]" />,
                    title: t('ai.clinicalAccuracy'),
                    desc: t('ai.clinicalAccuracyDesc'),
                  },
                  {
                    icon: <FaUniversity className="h-5 w-5 text-[#0077B6]" />,
                    title: t('ai.learningAssistant'),
                    desc: t('ai.learningAssistantDesc'),
                  },
                ].map((item, index) => (
                  <AIItem key={index} item={item} index={index} />
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2 relative"
            >
              <div className="bg-white p-6 rounded-xl shadow-xl border border-[#20B2AA] border-opacity-30">
                <div className="workflow-image mb-4 rounded-lg overflow-hidden">
                  <img
                    src="/imgs/xray.jpg"
                    alt={t('ai.sampleDiagnosis')}
                    loading="lazy"
                  />
                </div>
              </div>
              <motion.button
                whileHover={{ scale: 1.05, boxShadow: '0 8px 20px rgba(0, 119, 182, 0.4)' }}
                whileTap={{ scale: 0.95 }}
                onClick={() => safeNavigate('/try-ai')}
                className="mt-6 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-3 rounded-full font-bold hover:shadow-xl transition-all duration-300 shadow-lg inline-flex items-center"
                aria-label={t('cta.tryOurAI')}
              >
                {t('cta.tryOurAI')} <RiAiGenerate className="ml-2 text-xl" />
              </motion.button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Modern How It Works Section */}
      <section className="py-20 bg-gradient-to-b from-[rgba(0,119,182,0.05)] to-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('howItWorks.simple')}{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
                {t('howItWorks.workflow')}
              </span>
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">{t('howItWorks.description')}</p>
          </motion.div>

          <div className="relative">
            <div className="hidden md:block absolute left-1/2 h-full w-0.5 bg-gradient-to-b from-[#0077B6] to-[#20B2AA]"></div>

            {/* Step 1 - Student Workflow */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative mb-16 md:mb-24"
            >
              <div className="md:flex items-center gap-8">
                <div className="md:w-1/2 md:pr-16 mb-8 md:mb-0">
                  <div className="flex items-center mb-6">
                    <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white text-2xl font-bold mr-4">
                      1
                    </div>
                    <h3 className="text-2xl font-bold text-[#0077B6]">{t('howItWorks.forStudents')}</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md border border-[rgba(0,119,182,0.2)]">
                    <ul className="space-y-3">
                      {[
                        t('howItWorks.studentStep1'),
                        t('howItWorks.studentStep2'),
                        t('howItWorks.studentStep3'),
                      ].map((step, i) => (
                        <li key={i} className="flex items-start">
                          <div className="bg-[rgba(0,119,182,0.1)] rounded-full p-1 mr-3 mt-1">
                            <svg
                              className="w-4 h-4 text-[#0077B6]"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M5 13l4 4L19 7"
                              ></path>
                            </svg>
                          </div>
                          <span className="text-[#333333]">{step}</span>
                        </li>
                      ))}
                    </ul>
                    <button
                      onClick={() => safeNavigate('/universityServices')}
                      className="mt-6 bg-[rgba(0,119,182,0.1)] hover:bg-[rgba(0,119,182,0.2)] text-[#0077B6] px-6 py-2 rounded-full font-medium transition-colors duration-300 inline-flex items-center"
                      aria-label={t('howItWorks.studentCTA')}
                    >
                      {t('howItWorks.studentCTA')} <FaUniversity className="ml-2" />
                    </button>
                  </div>
                </div>

                <div className="md:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl blur opacity-25 group-hover:opacity-50 transition duration-200"></div>
                    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden border border-[rgba(32,178,170,0.3)]">
                      <div className="workflow-image">
                        <img
                          src="/imgs/dash.jpg"
                          alt={t('howItWorks.forStudents')}
                          loading="lazy"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Step 2 - AI Analysis */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative mb-16 md:mb-24"
            >
              <div className="md:flex items-center gap-8 flex-row-reverse">
                <div className="md:w-1/2 md:pl-16 mb-8 md:mb-0">
                  <div className="flex items-center mb-6">
                    <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white text-2xl font-bold mr-4">
                      2
                    </div>
                    <h3 className="text-2xl font-bold text-[#0077B6]">{t('howItWorks.aiAnalysis')}</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md border border-[rgba(0,119,182,0.2)]">
                    <ul className="space-y-3">
                      {[
                        t('howItWorks.aiStep1'),
                        t('howItWorks.aiStep2'),
                        t('howItWorks.aiStep3'),
                      ].map((step, i) => (
                        <li key={i} className="flex items-start">
                          <div className="bg-[rgba(32,178,170,0.15)] rounded-full p-1 mr-3 mt-1">
                            <RiAiGenerate className="w-4 h-4 text-[#20B2AA]" />
                          </div>
                          <span className="text-[#333333]">{step}</span>
                        </li>
                      ))}
                    </ul>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => safeNavigate('/try-ai')}
                      className="mt-6 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all duration-300 shadow-md inline-flex items-center"
                      aria-label={t('cta.tryOurAI')}
                    >
                      {t('cta.tryOurAI')} <RiAiGenerate className="ml-2" />
                    </motion.button>
                  </div>
                </div>

                <div className="md:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl blur opacity-25 group-hover:opacity-50 transition duration-200"></div>
                    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden border border-[rgba(32,178,170,0.3)]">
                      <div className="workflow-image">
                        <img
                          src="/imgs/ai.jpg"
                          alt={t('howItWorks.aiAnalysis')}
                          loading="lazy"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Step 3 - Administrator Workflow */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="md:flex items-center gap-8">
                <div className="md:w-1/2 md:pr-16 mb-8 md:mb-0">
                  <div className="flex items-center mb-6">
                    <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white text-2xl font-bold mr-4">
                      3
                    </div>
                    <h3 className="text-2xl font-bold text-[#0077B6]">{t('howItWorks.forAdministrators')}</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md border border-[rgba(0,119,182,0.2)]">
                    <ul className="space-y-3">
                      {[
                        t('howItWorks.adminStep1'),
                        t('howItWorks.adminStep2'),
                        t('howItWorks.adminStep3'),
                      ].map((step, i) => (
                        <li key={i} className="flex items-start">
                          <div className="bg-[rgba(0,119,182,0.15)] rounded-full p-1 mr-3 mt-1">
                            <FaChartLine className="w-4 h-4 text-[#0077B6]" />
                          </div>
                          <span className="text-[#333333]">{step}</span>
                        </li>
                      ))}
                    </ul>
                    <button
                      onClick={() => safeNavigate('/universityServices')}
                      className="mt-6 bg-[rgba(0,119,182,0.15)] hover:bg-[rgba(0,119,182,0.25)] text-[#0077B6] px-6 py-2 rounded-full font-medium transition-colors duration-300 inline-flex items-center"
                      aria-label={t('howItWorks.adminCTA')}
                    >
                      {t('howItWorks.adminCTA')} <FaChartLine className="ml-2" />
                    </button>
                  </div>
                </div>

                <div className="md:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl blur opacity-25 group-hover:opacity-50 transition duration-200"></div>
                    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden border border-[rgba(0,119,182,0.3)]">
                      <div className="workflow-image">
                        <img
                          src="/imgs/dash2.jpg"
                          alt={t('howItWorks.forAdministrators')}
                          loading="lazy"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t('cta.readyToTransform')}{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-[rgba(255,255,255,0.7)]">
                {t('cta.yourPractice')}
              </span>
            </h2>
            <p className="text-xl text-white text-opacity-90 max-w-3xl mx-auto">{t('cta.description')}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => safeNavigate('/universityServices')}
              className="bg-white text-[#0077B6] px-8 py-4 rounded-full font-bold hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
              aria-label={t('cta.exploreUniversity')}
            >
              <FaUniversity className="mr-2" />
              {t('cta.exploreUniversity')}
            </motion.button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-8"
          >
            <button
              onClick={() => safeNavigate('/try-ai')}
              className="text-white text-opacity-90 hover:text-white font-medium flex items-center justify-center mx-auto"
              aria-label={t('cta.tryOurAI')}
            >
              <RiAiGenerate className="mr-2" />
              {t('cta.tryOurAI')}
            </button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Home;