const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

// Validation schema for creating a lab request
const labRequestSchema = Joi.object({
  patientId: Joi.string().required(),
  patientName: Joi.string().required(),
  labType: Joi.string().valid('university', 'outside').required(),
  notes: Joi.string().allow(''),
});

// Validation schema for updating a lab request
const updateLabRequestSchema = Joi.object({
  status: Joi.string().valid('approved', 'rejected', 'completed').required(),
  responseNotes: Joi.string().allow(''),
});

// Create a new lab request
const createLabRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = labRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { patientId, patientName, labType, notes } = req.body;

    // Find the student
    const student = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { studentId: req.user.studentId });
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Verify the patient exists
    const patient = await FirestoreHelpers.findOne(COLLECTIONS.PATIENTS, { nationalId: patientId });
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Create the lab request
    const labRequest = {
      studentId: req.user.studentId,
      studentName: req.user.name,
      university: student.university,
      patientId,
      patientName: patient.fullName,
      labType,
      notes: notes || '',
      submitDate: new Date(),
      status: 'pending'
    };

    await FirestoreHelpers.create(COLLECTIONS.LAB_REQUESTS, labRequest);

    res.status(201).json({
      message: 'Lab request created successfully',
      labRequest
    });
  } catch (error) {
    console.error('Error creating lab request:', error);
    res.status(500).json({
      message: 'Server error while creating lab request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all lab requests for a student
const getStudentLabRequests = async (req, res) => {
  try {
    const labRequests = await FirestoreHelpers.find(COLLECTIONS.LAB_REQUESTS, { field: 'studentId', operator: '==', value: req.user.studentId });
    
    // Sort in JavaScript
    labRequests.sort((a, b) => new Date(b.submitDate || b.createdAt) - new Date(a.submitDate || a.createdAt));

    res.json(labRequests);
  } catch (error) {
    console.error('Error fetching student lab requests:', error);
    res.status(500).json({
      message: 'Server error while fetching lab requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all lab requests (for assistants/admins)
const getAllLabRequests = async (req, res) => {
  try {
    // Get university based on user role
    let university;
    if (req.user.role === 'assistant') {
      // For assistants, get university from affiliation or direct university field
      university = req.user.affiliation?.id || req.user.university;
    } else if (req.user.role === 'admin') {
      // For admins, use the university field
      university = req.user.university;
    }

    if (!university) {
      return res.status(400).json({ message: 'University information missing from user profile' });
    }

    const labRequests = await FirestoreHelpers.find(COLLECTIONS.LAB_REQUESTS, { field: 'university', operator: '==', value: university });
    
    // Sort in JavaScript
    labRequests.sort((a, b) => new Date(b.submitDate || b.createdAt) - new Date(a.submitDate || a.createdAt));

    res.json(labRequests);
  } catch (error) {
    console.error('Error fetching all lab requests:', error);
    res.status(500).json({
      message: 'Server error while fetching lab requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update a lab request (approve/reject/complete)
const updateLabRequest = async (req, res) => {
  try {
    // Validate request body
    const { error } = updateLabRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const { status, responseNotes } = req.body;
    const { id } = req.params;

    const labRequest = await FirestoreHelpers.findById(COLLECTIONS.LAB_REQUESTS, id);
    if (!labRequest) {
      return res.status(404).json({ message: 'Lab request not found' });
    }

    // Update the lab request
    labRequest.status = status;
    labRequest.responseNotes = responseNotes || '';
    labRequest.responseDate = new Date();
    labRequest.responderId = req.user.id || req.user.studentId || req.user.assistantId || req.user.adminId;
    labRequest.responderName = req.user.name || req.user.fullName || 'Unknown';

    await FirestoreHelpers.update(COLLECTIONS.LAB_REQUESTS, id, labRequest);

    res.json({
      message: `Lab request ${status}`,
      labRequest
    });
  } catch (error) {
    console.error('Error updating lab request:', error);
    res.status(500).json({
      message: 'Server error while updating lab request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createLabRequest,
  getStudentLabRequests,
  getAllLabRequests,
  updateLabRequest
};
