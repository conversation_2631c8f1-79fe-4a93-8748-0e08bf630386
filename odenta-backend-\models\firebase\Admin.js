const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Admin validation schema for Firebase
const adminSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  email: commonSchemas.email,
  password: Joi.string().required(),
  plainPassword: Joi.string().optional(), // Store unhashed password if needed
  name: Joi.string().required(),
  role: Joi.string().default('admin'),
  university: Joi.string().required(),
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Admin creation schema (without ID)
const createAdminSchema = adminSchema.fork(['id'], (schema) => schema.forbidden());

// Admin update schema (partial)
const updateAdminSchema = adminSchema.fork(
  ['email', 'name', 'university'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Admin login schema
const loginAdminSchema = Joi.object({
  email: commonSchemas.email,
  password: Joi.string().required()
});

// Helper functions for Admin operations
const AdminHelpers = {
  // Validate admin data
  validateCreate: (data) => createAdminSchema.validate(data),
  validateUpdate: (data) => updateAdminSchema.validate(data),
  validateLogin: (data) => loginAdminSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    return prepared;
  },
  
  // Remove sensitive data for client response
  sanitizeForResponse: (adminData) => {
    const sanitized = { ...adminData };
    delete sanitized.password;
    delete sanitized.plainPassword;
    return sanitized;
  }
};

module.exports = {
  adminSchema,
  createAdminSchema,
  updateAdminSchema,
  loginAdminSchema,
  AdminHelpers,
  COLLECTION_NAME: COLLECTIONS.ADMINS
};
