const express = require('express');
const router = express.Router();
const {
  submitReview,
  getReviewById,
  getStudentReviews,
  getPendingReviews,
  getDoneReviews,
  updateReview,
  getAllReviews,
  getReviewStepsByType,
  updateReviewSteps,
  updateStepStatuses,
  getSupervisorReviews,
  getSupervisorSignature,
  saveSupervisorSignature,
  getSupervisorAnalytics,
} = require('../controllers/reviewController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Submit a review (student only)
router.post('/', auth, role('student'), submitReview);

// Get student reviews (student can get their own, supervisor can get any)
router.get('/student', auth, role('student', 'supervisor', 'admin', 'superadmin'), getStudentReviews);

// Get pending reviews (supervisor only)
router.get('/pending', auth, role('supervisor'), getPendingReviews);

// Get done reviews (supervisor only)
router.get('/done', auth, role('supervisor'), getDoneReviews);

// Get all reviews for a supervisor (supervisor only)
router.get('/supervisor', auth, role('supervisor'), getSupervisorReviews);

// Get supervisor's signature (supervisor only)
router.get('/signature', auth, role('supervisor'), getSupervisorSignature);

// Save supervisor's signature (supervisor only)
router.post('/signature', auth, role('supervisor'), saveSupervisorSignature);

// Get supervisor analytics (supervisor only)
router.get('/analytics', auth, role('supervisor'), getSupervisorAnalytics);

// Get a review by ID (student, supervisor, admin, superadmin)
router.get('/:id', auth, role('student', 'supervisor', 'admin', 'superadmin'), getReviewById);

// Get all reviews for university (admin, superadmin)
router.get('/', auth, role('admin', 'superadmin'), getAllReviews);

// Update a review (supervisor only)
router.put('/:reviewId', auth, role('supervisor'), updateReview);

// Get review steps by procedure type (public access for students)
router.get('/steps/:procedureType', getReviewStepsByType);

// Update review steps (student only)
router.put('/steps/:reviewId', auth, role('student'), updateReviewSteps);

// Update step statuses (supervisor only)
router.patch('/:reviewId/step-statuses', auth, role('supervisor'), updateStepStatuses);

module.exports = router;