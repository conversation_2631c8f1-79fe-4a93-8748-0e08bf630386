# Railway Deployment Fix Guide

## 🚨 Error: "Is a directory (os error 21)"

This error occurs when Railway's build process tries to write to a location that's already a directory. Here's how to fix it:

## ✅ Solution Steps

### 1. Configuration Files Added

I've added the following files to fix the deployment:

- **`railway.json`** - Railway configuration
- **`nixpacks.toml`** - Build configuration with Node.js 18 (SIMPLIFIED)
- **`.nixpacks`** - Alternative build configuration (SIMPLIFIED)
- **`Procfile`** - Alternative deployment method
- **`.dockerignore`** - Excludes unnecessary files from build

### 2. Updated package.json

- Removed unnecessary `build` script
- Changed Node.js version to `>=18.0.0` (compatible range)
- Simplified scripts for backend deployment

### 3. Fixed Directory Conflict

The "Is a directory (os error 21)" error was caused by:

- Unnecessary build step for Node.js backend
- Directory conflicts during the build process
- Complex build configuration

I've fixed this by:

- **Removed build step** - Not needed for Node.js backend
- **Simplified installation** - Using `npm ci --production=false`
- **Streamlined configuration** - Minimal required steps

### 4. Deployment Steps

#### Step 1: Commit and Push Changes
```bash
cd odenta-backend-
git add .
git commit -m "Fix Railway deployment - remove build step and simplify config"
git push origin main
```

#### Step 2: Railway Dashboard Setup

1. **Go to Railway Dashboard**
   - Visit [railway.app](https://railway.app)
   - Select your project

2. **Set Environment Variables**
   Add these in Railway Variables tab:

   ```env
   # Database Configuration
   MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer
   
   # JWT Configuration
   JWT_SECRET=your_production_jwt_secret_here
   JWT_REFRESH_SECRET=your_production_refresh_secret_here
   JWT_ACCESS_EXPIRATION=15m
   JWT_REFRESH_EXPIRATION=7d
   
   # Server Configuration
   PORT=5000
   NODE_ENV=production
   
   # Frontend URL
   FRONTEND_URL=https://your-vercel-app.vercel.app
   
   # File Upload Configuration
   UPLOAD_PATH=./uploads
   MAX_FILE_SIZE=50mb
   ```

3. **Generate JWT Secrets**
   ```bash
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
   ```

#### Step 3: Redeploy

1. **Trigger New Deployment**
   - Railway will automatically redeploy when you push changes
   - Or manually trigger from Railway dashboard

2. **Monitor Build Logs**
   - Watch the build process in Railway dashboard
   - Look for any new errors

#### Step 4: Test Deployment

1. **Health Check**
   - Visit: `https://your-app.railway.app/api/health`
   - Should return: `{"status":"OK","timestamp":"..."}`

2. **Root Endpoint**
   - Visit: `https://your-app.railway.app/`
   - Should return: `Welcome to the ODenta API!`

## 🔧 Troubleshooting

### If Still Getting Errors:

1. **Clear Railway Cache**
   - Go to Railway dashboard
   - Settings → Clear build cache
   - Redeploy

2. **Try Alternative Configuration**
   - If `nixpacks.toml` fails, Railway will fall back to `.nixpacks`
   - Or use the `Procfile` method

3. **Check Node.js Version**
   - Ensure you're using Node.js 18.x
   - Railway should automatically detect this

4. **Verify Environment Variables**
   - Double-check all required variables are set
   - Ensure no typos in variable names

### Common Issues:

1. **Missing Environment Variables**
   - Ensure all required variables are set in Railway
   - Check for typos in variable names

2. **Node.js Version Mismatch**
   - The configuration files ensure Node.js 18.x is used

3. **Build Timeout**
   - The simplified configuration should resolve timeout issues

4. **Directory Conflicts**
   - Fixed by removing unnecessary build step
   - Simplified installation process

5. **NPM Installation Error**
   - Fixed by removing separate npm installation
   - npm is included with Node.js 18.x

## 📋 Required Environment Variables

Make sure these are set in Railway:

```env
MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer
JWT_SECRET=your_production_jwt_secret_here
JWT_REFRESH_SECRET=your_production_refresh_secret_here
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://your-vercel-app.vercel.app
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50mb
```

## 🎯 Success Indicators

After successful deployment, you should see:

1. ✅ Build completes without errors
2. ✅ Health check endpoint responds
3. ✅ Application starts successfully
4. ✅ No "context canceled" errors
5. ✅ No "undefined variable 'npm'" errors
6. ✅ No "Is a directory (os error 21)" errors

## 📞 Support

If you're still experiencing issues:

1. Check Railway build logs for specific error messages
2. Verify all environment variables are correctly set
3. Ensure your MongoDB connection string is valid
4. Test the health endpoint after deployment

The simplified configuration should resolve all the deployment issues you've encountered. 