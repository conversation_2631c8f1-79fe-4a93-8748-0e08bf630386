const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const role = require('../middleware/role');
const superadminController = require('../controllers/superadminController');
const superadminAnalyticsController = require('../controllers/superadminAnalyticsController');

// Basic analytics (keep for backward compatibility)
router.get('/superadmin', auth, role('superadmin'), superadminController.getAnalytics);

// Enhanced analytics dashboard
router.get('/analytics', auth, role('superadmin'), superadminAnalyticsController.getSuperadminAnalytics);

// Activity log routes
router.get('/activity', auth, role('superadmin'), superadminController.getActivityLog);
router.get('/activity/export', auth, role('superadmin'), superadminController.exportActivityLog);

// Test activity logging
router.post('/test-activity', auth, role('superadmin'), (req, res) => {
  res.json({ message: 'Test activity logged successfully' });
});

module.exports = router;