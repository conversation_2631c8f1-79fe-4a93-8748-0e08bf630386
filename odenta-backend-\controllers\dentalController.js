const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const teethChartSchema = Joi.object({
  date: Joi.date().required(),
  title: Joi.string().required(),
  patient: Joi.string().required(),
  teeth: Joi.array().items(
    Joi.object({
      toothNumber: Joi.number().required(),
      surfaces: Joi.array().items(Joi.string()),
      procedure: Joi.string().required(),
      notes: Joi.string()
    })
  ).default([])
});

const createChart = async (req, res) => {
  const { error } = teethChartSchema.validate(req.body);
  if (error) return res.status(400).json({ message: error.details[0].message });

  try {
    const patient = await FirestoreHelpers.getDocument(COLLECTIONS.PATIENTS, req.body.patient);
    if (!patient) return res.status(404).json({ message: 'Patient not found' });

    const chart = await FirestoreHelpers.addDocument(COLLECTIONS.TEETH_CHARTS, { ...req.body });
    res.status(201).json(chart);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const getChartsByPatient = async (req, res) => {
  try {
    const charts = await FirestoreHelpers.getDocuments(COLLECTIONS.TEETH_CHARTS, { patient: req.params.nationalId });
    res.json(charts);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const addToothToChart = async (req, res) => {
  try {
    const chart = await FirestoreHelpers.getDocument(COLLECTIONS.TEETH_CHARTS, req.params.chartId);
    if (!chart) return res.status(404).json({ message: 'Chart not found' });
    if (chart.isLocked) return res.status(403).json({ message: 'This chart is locked and cannot be edited' });

    // Check if a tooth with the same number already exists
    const existingToothIndex = chart.teeth.findIndex(tooth => tooth.toothNumber === req.body.toothNumber);

    if (existingToothIndex !== -1) {
      // Update existing tooth
      chart.teeth[existingToothIndex] = {
        ...chart.teeth[existingToothIndex],
        ...req.body
      };
      await FirestoreHelpers.updateDocument(COLLECTIONS.TEETH_CHARTS, req.params.chartId, chart);
      res.status(200).json(chart.teeth[existingToothIndex]);
    } else {
      // Add new tooth
      chart.teeth.push(req.body);
      await FirestoreHelpers.updateDocument(COLLECTIONS.TEETH_CHARTS, req.params.chartId, chart);
      res.status(201).json(chart.teeth[chart.teeth.length - 1]);
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const lockChart = async (req, res) => {
  try {
    const chart = await FirestoreHelpers.getDocument(COLLECTIONS.TEETH_CHARTS, req.params.chartId);
    if (!chart) return res.status(404).json({ message: 'Chart not found' });
    if (chart.isLocked) return res.status(400).json({ message: 'Chart is already locked' });

    chart.isLocked = true;
    await FirestoreHelpers.updateDocument(COLLECTIONS.TEETH_CHARTS, req.params.chartId, chart);
    res.json({ message: 'Chart locked successfully', chart });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = { createChart, getChartsByPatient, addToothToChart, lockChart };