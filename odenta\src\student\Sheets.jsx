import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import Sidebar from './Sidebar';
import Navbar from './Navbar';
import PatientNav from './PatientNav';
import FixedProsthodonticsSheet from './FixedProsthodonticsSheet';
import OperativeSheet from './OperativeSheet';
import EndodonticSheet from './EndodonticSheet';
import RemovableProsthodonticsSheet from './RemovableProsthodonticsSheet';
import PeriodonticsSheet from './PeriodonticsSheet';

const Sheets = ({ selectedChart, setSelectedChart, charts }) => {
  const { nationalId } = useParams();
  const { token } = useAuth();
  const navigate = useNavigate();
  const [sheet, setSheet] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedSheetType, setSelectedSheetType] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [savedSheetId, setSavedSheetId] = useState(null);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Available sheet types
  const sheetTypes = [
    'Operative',
    'Fixed Prosthodontics',
    'Removable Prosthodontics',
    'Endodontics',
    'Periodontics',
  ];

  // Procedure Selection Component
  const ProcedureSelection = () => {
    // Determine background color based on selected sheet type
    let bgColor = "bg-white";
    let borderColor = "";

    if (selectedSheetType) {
      switch(selectedSheetType) {
        case 'Operative':
          bgColor = "bg-[#0077B6]/10";
          borderColor = "border-[#0077B6]/30";
          break;
        case 'Fixed Prosthodontics':
          bgColor = "bg-[#20B2AA]/10";
          borderColor = "border-[#20B2AA]/30";
          break;
        case 'Removable Prosthodontics':
          bgColor = "bg-[#28A745]/10";
          borderColor = "border-[#28A745]/30";
          break;
        case 'Endodontics':
          bgColor = "bg-[#0077B6]/10";
          borderColor = "border-[#0077B6]/30";
          break;
        case 'Periodontics':
          bgColor = "bg-[#20B2AA]/10";
          borderColor = "border-[#20B2AA]/30";
          break;
        default:
          bgColor = "bg-white";
      }
    }

    return (
      <div className={`${bgColor} ${borderColor} border rounded-xl shadow-md p-4 mb-6 transition-all duration-300`}>
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-4 md:mb-0">
            <h2 className="text-lg font-semibold text-gray-800">
              {selectedSheetType ? `Current Procedure: ${selectedSheetType}` : 'Select Procedure Type'}
            </h2>
            <p className="text-sm text-gray-600">
              {selectedSheetType
                ? 'You can change the procedure type using the dropdown'
                : 'Choose the procedure type for this treatment sheet'}
            </p>
          </div>
          <div className="relative w-full md:w-64">
            <select
              value={selectedSheetType || ''}
              onChange={handleSheetTypeChange}
              className="block w-full px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] font-medium shadow-md appearance-none pr-10"
            >
              <option value="" className="bg-white text-gray-800">Select Procedure Type</option>
              {sheetTypes.map((type) => (
                <option key={type} value={type} className="bg-white text-gray-800">
                  {type}
                </option>
              ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-white">
              <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Initialize selectedSheetType from localStorage if available
  useEffect(() => {
    // Clear any previously saved sheet type to ensure fresh selection
    localStorage.removeItem('selectedSheetType');
    setSelectedSheetType('');
  }, []);

  // Handle sheet type change
  const handleSheetTypeChange = (e) => {
    const newSheetType = e.target.value;

    // Save to localStorage for persistence across components
    localStorage.setItem('selectedSheetType', newSheetType);
    setSelectedSheetType(newSheetType);
  };

  useEffect(() => {
    // If no sheet type is selected, don't do anything
    if (!selectedSheetType) {
      setLoading(false);
      setSheet(null);
      return;
    }

    // When a sheet type is selected, create a new empty sheet of that type
    setLoading(true);

    // Create a new sheet object with the selected type
    const newSheet = {
      type: selectedSheetType,
      details: {
        diagnosis: '',
        treatmentPlan: '',
        notes: '',
        specificData: {}
      },
      _id: `new-${Date.now()}`, // Temporary ID for new sheet
      createdAt: new Date().toISOString()
    };

    console.log(`Creating new sheet of type: ${selectedSheetType}`);
    setSheet(newSheet);
    setError('');
    setLoading(false);

  }, [selectedSheetType]);

  // Redirect to history after successful save
  useEffect(() => {
    if (showSuccessPopup) {
      const timer = setTimeout(() => {
        navigate(`/patientprofile/${nationalId}/history`);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [showSuccessPopup, navigate, nationalId]);

  // Function to save sheet data to the database
  const handleSaveSheet = async (sheetData, diagnosis, treatmentPlan, notes) => {
    try {
      setLoading(true);

      const sheetToSave = {
        type: sheet.type,
        diagnosis: diagnosis || '',
        treatmentPlan: treatmentPlan || '',
        notes: notes || '',
        specificData: sheetData
      };

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}/treatment-sheets`,
        sheetToSave,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Sheet saved successfully:', response.data);
      setSavedSheetId(response.data.sheet?._id || response.data.sheet?.id || 'saved');
      setShowSuccessPopup(true);
      setLoading(false);

      return true;
    } catch (err) {
      console.error('Error saving sheet:', err.response?.data);
      setError(err.response?.data?.message || 'Failed to save sheet');
      setLoading(false);
      return false;
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-shrink-0">
            <PatientNav
              selectedChart={selectedChart}
              setSelectedChart={setSelectedChart}
              charts={charts}
            />
          </div>
          <div className="p-6 overflow-y-auto">
            <div className="max-w-6xl mx-auto">
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedSheetType) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-shrink-0">
            <PatientNav
              selectedChart={selectedChart}
              setSelectedChart={setSelectedChart}
              charts={charts}
            />
          </div>
          <div className="p-6 overflow-y-auto">
            <div className="max-w-6xl mx-auto">
              <ProcedureSelection />

              <div className="bg-white rounded-xl shadow-md p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Create New Treatment Sheet</h2>
                <p className="text-gray-600">
                  Select a procedure type above to create a new treatment sheet for this patient.
                </p>
              </div>

              <div className="bg-[#0077B6]/10 border border-[#0077B6]/30 rounded-lg p-4 text-[#0077B6] text-sm">
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 text-[#0077B6]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p>
                    The sheet will be saved to the patient's history after completion.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !sheet) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-shrink-0">
            <PatientNav
              selectedChart={selectedChart}
              setSelectedChart={setSelectedChart}
              charts={charts}
            />
          </div>
          <div className="p-6 overflow-y-auto">
            <div className="max-w-6xl mx-auto">
              <ProcedureSelection />

              <div className="p-4 bg-red-100 text-[#333333] rounded-lg">
                {error || `No ${selectedSheetType} sheet found. Please select a different sheet type.`}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <div className="flex-shrink-0">
          <PatientNav
            selectedChart={selectedChart}
            setSelectedChart={setSelectedChart}
            charts={charts}
          />
        </div>
        <div className="p-6 overflow-y-auto">
          <div className="max-w-6xl mx-auto">
            <ProcedureSelection />

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {(() => {
                const sheetProps = {
                  uniqueId: sheet.details?.specificData?.uniqueId || sheet._id || '',
                  initialData: sheet.details?.specificData || {},
                  onSave: handleSaveSheet
                };

                switch(sheet.type) {
                  case 'Fixed Prosthodontics':
                    return <FixedProsthodonticsSheet {...sheetProps} />;
                  case 'Operative':
                    return <OperativeSheet {...sheetProps} />;
                  case 'Endodontics':
                    return <EndodonticSheet {...sheetProps} />;
                  case 'Removable Prosthodontics':
                    return <RemovableProsthodonticsSheet {...sheetProps} />;
                  case 'Periodontics':
                    return <PeriodonticsSheet {...sheetProps} />;
                  default:
                    return (
                      <div className="p-6 bg-white rounded-lg shadow">
                        <h2 className="text-xl font-semibold text-gray-800 mb-4">{sheet.type} Sheet</h2>
                        <p className="text-gray-600">This sheet type is not yet implemented.</p>
                        <pre className="mt-4 p-4 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(sheet, null, 2)}
                        </pre>
                      </div>
                    );
                }
              })()}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Success Popup */}
      <AnimatePresence>
        {showSuccessPopup && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 flex items-center justify-center z-50"
          >
            <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowSuccessPopup(false)}></div>
            <div className="bg-white rounded-lg shadow-xl p-6 m-4 max-w-sm w-full relative z-10">
              <div className="flex items-center justify-center mb-4">
                <div className="bg-[#28A745]/20 rounded-full p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#28A745]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-center text-gray-800 mb-2">Sheet Saved Successfully!</h3>
              <p className="text-center text-gray-600 mb-4">Your {sheet.type} sheet has been saved.</p>
              <div className="flex justify-center">
                <p className="text-sm text-gray-500">Redirecting to history...</p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Sheets;