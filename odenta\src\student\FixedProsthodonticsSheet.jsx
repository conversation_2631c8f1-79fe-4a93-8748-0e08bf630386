import { useState } from 'react';
import { motion } from 'framer-motion';
import MedicalTab from './MedicalTab';
import { generateSheetPDF } from '../utils/pdfUtils';

const FixedProsthodonticsSheet = ({ initialData, onSave }) => {
  const defaultFormData = {
    crown: {
      abutmentTooth: '',
      periodontalExamination: {
        oralHygieneCondition: '',
        gingivitis: '',
        periodontitis: ''
      },
      occlusion: {
        angleClassification: '',
        workingSideContactsRight: '',
        workingSideContactsLeft: ''
      },
      clinicalExamination: {
        periodontalPockets: '',
        mobility: '',
        gingivalRecession: '',
        caries: '',
        restorations: '',
        restorationType: '',
        dentinPulpComplex: ''
      },
      radiographicExamination: {
        periapicalLesions: '',
        rctQuality: '',
        alveolarBoneLoss: '',
        crownRootRatio: ''
      },
      mountedDiagnosticCasts: {
        lengthOfTooth: '',
        positionOfTooth: '',
        rotationOfTooth: '',
        mdTilting: '',
        supraEruptionOfOpposing: ''
      },
      treatmentPlan: {
        preProstheticPreparation: {
          restoration: '',
          rootCanalTreatment: '',
          periodontalTherapy: '',
          crownLengthening: '',
          orthoTreatment: ''
        },
        restorationDesign: {
          material: '',
          restoration: '',
          occlusalContact: '',
          ceramicShade: ''
        }
      }
    },
    fixedDentalProsthesis: {
      periodontalExamination: {
        oralHygieneCondition: '',
        gingivitis: '',
        periodontitis: ''
      },
      occlusion: {
        angleClassification: '',
        workingSideContactsRight: '',
        workingSideContactsLeft: ''
      },
      abutmentA: {
        toothNumber: '',
        clinicalExamination: {
          periodontalPockets: '',
          mobility: '',
          gingivalRecession: '',
          caries: '',
          restorations: '',
          restorationType: '',
          dentinPulpComplex: ''
        },
        radiographicExamination: {
          periapicalLesions: '',
          rctQuality: '',
          alveolarBoneLoss: '',
          crownRootRatio: ''
        }
      },
      abutmentB: {
        toothNumber: '',
        clinicalExamination: {
          periodontalPockets: '',
          mobility: '',
          gingivalRecession: '',
          caries: '',
          restorations: '',
          restorationType: '',
          dentinPulpComplex: ''
        },
        radiographicExamination: {
          periapicalLesions: '',
          rctQuality: '',
          alveolarBoneLoss: '',
          crownRootRatio: ''
        }
      },
      mountedDiagnosticCasts: {
        lengthOfAbutment: '',
        positionOfAbutment: '',
        rotationOfAbutment: '',
        abutmentMdTilting: '',
        supraEruptionOfOpposing: '',
        spanLength: '',
        edentulousRidge: ''
      },
      treatmentPlan: {
        preProstheticPreparationAbutmentA: {
          restoration: '',
          rootCanalTreatment: '',
          periodontalTherapy: '',
          crownLengthening: '',
          orthoTreatment: ''
        },
        preProstheticPreparationAbutmentB: {
          restoration: '',
          rootCanalTreatment: '',
          periodontalTherapy: '',
          crownLengthening: '',
          orthoTreatment: ''
        },
        restorationDesign: {
          material: '',
          retainer: '',
          ponticDesign: '',
          ponticTissueContact: '',
          occlusalContact: '',
          connector: '',
          ceramicShadeAbutmentA: '',
          ceramicShadeAbutmentB: ''
        }
      }
    }
  };

  const mergedData = initialData && Object.keys(initialData).length > 0
    ? { ...defaultFormData, ...initialData }
    : defaultFormData;

  const [formData, setFormData] = useState(mergedData);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('medical');

  // Modified validation to make fields optional
  const validateForm = () => {
    // Since all fields are now optional, we're just returning true
    // You can add custom validation logic here if needed for specific fields
    return true;
  };

  const handleChange = (section, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${field}`]: '' }));
  };

  const handleNestedChange = (section, parent, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [parent]: {
          ...prev[section][parent],
          [subsection]: {
            ...prev[section][parent][subsection],
            [field]: value
          }
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${parent}.${subsection}.${field}`]: '' }));
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      alert('Please fill all required fields.');
      return;
    }

    // Get the active tab data
    const sheetData = activeTab === 'crown' ? formData.crown : formData.fixedDentalProsthesis;

    // Create diagnosis and treatment plan strings
    const diagnosis = `${activeTab === 'crown' ? 'Crown' : 'Fixed Dental Prosthesis'} for ${
      activeTab === 'crown'
        ? `tooth ${formData.crown.abutmentTooth}`
        : `teeth ${formData.fixedDentalProsthesis.abutmentA.toothNumber} to ${formData.fixedDentalProsthesis.abutmentB.toothNumber}`
    }`;

    const treatmentPlan = activeTab === 'crown'
      ? `${formData.crown.treatmentPlan.restorationDesign.material} ${formData.crown.treatmentPlan.restorationDesign.restoration}`
      : `${formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.material} bridge with ${formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticDesign} pontic design`;

    // Call the onSave function from props
    if (onSave) {
      const success = await onSave(sheetData, diagnosis, treatmentPlan, '');
      if (!success) {
        alert('Failed to save sheet. Please try again.');
      }
    } else {
      alert('Form submitted successfully!');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // Get the active tab data
      const sheetData = activeTab === 'crown' ? formData.crown : formData.fixedDentalProsthesis;

      // Create diagnosis and treatment plan strings
      const diagnosis = `${activeTab === 'crown' ? 'Crown' : 'Fixed Dental Prosthesis'} for ${
        activeTab === 'crown'
          ? `tooth ${formData.crown.abutmentTooth}`
          : `teeth ${formData.fixedDentalProsthesis.abutmentA.toothNumber} to ${formData.fixedDentalProsthesis.abutmentB.toothNumber}`
      }`;

      const treatmentPlan = activeTab === 'crown'
        ? `${formData.crown.treatmentPlan.restorationDesign.material} ${formData.crown.treatmentPlan.restorationDesign.restoration}`
        : `${formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.material} bridge with ${formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticDesign} pontic design`;

      // Create a mock sheet object for PDF generation
      const mockSheet = {
        type: 'Fixed Prosthodontics',
        createdAt: new Date().toISOString(),
        details: {
          diagnosis,
          treatmentPlan,
          specificData: sheetData
        }
      };

      await generateSheetPDF(mockSheet, 'Current_Patient');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const renderRadioButtons = (label, value, onChange, options, error, required = false) => (
    <div className="mb-5">
      <label className="block text-base font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {options.map((option) => (
          <div
            key={option}
            className={`flex items-center p-2 rounded-lg border transition-all ${
              value === option
                ? 'bg-[rgba(40,167,69,0.1)] border-[#28A745]'
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
          >
            <input
              type="radio"
              id={`${label.replace(/\s+/g, '')}-${option}`}
              name={label.replace(/\s+/g, '')}
              value={option}
              checked={value === option}
              onChange={onChange}
              className={`h-4 w-4 ${value === option ? 'text-[#28A745] focus:ring-[#28A745]' : 'text-[#0077B6] focus:ring-[#0077B6]'} border-gray-300`}
            />
            <label
              htmlFor={`${label.replace(/\s+/g, '')}-${option}`}
              className={`ml-2 text-sm cursor-pointer w-full ${value === option ? 'font-medium text-[#28A745]' : 'text-gray-700'}`}
            >
              {option}
            </label>
          </div>
        ))}
      </div>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  // Adding renderSelect function for backward compatibility
  const renderSelect = (label, value, onChange, options, error, required = false) => (
    <div className="mb-4">
      <label className="block text-base font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <select
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all ${error ? 'border-red-500' : 'border-gray-300'}`}
      >
        <option value="">Select</option>
        {options.map((option) => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  const renderInput = (label, value, onChange, error, type = 'text', required = false) => (
    <div className="mb-4">
      <label className="block text-base font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type={type}
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all ${error ? 'border-red-500' : 'border-gray-300'}`}
      />
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto mt-8 p-6 bg-white rounded-xl shadow-md border border-[rgba(0,119,182,0.1)]"
    >
      <h3 className="text-2xl font-bold text-[#0077B6] mb-6">Fixed Prosthodontics Examination Sheet</h3>

      {/* Tabs */}
      <div className="flex border-b border-[rgba(0,119,182,0.1)] mb-6 overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'medical' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('medical')}
        >
          Medical
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'crown' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('crown')}
        >
          Crown
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'fdp' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('fdp')}
        >
          Fixed Dental Prosthesis
        </button>
      </div>

      {activeTab === 'medical' && (
        <div>
          <MedicalTab onSave={(medicalInfo) => {
            console.log('Medical info updated:', medicalInfo);
            // You can update the form data here if needed
          }} />
        </div>
      )}

      {activeTab === 'crown' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Abutment Tooth:</h4>
            {renderInput(
              'Abutment Tooth',
              formData.crown.abutmentTooth,
              (e) => setFormData({ ...formData, crown: { ...formData.crown, abutmentTooth: e.target.value } }),
              errors['crown.abutmentTooth']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">1) Periodontal Examination</h4>
            {renderRadioButtons(
              'Oral Hygiene Condition',
              formData.crown.periodontalExamination.oralHygieneCondition,
              (e) => handleChange('crown', 'periodontalExamination', 'oralHygieneCondition', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['crown.periodontalExamination.oralHygieneCondition']
            )}
            {renderRadioButtons(
              'Gingivitis',
              formData.crown.periodontalExamination.gingivitis,
              (e) => handleChange('crown', 'periodontalExamination', 'gingivitis', e.target.value),
              ['None', 'Localized', 'Generalized'],
              errors['crown.periodontalExamination.gingivitis']
            )}
            {renderRadioButtons(
              'Periodontitis',
              formData.crown.periodontalExamination.periodontitis,
              (e) => handleChange('crown', 'periodontalExamination', 'periodontitis', e.target.value),
              ['None', 'Localized', 'Generalized'],
              errors['crown.periodontalExamination.periodontitis']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">2) Occlusion</h4>
            {renderRadioButtons(
              'Angle Classification of Max Intercusptation',
              formData.crown.occlusion.angleClassification,
              (e) => handleChange('crown', 'occlusion', 'angleClassification', e.target.value),
              ['Class I', 'Class II', 'Class III'],
              errors['crown.occlusion.angleClassification']
            )}
            {renderRadioButtons(
              'Working Side Contacts (Right side)',
              formData.crown.occlusion.workingSideContactsRight,
              (e) => handleChange('crown', 'occlusion', 'workingSideContactsRight', e.target.value),
              ['Canine Guidance', 'Group Function'],
              errors['crown.occlusion.workingSideContactsRight']
            )}
            {renderRadioButtons(
              'Working Side Contacts (Left side)',
              formData.crown.occlusion.workingSideContactsLeft,
              (e) => handleChange('crown', 'occlusion', 'workingSideContactsLeft', e.target.value),
              ['Canine Guidance', 'Group Function'],
              errors['crown.occlusion.workingSideContactsLeft']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">3) Clinical Examination</h4>
            {renderRadioButtons(
              'Periodontal Pockets',
              formData.crown.clinicalExamination.periodontalPockets,
              (e) => handleChange('crown', 'clinicalExamination', 'periodontalPockets', e.target.value),
              ['None', '1-2 mm', '3-4 mm', '>4 mm'],
              errors['crown.clinicalExamination.periodontalPockets']
            )}
            {renderRadioButtons(
              'Mobility',
              formData.crown.clinicalExamination.mobility,
              (e) => handleChange('crown', 'clinicalExamination', 'mobility', e.target.value),
              ['Grade 0', 'Grade 1', 'Grade 2', 'Grade 3'],
              errors['crown.clinicalExamination.mobility']
            )}
            {renderRadioButtons(
              'Gingival Recession',
              formData.crown.clinicalExamination.gingivalRecession,
              (e) => handleChange('crown', 'clinicalExamination', 'gingivalRecession', e.target.value),
              ['None', '1-2 mm', '>2 mm'],
              errors['crown.clinicalExamination.gingivalRecession']
            )}
            {renderRadioButtons(
              'Caries',
              formData.crown.clinicalExamination.caries,
              (e) => handleChange('crown', 'clinicalExamination', 'caries', e.target.value),
              ['No caries', 'Simple', 'Extensive'],
              errors['crown.clinicalExamination.caries']
            )}
            {renderRadioButtons(
              'Restorations',
              formData.crown.clinicalExamination.restorations,
              (e) => handleChange('crown', 'clinicalExamination', 'restorations', e.target.value),
              ['No restoration', 'Simple', 'Extensive'],
              errors['crown.clinicalExamination.restorations']
            )}
            {renderRadioButtons(
              'Restoration Type',
              formData.crown.clinicalExamination.restorationType,
              (e) => handleChange('crown', 'clinicalExamination', 'restorationType', e.target.value),
              ['Amalgam', 'Composite', 'Custom Post and core', 'Prefabricated post and core'],
              errors['crown.clinicalExamination.restorationType']
            )}
            {renderRadioButtons(
              'Dentin Pulp Complex',
              formData.crown.clinicalExamination.dentinPulpComplex,
              (e) => handleChange('crown', 'clinicalExamination', 'dentinPulpComplex', e.target.value),
              ['Normal - Vital', 'Pulpitis', 'Non Vital', 'RCT'],
              errors['crown.clinicalExamination.dentinPulpComplex']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">4) Radiographic Examination</h4>
            {renderRadioButtons(
              'Periapical Lesions',
              formData.crown.radiographicExamination.periapicalLesions,
              (e) => handleChange('crown', 'radiographicExamination', 'periapicalLesions', e.target.value),
              ['Yes', 'No'],
              errors['crown.radiographicExamination.periapicalLesions']
            )}
            {renderRadioButtons(
              'RCT Quality (if present)',
              formData.crown.radiographicExamination.rctQuality,
              (e) => handleChange('crown', 'radiographicExamination', 'rctQuality', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['crown.radiographicExamination.rctQuality']
            )}
            {renderRadioButtons(
              'Alveolar Bone Loss',
              formData.crown.radiographicExamination.alveolarBoneLoss,
              (e) => handleChange('crown', 'radiographicExamination', 'alveolarBoneLoss', e.target.value),
              ['None', '1-2 mm', '2-4 mm', '>4 mm'],
              errors['crown.radiographicExamination.alveolarBoneLoss']
            )}
            {renderRadioButtons(
              'Crown:Root Ratio',
              formData.crown.radiographicExamination.crownRootRatio,
              (e) => handleChange('crown', 'radiographicExamination', 'crownRootRatio', e.target.value),
              ['2:3', '1:2', '1:1', '>1:1'],
              errors['crown.radiographicExamination.crownRootRatio']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">5) Mounted Diagnostic Casts</h4>
            {renderRadioButtons(
              'Length of Tooth',
              formData.crown.mountedDiagnosticCasts.lengthOfTooth,
              (e) => handleChange('crown', 'mountedDiagnosticCasts', 'lengthOfTooth', e.target.value),
              ['Acceptable', 'Short Abutment'],
              errors['crown.mountedDiagnosticCasts.lengthOfTooth']
            )}
            {renderRadioButtons(
              'Position of Tooth',
              formData.crown.mountedDiagnosticCasts.positionOfTooth,
              (e) => handleChange('crown', 'mountedDiagnosticCasts', 'positionOfTooth', e.target.value),
              ['Acceptable', 'Not Acceptable'],
              errors['crown.mountedDiagnosticCasts.positionOfTooth']
            )}
            {renderRadioButtons(
              'Rotation of Tooth',
              formData.crown.mountedDiagnosticCasts.rotationOfTooth,
              (e) => handleChange('crown', 'mountedDiagnosticCasts', 'rotationOfTooth', e.target.value),
              ['Acceptable', 'Not Acceptable'],
              errors['crown.mountedDiagnosticCasts.rotationOfTooth']
            )}
            {renderRadioButtons(
              'MD Tilting',
              formData.crown.mountedDiagnosticCasts.mdTilting,
              (e) => handleChange('crown', 'mountedDiagnosticCasts', 'mdTilting', e.target.value),
              ['No Tilting', 'Slightly Tilted', 'Over Tilted'],
              errors['crown.mountedDiagnosticCasts.mdTilting']
            )}
            {renderRadioButtons(
              'Supra Eruption of Opposing',
              formData.crown.mountedDiagnosticCasts.supraEruptionOfOpposing,
              (e) => handleChange('crown', 'mountedDiagnosticCasts', 'supraEruptionOfOpposing', e.target.value),
              ['None', 'Mild (need Enameloplasty)', 'Needs Occlusal restoration'],
              errors['crown.mountedDiagnosticCasts.supraEruptionOfOpposing']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Treatment Plan</h4>
            <div className="mb-5 p-4 bg-white rounded-lg shadow-sm">
              <h5 className="text-base font-semibold text-[#20B2AA] mb-3">Pre-prosthetic Preparation</h5>
              {renderRadioButtons(
                'Restoration',
                formData.crown.treatmentPlan.preProstheticPreparation.restoration,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'preProstheticPreparation', 'restoration', e.target.value),
                ['Not Needed', 'Needed'],
                errors['crown.treatmentPlan.preProstheticPreparation.restoration']
              )}
              {renderRadioButtons(
                'Root Canal Treatment',
                formData.crown.treatmentPlan.preProstheticPreparation.rootCanalTreatment,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'preProstheticPreparation', 'rootCanalTreatment', e.target.value),
                ['Not Needed', 'RCT Needed', 'Treated', 'Treated - need retreatment'],
                errors['crown.treatmentPlan.preProstheticPreparation.rootCanalTreatment']
              )}
              {renderRadioButtons(
                'Periodontal Therapy',
                formData.crown.treatmentPlan.preProstheticPreparation.periodontalTherapy,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'preProstheticPreparation', 'periodontalTherapy', e.target.value),
                ['Not Needed', 'Needed'],
                errors['crown.treatmentPlan.preProstheticPreparation.periodontalTherapy']
              )}
              {renderRadioButtons(
                'Crown Lengthening',
                formData.crown.treatmentPlan.preProstheticPreparation.crownLengthening,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'preProstheticPreparation', 'crownLengthening', e.target.value),
                ['Not Needed', 'Needed'],
                errors['crown.treatmentPlan.preProstheticPreparation.crownLengthening']
              )}
              {renderRadioButtons(
                'Ortho. Treatment',
                formData.crown.treatmentPlan.preProstheticPreparation.orthoTreatment,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'preProstheticPreparation', 'orthoTreatment', e.target.value),
                ['Not Needed', 'Needed'],
                errors['crown.treatmentPlan.preProstheticPreparation.orthoTreatment']
              )}
            </div>
            <div className="mb-4 p-4 bg-white rounded-lg shadow-sm">
              <h5 className="text-base font-semibold text-[#20B2AA] mb-3">Restoration Design</h5>
              {renderRadioButtons(
                'Material',
                formData.crown.treatmentPlan.restorationDesign.material,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'restorationDesign', 'material', e.target.value),
                ['Full Metal', 'PFM', 'Zirconia', 'All Ceramic'],
                errors['crown.treatmentPlan.restorationDesign.material']
              )}
              {renderRadioButtons(
                'Restoration',
                formData.crown.treatmentPlan.restorationDesign.restoration,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'restorationDesign', 'restoration', e.target.value),
                ['Crown', 'Onlay', 'Inlay'],
                errors['crown.treatmentPlan.restorationDesign.restoration']
              )}
              {renderRadioButtons(
                'Occlusal Contact (in PFM only)',
                formData.crown.treatmentPlan.restorationDesign.occlusalContact,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'restorationDesign', 'occlusalContact', e.target.value),
                ['Metal', 'Ceramic', 'Combination'],
                errors['crown.treatmentPlan.restorationDesign.occlusalContact']
              )}
              {renderInput(
                'Ceramic Shade',
                formData.crown.treatmentPlan.restorationDesign.ceramicShade,
                (e) => handleNestedChange('crown', 'treatmentPlan', 'restorationDesign', 'ceramicShade', e.target.value),
                errors['crown.treatmentPlan.restorationDesign.ceramicShade']
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'fdp' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">1) Periodontal Examination</h4>
            {renderRadioButtons(
              'Oral Hygiene Condition',
              formData.fixedDentalProsthesis.periodontalExamination.oralHygieneCondition,
              (e) => handleChange('fixedDentalProsthesis', 'periodontalExamination', 'oralHygieneCondition', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['fixedDentalProsthesis.periodontalExamination.oralHygieneCondition']
            )}
            {renderRadioButtons(
              'Gingivitis',
              formData.fixedDentalProsthesis.periodontalExamination.gingivitis,
              (e) => handleChange('fixedDentalProsthesis', 'periodontalExamination', 'gingivitis', e.target.value),
              ['None', 'Localized', 'Generalized'],
              errors['fixedDentalProsthesis.periodontalExamination.gingivitis']
            )}
            {renderRadioButtons(
              'Periodontitis',
              formData.fixedDentalProsthesis.periodontalExamination.periodontitis,
              (e) => handleChange('fixedDentalProsthesis', 'periodontalExamination', 'periodontitis', e.target.value),
              ['None', 'Localized', 'Generalized'],
              errors['fixedDentalProsthesis.periodontalExamination.periodontitis']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">2) Occlusion</h4>
            {renderRadioButtons(
              'Angle Classification of Max Intercusptation',
              formData.fixedDentalProsthesis.occlusion.angleClassification,
              (e) => handleChange('fixedDentalProsthesis', 'occlusion', 'angleClassification', e.target.value),
              ['Class I', 'Class II', 'Class III'],
              errors['fixedDentalProsthesis.occlusion.angleClassification']
            )}
            {renderRadioButtons(
              'Working Side Contacts (Right side)',
              formData.fixedDentalProsthesis.occlusion.workingSideContactsRight,
              (e) => handleChange('fixedDentalProsthesis', 'occlusion', 'workingSideContactsRight', e.target.value),
              ['Canine Guidance', 'Group Function'],
              errors['fixedDentalProsthesis.occlusion.workingSideContactsRight']
            )}
            {renderRadioButtons(
              'Working Side Contacts (Left side)',
              formData.fixedDentalProsthesis.occlusion.workingSideContactsLeft,
              (e) => handleChange('fixedDentalProsthesis', 'occlusion', 'workingSideContactsLeft', e.target.value),
              ['Canine Guidance', 'Group Function'],
              errors['fixedDentalProsthesis.occlusion.workingSideContactsLeft']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">3) Abutment A - Clinical Examination</h4>
            {renderInput(
              'Tooth Number',
              formData.fixedDentalProsthesis.abutmentA.toothNumber,
              (e) => handleChange('fixedDentalProsthesis', 'abutmentA', 'toothNumber', e.target.value),
              errors['fixedDentalProsthesis.abutmentA.toothNumber']
            )}
            {renderRadioButtons(
              'Periodontal Pockets',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.periodontalPockets,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'periodontalPockets', e.target.value),
              ['None', '1-2 mm', '3-4 mm', '>4 mm'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.periodontalPockets']
            )}
            {renderRadioButtons(
              'Mobility',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.mobility,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'mobility', e.target.value),
              ['Grade 0', 'Grade 1', 'Grade 2', 'Grade 3'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.mobility']
            )}
            {renderRadioButtons(
              'Gingival Recession',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.gingivalRecession,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'gingivalRecession', e.target.value),
              ['None', '1-2 mm', '>2 mm'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.gingivalRecession']
            )}
            {renderRadioButtons(
              'Caries',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.caries,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'caries', e.target.value),
              ['No caries', 'Simple', 'Extensive'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.caries']
            )}
            {renderRadioButtons(
              'Restorations',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.restorations,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'restorations', e.target.value),
              ['No restoration', 'Simple', 'Extensive'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.restorations']
            )}
            {renderRadioButtons(
              'Restoration Type',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.restorationType,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'restorationType', e.target.value),
              ['Amalgam', 'Composite', 'Custom Post and core', 'Prefabricated post and core'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.restorationType']
            )}
            {renderRadioButtons(
              'Dentin Pulp Complex',
              formData.fixedDentalProsthesis.abutmentA.clinicalExamination.dentinPulpComplex,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'clinicalExamination', 'dentinPulpComplex', e.target.value),
              ['Normal - Vital', 'Pulpitis', 'Non Vital', 'RCT'],
              errors['fixedDentalProsthesis.abutmentA.clinicalExamination.dentinPulpComplex']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">4) Abutment A - Radiographic Examination</h4>
            {renderRadioButtons(
              'Periapical Lesions',
              formData.fixedDentalProsthesis.abutmentA.radiographicExamination.periapicalLesions,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'radiographicExamination', 'periapicalLesions', e.target.value),
              ['Yes', 'No'],
              errors['fixedDentalProsthesis.abutmentA.radiographicExamination.periapicalLesions']
            )}
            {renderRadioButtons(
              'RCT Quality (if present)',
              formData.fixedDentalProsthesis.abutmentA.radiographicExamination.rctQuality,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'radiographicExamination', 'rctQuality', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['fixedDentalProsthesis.abutmentA.radiographicExamination.rctQuality']
            )}
            {renderRadioButtons(
              'Alveolar Bone Loss',
              formData.fixedDentalProsthesis.abutmentA.radiographicExamination.alveolarBoneLoss,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'radiographicExamination', 'alveolarBoneLoss', e.target.value),
              ['None', '1-2 mm', '2-4 mm', '>4 mm'],
              errors['fixedDentalProsthesis.abutmentA.radiographicExamination.alveolarBoneLoss']
            )}
            {renderRadioButtons(
              'Crown:Root Ratio',
              formData.fixedDentalProsthesis.abutmentA.radiographicExamination.crownRootRatio,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentA', 'radiographicExamination', 'crownRootRatio', e.target.value),
              ['2:3', '1:2', '1:1', '>1:1'],
              errors['fixedDentalProsthesis.abutmentA.radiographicExamination.crownRootRatio']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">5) Abutment B - Clinical Examination</h4>
            {renderInput(
              'Tooth Number',
              formData.fixedDentalProsthesis.abutmentB.toothNumber,
              (e) => handleChange('fixedDentalProsthesis', 'abutmentB', 'toothNumber', e.target.value),
              errors['fixedDentalProsthesis.abutmentB.toothNumber']
            )}
            {renderRadioButtons(
              'Periodontal Pockets',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.periodontalPockets,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'periodontalPockets', e.target.value),
              ['None', '1-2 mm', '3-4 mm', '>4 mm'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.periodontalPockets']
            )}
            {renderRadioButtons(
              'Mobility',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.mobility,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'mobility', e.target.value),
              ['Grade 0', 'Grade 1', 'Grade 2', 'Grade 3'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.mobility']
            )}
            {renderRadioButtons(
              'Gingival Recession',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.gingivalRecession,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'gingivalRecession', e.target.value),
              ['None', '1-2 mm', '>2 mm'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.gingivalRecession']
            )}
            {renderRadioButtons(
              'Caries',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.caries,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'caries', e.target.value),
              ['No caries', 'Simple', 'Extensive'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.caries']
            )}
            {renderRadioButtons(
              'Restorations',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.restorations,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'restorations', e.target.value),
              ['No restoration', 'Simple', 'Extensive'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.restorations']
            )}
            {renderRadioButtons(
              'Restoration Type',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.restorationType,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'restorationType', e.target.value),
              ['Amalgam', 'Composite', 'Custom Post and core', 'Prefabricated post and core'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.restorationType']
            )}
            {renderRadioButtons(
              'Dentin Pulp Complex',
              formData.fixedDentalProsthesis.abutmentB.clinicalExamination.dentinPulpComplex,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'clinicalExamination', 'dentinPulpComplex', e.target.value),
              ['Normal - Vital', 'Pulpitis', 'Non Vital', 'RCT'],
              errors['fixedDentalProsthesis.abutmentB.clinicalExamination.dentinPulpComplex']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">6) Abutment B - Radiographic Examination</h4>
            {renderRadioButtons(
              'Periapical Lesions',
              formData.fixedDentalProsthesis.abutmentB.radiographicExamination.periapicalLesions,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'radiographicExamination', 'periapicalLesions', e.target.value),
              ['Yes', 'No'],
              errors['fixedDentalProsthesis.abutmentB.radiographicExamination.periapicalLesions']
            )}
            {renderRadioButtons(
              'RCT Quality (if present)',
              formData.fixedDentalProsthesis.abutmentB.radiographicExamination.rctQuality,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'radiographicExamination', 'rctQuality', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['fixedDentalProsthesis.abutmentB.radiographicExamination.rctQuality']
            )}
            {renderRadioButtons(
              'Alveolar Bone Loss',
              formData.fixedDentalProsthesis.abutmentB.radiographicExamination.alveolarBoneLoss,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'radiographicExamination', 'alveolarBoneLoss', e.target.value),
              ['None', '1-2 mm', '2-4 mm', '>4 mm'],
              errors['fixedDentalProsthesis.abutmentB.radiographicExamination.alveolarBoneLoss']
            )}
            {renderRadioButtons(
              'Crown:Root Ratio',
              formData.fixedDentalProsthesis.abutmentB.radiographicExamination.crownRootRatio,
              (e) => handleNestedChange('fixedDentalProsthesis', 'abutmentB', 'radiographicExamination', 'crownRootRatio', e.target.value),
              ['2:3', '1:2', '1:1', '>1:1'],
              errors['fixedDentalProsthesis.abutmentB.radiographicExamination.crownRootRatio']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">7) Mounted Diagnostic Casts</h4>
            {renderRadioButtons(
              'Length of Abutment',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.lengthOfAbutment,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'lengthOfAbutment', e.target.value),
              ['Acceptable', 'Short Abutment'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.lengthOfAbutment']
            )}
            {renderRadioButtons(
              'Position of Abutment',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.positionOfAbutment,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'positionOfAbutment', e.target.value),
              ['Acceptable', 'Not Acceptable'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.positionOfAbutment']
            )}
            {renderRadioButtons(
              'Rotation of Abutment',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.rotationOfAbutment,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'rotationOfAbutment', e.target.value),
              ['Acceptable', 'Not Acceptable'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.rotationOfAbutment']
            )}
            {renderRadioButtons(
              'Abutment MD Tilting',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.abutmentMdTilting,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'abutmentMdTilting', e.target.value),
              ['No Tilting', 'Slightly Tilted', 'Over Tilted'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.abutmentMdTilting']
            )}
            {renderRadioButtons(
              'Supra Eruption of Opposing',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.supraEruptionOfOpposing,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'supraEruptionOfOpposing', e.target.value),
              ['None', 'Mild (need Enameloplasty)', 'Needs Occlusal restoration'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.supraEruptionOfOpposing']
            )}
            {renderRadioButtons(
              'Span Length (FDP)',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.spanLength,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'spanLength', e.target.value),
              ['Sufficient', 'Slightly Deficient', 'Deficient'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.spanLength']
            )}
            {renderRadioButtons(
              'Edentulous Ridge',
              formData.fixedDentalProsthesis.mountedDiagnosticCasts.edentulousRidge,
              (e) => handleChange('fixedDentalProsthesis', 'mountedDiagnosticCasts', 'edentulousRidge', e.target.value),
              ['Acceptable', 'Needs Corrective Surgery'],
              errors['fixedDentalProsthesis.mountedDiagnosticCasts.edentulousRidge']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Treatment Plan</h4>
            <div className="mb-5 p-4 bg-white rounded-lg shadow-sm">
              <h5 className="text-base font-semibold text-[#20B2AA] mb-3">Pre-prosthetic Preparation - Abutment A</h5>
              {renderRadioButtons(
                'Restoration',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.restoration,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentA', 'restoration', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.restoration']
              )}
              {renderRadioButtons(
                'Root Canal Treatment',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.rootCanalTreatment,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentA', 'rootCanalTreatment', e.target.value),
                ['Not Needed', 'RCT Needed', 'Treated', 'Treated - need retreatment'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.rootCanalTreatment']
              )}
              {renderRadioButtons(
                'Periodontal Therapy',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.periodontalTherapy,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentA', 'periodontalTherapy', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.periodontalTherapy']
              )}
              {renderRadioButtons(
                'Crown Lengthening',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.crownLengthening,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentA', 'crownLengthening', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.crownLengthening']
              )}
              {renderRadioButtons(
                'Ortho. Treatment',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.orthoTreatment,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentA', 'orthoTreatment', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentA.orthoTreatment']
              )}
            </div>
            <div className="mb-5 p-4 bg-white rounded-lg shadow-sm">
              <h5 className="text-base font-semibold text-[#20B2AA] mb-3">Pre-prosthetic Preparation - Abutment B</h5>
              {renderRadioButtons(
                'Restoration',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.restoration,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentB', 'restoration', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.restoration']
              )}
              {renderRadioButtons(
                'Root Canal Treatment',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.rootCanalTreatment,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentB', 'rootCanalTreatment', e.target.value),
                ['Not Needed', 'RCT Needed', 'Treated', 'Treated - need retreatment'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.rootCanalTreatment']
              )}
              {renderRadioButtons(
                'Periodontal Therapy',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.periodontalTherapy,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentB', 'periodontalTherapy', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.periodontalTherapy']
              )}
              {renderRadioButtons(
                'Crown Lengthening',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.crownLengthening,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentB', 'crownLengthening', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.crownLengthening']
              )}
              {renderRadioButtons(
                'Ortho. Treatment',
                formData.fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.orthoTreatment,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'preProstheticPreparationAbutmentB', 'orthoTreatment', e.target.value),
                ['Not Needed', 'Needed'],
                errors['fixedDentalProsthesis.treatmentPlan.preProstheticPreparationAbutmentB.orthoTreatment']
              )}
            </div>
            <div className="mb-4 p-4 bg-white rounded-lg shadow-sm">
              <h5 className="text-base font-semibold text-[#20B2AA] mb-3">Restoration Design</h5>
              {renderRadioButtons(
                'Material',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.material,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'material', e.target.value),
                ['Full Metal', 'PFM', 'Zirconia', 'All Ceramic'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.material']
              )}
              {renderRadioButtons(
                'Retainer',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.retainer,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'retainer', e.target.value),
                ['Full Coverage', 'Partial Coverage'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.retainer']
              )}
              {renderRadioButtons(
                'Pontic Design',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticDesign,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'ponticDesign', e.target.value),
                ['Mod. Ridge Lap', 'Ovate', 'Conical', 'Hygienic'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticDesign']
              )}
              {renderRadioButtons(
                'Pontic Tissue Contact (in PFM only)',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticTissueContact,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'ponticTissueContact', e.target.value),
                ['Metal', 'Ceramic'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.ponticTissueContact']
              )}
              {renderRadioButtons(
                'Occlusal Contact (in PFM only)',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.occlusalContact,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'occlusalContact', e.target.value),
                ['Metal', 'Ceramic', 'Combination'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.occlusalContact']
              )}
              {renderRadioButtons(
                'Connector',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.connector,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'connector', e.target.value),
                ['Rigid', 'Non Rigid'],
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.connector']
              )}
              {renderInput(
                'Ceramic Shade Abutment A',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ceramicShadeAbutmentA,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'ceramicShadeAbutmentA', e.target.value),
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.ceramicShadeAbutmentA']
              )}
              {renderInput(
                'Ceramic Shade Abutment B',
                formData.fixedDentalProsthesis.treatmentPlan.restorationDesign.ceramicShadeAbutmentB,
                (e) => handleNestedChange('fixedDentalProsthesis', 'treatmentPlan', 'restorationDesign', 'ceramicShadeAbutmentB', e.target.value),
                errors['fixedDentalProsthesis.treatmentPlan.restorationDesign.ceramicShadeAbutmentB']
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 flex justify-end gap-4">
        <button
          onClick={handleDownloadPDF}
          className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download PDF
        </button>
        <button
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#0077B6] text-white font-medium rounded-lg hover:bg-[#005f92] transition-all shadow-sm"
        >
          Save Sheet
        </button>
      </div>
    </motion.div>
  );
};

export default FixedProsthodonticsSheet;