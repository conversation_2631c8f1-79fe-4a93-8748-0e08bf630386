const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { backendCache, CacheKeys, CacheTTL } = require('../utils/cache');

// Get all students (university context)
const getAllStudents = async (req, res) => {
  try {
    // Extract pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50; // Default to 50 students per page
    const search = req.query.search || '';

    // Get university based on user role
    let university;
    if (req.user.role === 'assistant') {
      // For assistants, get university from affiliation or direct university field
      university = req.user.affiliation?.id || req.user.university;
    } else {
      // For admins, use the university field
      university = req.user.university;
    }

    if (!university) {
      return res.status(400).json({ message: 'University information missing from user profile' });
    }

    // Create cache key
    const cacheKey = CacheKeys.STUDENTS_BY_UNIVERSITY(university, page, limit, search);

    // Try to get from cache first
    const cachedResult = await backendCache.getOrSet(
      cacheKey,
      async () => {
        console.log(`Fetching students from database for university: ${university}`);

        let students = await FirestoreHelpers.find(
          COLLECTIONS.STUDENTS,
          { field: 'university', operator: '==', value: university }
        );

        // Apply search filter if provided
        if (search) {
          const searchLower = search.toLowerCase();
          students = students.filter(student =>
            student.name?.toLowerCase().includes(searchLower) ||
            student.email?.toLowerCase().includes(searchLower) ||
            student.studentId?.toLowerCase().includes(searchLower)
          );
        }

        // Calculate pagination
        const total = students.length;
        const totalPages = Math.ceil(total / limit);
        const skip = (page - 1) * limit;
        const paginatedStudents = students.slice(skip, skip + limit);

        return {
          students: paginatedStudents,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        };
      },
      CacheTTL.LONG // Cache for 15 minutes
    );

    res.json(cachedResult);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all assistants (university context)
const getAllAssistants = async (req, res) => {
  try {
    const assistants = await FirestoreHelpers.find(
      COLLECTIONS.ASSISTANTS,
      { field: 'university', operator: '==', value: req.user.university }
    );
    res.json(assistants);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all supervisors (university context)
const getAllSupervisors = async (req, res) => {
  try {
    const supervisors = await FirestoreHelpers.find(
      COLLECTIONS.SUPERVISORS,
      { field: 'university', operator: '==', value: req.user.university }
    );
    res.json(supervisors);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all patients (only those assigned to students in the admin's/assistant's university)
const getAllPatients = async (req, res) => {
  try {
    console.log('getAllPatients called by user:', req.user);

    // Extract pagination parameters
    const page = parseInt(req.query.page);
    const limit = parseInt(req.query.limit);
    const search = req.query.search || '';
    const studentFilter = req.query.studentFilter || 'all';
    const sortBy = req.query.sortBy || 'registrationDate';
    const sortOrder = req.query.sortOrder || 'desc';

    // Check if pagination is requested
    const usePagination = !isNaN(page) && !isNaN(limit) && page > 0 && limit > 0;
    console.log('Pagination requested:', usePagination, 'page:', page, 'limit:', limit);

    // Get university based on user role
    let university;
    if (req.user.role === 'assistant') {
      // For assistants, get university from affiliation or direct university field
      university = req.user.affiliation?.id || req.user.university;
      console.log('Assistant university from affiliation:', req.user.affiliation?.id);
      console.log('Assistant university from direct field:', req.user.university);
    } else {
      // For admins, use the university field
      university = req.user.university;
    }

    console.log('Final university for query:', university);

    if (!university) {
      return res.status(400).json({ message: 'University information missing from user profile' });
    }

    // Temporarily disable caching to debug the issue
    console.log(`Fetching patients from database for university: ${university}`);

    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'university', operator: '==', value: university }
    );
    console.log('Found students:', students.length);

    const studentIds = students.map(student => student.studentId);
    console.log('Student IDs:', studentIds);

    // Get all patients for these students + unassigned patients
    const allPatients = await FirestoreHelpers.find(COLLECTIONS.PATIENTS);
    let patients = allPatients.filter(patient =>
      studentIds.includes(patient.drId) ||
      patient.drId === 'unassigned' ||
      patient.drId === 'N/A' ||
      !patient.drId
    );
    console.log('Found patients (including unassigned):', patients.length);

    // Enhance patient data with student information
    const studentsMap = new Map(students.map(s => [s.studentId, s]));
    patients = patients.map(patient => {
      const student = studentsMap.get(patient.drId);
      if (student) {
        patient.studentName = student.name;
        patient.studentId = student.studentId;
      } else if (patient.drId === 'unassigned' || patient.drId === 'N/A' || !patient.drId) {
        // Handle unassigned patients
        patient.studentName = 'Unassigned';
        patient.studentId = 'unassigned';
      }
      return patient;
    });

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      patients = patients.filter(patient =>
        patient.fullName?.toLowerCase().includes(searchLower) ||
        patient.nationalId?.toLowerCase().includes(searchLower) ||
        patient.phoneNumber?.toLowerCase().includes(searchLower)
      );
    }

    // Apply student filter
    if (studentFilter !== 'all') {
      if (studentFilter === 'unassigned') {
        patients = patients.filter(patient =>
          patient.drId === 'unassigned' || !patient.drId || patient.drId === 'N/A'
        );
      } else {
        patients = patients.filter(patient => patient.drId === studentFilter);
      }
    }

    // Apply sorting
    patients.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'registrationDate':
        case 'createdAt':
          aValue = new Date(a.registrationDate || a.createdAt?.seconds * 1000 || 0);
          bValue = new Date(b.registrationDate || b.createdAt?.seconds * 1000 || 0);
          break;
        case 'fullName':
          aValue = a.fullName?.toLowerCase() || '';
          bValue = b.fullName?.toLowerCase() || '';
          break;
        case 'nationalId':
          aValue = a.nationalId?.toLowerCase() || '';
          bValue = b.nationalId?.toLowerCase() || '';
          break;
        default:
          aValue = new Date(a.registrationDate || a.createdAt?.seconds * 1000 || 0);
          bValue = new Date(b.registrationDate || b.createdAt?.seconds * 1000 || 0);
      }

      if (sortOrder === 'desc') {
        return (sortBy === 'registrationDate' || sortBy === 'createdAt') ? bValue - aValue : bValue.localeCompare(aValue);
      } else {
        return (sortBy === 'registrationDate' || sortBy === 'createdAt') ? aValue - bValue : aValue.localeCompare(bValue);
      }
    });

    if (usePagination) {
      // Return paginated results
      const total = patients.length;
      const totalPages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;
      const paginatedPatients = patients.slice(skip, skip + limit);

      const result = {
        patients: paginatedPatients,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      };

      res.json(result);
    } else {
      // Return all patients (old format for backward compatibility)
      console.log('Returning all patients (old format):', patients.length);
      res.json(patients);
    }
  } catch (error) {
    console.error('Error in getAllPatients:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get all appointments for the admin's university
const getAllAppointments = async (req, res) => {
  try {
    // Get the admin's university
    const { university } = req.user;

    if (!university) {
      return res.status(400).json({ message: 'University information missing from user profile' });
    }

    console.log(`Fetching appointments for university: ${university}`);

    // Get all students from this university to get their studentIds
    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'university', operator: '==', value: university }
    );
    const studentIds = students.map(student => student.studentId);

    console.log(`Found ${students.length} students for university: ${university}`);

    // Get all appointments and filter them in memory
    const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
    
    // Filter appointments that have the same university field or are assigned to students of this university
    const appointments = allAppointments.filter(appt => 
      appt.university === university || 
      (appt.doctorModel === 'Student' && studentIds.includes(appt.doctor))
    );

    // Sort appointments by date (descending) and time (ascending)
    appointments.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      if (dateA.getTime() !== dateB.getTime()) {
        return dateB.getTime() - dateA.getTime(); // Descending by date
      }
      return (a.time || '').localeCompare(b.time || ''); // Ascending by time
    });

    console.log(`Found ${appointments.length} appointments for university: ${university}`);

    // Create a map using studentId as the key
    const studentsMapByStudentId = new Map(students.map(s => [s.studentId, s]));

    // Enhance appointment data with student information
    const enhancedAppointments = appointments.map(appt => {
      const appointmentData = { ...appt };

      // If the doctor is a student, add the student name
      if (appt.doctorModel === 'Student' && appt.doctor) {
        // Find the student by the doctor field (which contains the studentId)
        const student = studentsMapByStudentId.get(appt.doctor);

        if (student) {
          appointmentData.studentName = student.name;
          appointmentData.studentId = student.studentId;
          console.log(`Found student ${student.name} (${student.studentId}) for appointment ${appt.id}`);
        } else {
          console.log(`No student found for appointment ${appt.id} with doctor (studentId) ${appt.doctor}`);
        }
      }

      return appointmentData;
    });

    res.json(enhancedAppointments);
  } catch (error) {
    console.error('Error fetching appointments:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get all dentists (clinic context)
const getAllDentists = async (req, res) => {
  try {
    const dentists = await FirestoreHelpers.getAll(COLLECTIONS.DENTISTS);
    res.json(dentists);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all treatment sheets from students in the admin's university
const getAllTreatmentSheets = async (req, res) => {
  try {
    // Get the admin's university
    const { university } = req.user;

    if (!university) {
      return res.status(400).json({ message: 'University information missing from user profile' });
    }

    console.log(`Fetching treatment sheets for university: ${university}`);

    // Get all students from this university to get their studentIds
    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'university', operator: '==', value: university }
    );
    const studentIds = students.map(student => student.studentId);

    console.log(`Found ${students.length} students for university: ${university}`);

    // Get all patients and filter those that belong to students of this university
    const allPatients = await FirestoreHelpers.getAll(COLLECTIONS.PATIENTS);
    const patients = allPatients.filter(patient => studentIds.includes(patient.drId));

    console.log(`Found ${patients.length} patients with treatment sheets for university: ${university}`);

    // Create a map using studentId as the key
    const studentsMapByStudentId = new Map(students.map(s => [s.studentId, s]));

    // Extract and enhance all treatment sheets
    const allSheets = [];
    patients.forEach(patient => {
      if (patient.treatmentSheets && patient.treatmentSheets.length > 0) {
        patient.treatmentSheets.forEach(sheet => {
          const student = studentsMapByStudentId.get(patient.drId);
          const enhancedSheet = {
            ...sheet,
            patientName: patient.fullName,
            patientNationalId: patient.nationalId,
            studentName: student ? student.name : 'Unknown Student',
            studentId: patient.drId,
            university: university
          };
          allSheets.push(enhancedSheet);
        });
      }
    });

    console.log(`Found ${allSheets.length} total treatment sheets for university: ${university}`);

    res.json(allSheets);
  } catch (error) {
    console.error('Error fetching treatment sheets:', error);
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  getAllStudents,
  getAllAssistants,
  getAllSupervisors,
  getAllPatients,
  getAllAppointments,
  getAllDentists,
  getAllTreatmentSheets
};
