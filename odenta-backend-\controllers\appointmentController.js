const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { AppointmentHelpers, createAppointmentSchema } = require('../models/firebase/Appointment');

const appointmentSchema = Joi.object({
  date: Joi.date().required(),
  time: Joi.string().required(),
  type: Joi.string().allow('').optional(),
  notes: Joi.string().allow(''),
  status: Joi.string().valid('pending', 'completed', 'cancelled').default('pending'),
  treatment: Joi.string().allow(''),
  duration: Joi.number().default(60),
  patient: Joi.string().required(), // nationalId
  doctor: Joi.string().when('isPatientInitiated', { is: false, then: Joi.required(), otherwise: Joi.allow(null) }),
  doctorModel: Joi.string().valid('Student', 'Dentist').when('isPatientInitiated', { is: false, then: Joi.required(), otherwise: Joi.allow(null) }),
  university: Joi.string().when('isPatientInitiated', { is: true, then: Joi.required(), otherwise: Joi.allow(null) }),
  isPatientInitiated: Joi.boolean().default(false),
  fullName: Joi.string(),
  phoneNumber: Joi.string(),
  age: Joi.number(),
  chiefComplaint: Joi.string().allow('').default(''),
  occupation: Joi.string().allow(''),
  address: Joi.string().allow(''),
});

const patientAppointmentSchema = Joi.object({
  date: Joi.date().required(),
  time: Joi.string().required(),
  type: Joi.string().required(),
  patient: Joi.string().required(), // nationalId
  fullName: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  age: Joi.number().required(),
  chiefComplaint: Joi.string().required(),
  duration: Joi.number().default(60),
  universityClinic: Joi.string().required(),
  isPatientInitiated: Joi.boolean().default(true),
  occupation: Joi.string().allow(''),
  address: Joi.string().allow(''),
});

const createAppointment = async (req, res) => {
  try {
    console.log('Received appointment data:', req.body);
    
    const { error } = appointmentSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details);
      return res.status(400).json({ message: error.details[0].message });
    }

    const { date, time, type, notes, status, patient, doctor, doctorModel, chiefComplaint, occupation, address } = req.body;

    console.log('Creating appointment with data:', { date, time, type, patient, doctor, doctorModel, chiefComplaint });

    // Validate date format
    const appointmentDate = new Date(date);
    if (isNaN(appointmentDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    const patientDoc = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: patient }
    );
    if (!patientDoc) {
      console.log(`No patient found with nationalId: ${patient}`);
      return res.status(404).json({ message: 'Patient not found' });
    }

    console.log('Found patient:', patientDoc);

    let doctorDoc;
    if (doctorModel === 'Student') {
      doctorDoc = await FirestoreHelpers.findOne(
        COLLECTIONS.STUDENTS,
        { field: 'studentId', operator: '==', value: doctor }
      );
      if (!doctorDoc) {
        console.log(`No student found with ID: ${doctor}`);
        return res.status(404).json({ message: 'Student not found' });
      }
    } else {
      console.log('Invalid doctorModel:', doctorModel);
      return res.status(400).json({ message: 'Invalid doctorModel' });
    }

    console.log('Found doctor:', doctorDoc);

    // Check for existing appointments
    const allAppointments = await FirestoreHelpers.find(COLLECTIONS.APPOINTMENTS);
    const existingAppointment = allAppointments.find(apt =>
      apt.doctor === doctor &&
      apt.doctorModel === doctorModel &&
      new Date(apt.date).toDateString() === appointmentDate.toDateString() &&
      apt.time === time &&
      apt.status !== 'cancelled'
    );

    if (existingAppointment) {
      console.log(`Slot already booked: ${date} ${time}`);
      return res.status(400).json({ message: 'This time slot is already booked' });
    }

    const appointmentData = {
      date: appointmentDate,
      time,
      type,
      notes: notes || '',
      status: status || 'pending',
      patient: patientDoc.id, // Use the Firestore document ID
      doctor: doctorDoc.studentId || doctor,
      doctorModel,
      chiefComplaint: chiefComplaint || '',
      occupation: occupation || '',
      address: address || '',
      nationalId: patientDoc.nationalId,
      fullName: patientDoc.fullName,
      phoneNumber: patientDoc.phoneNumber,
      age: patientDoc.age,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('Appointment data to save:', appointmentData);

    const savedAppointment = await FirestoreHelpers.create(COLLECTIONS.APPOINTMENTS, appointmentData);
    console.log('Appointment saved:', savedAppointment);

    // Update patient with appointment reference
    try {
      const updatedPatient = await FirestoreHelpers.update(
        COLLECTIONS.PATIENTS,
        patientDoc.id,
        {
          appointments: [...(patientDoc.appointments || []), savedAppointment.id],
          updatedAt: new Date()
        }
      );
      console.log('Patient updated with appointment reference');
    } catch (updateError) {
      console.error('Error updating patient with appointment reference:', updateError);
      // Don't fail the appointment creation if patient update fails
    }

    res.status(201).json(savedAppointment);
  } catch (error) {
    console.error('Error creating appointment:', error.message, error.stack);
    res.status(500).json({ message: `Server error: ${error.message}` });
  }
};

const createPatientAppointment = async (req, res) => {
  try {
    const { error: validationError } = patientAppointmentSchema.validate(req.body);
    if (validationError) {
      console.log('Validation error:', validationError.details);
      return res.status(400).json({ message: validationError.details[0].message });
    }

    const {
      date,
      time,
      type,
      patient,
      fullName,
      phoneNumber,
      age,
      chiefComplaint,
      duration,
      universityClinic,
      isPatientInitiated,
      occupation,
      address,
    } = req.body;

    const patientDoc = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: patient }
    );
    const university = await FirestoreHelpers.findOne(
      COLLECTIONS.UNIVERSITIES,
      { field: 'universityId', operator: '==', value: universityClinic }
    );
    if (!university) {
      console.log(`No university found with universityId: ${universityClinic}`);
      return res.status(404).json({ message: `University '${universityClinic}' not found` });
    }

    const slotDate = new Date(date);
    const slot = university.timeSlots.find(
      (s) =>
        s.date.toISOString().slice(0, 10) === slotDate.toISOString().slice(0, 10) &&
        s.time === time &&
        s.isAvailable
    );

    if (!slot) {
      console.log(`Slot not available: ${date} ${time} at ${universityClinic}`);
      return res.status(400).json({ message: 'Selected slot is not available' });
    }

    slot.isAvailable = false;
    await FirestoreHelpers.update(
      COLLECTIONS.UNIVERSITIES,
      university._id,
      {
        timeSlots: university.timeSlots.map(s => s._id === slot._id ? slot : s)
      }
    );

    const appointmentData = {
      date: slotDate,
      time,
      type,
      patient: patientDoc.id,
      fullName: patientDoc.fullName,
      phoneNumber: patientDoc.phoneNumber,
      age: patientDoc.age,
      chiefComplaint,
      duration,
      university: universityClinic,
      isPatientInitiated,
      status: 'pending',
      occupation,
      address,
      nationalId: patientDoc.nationalId,
      gender: patientDoc.gender,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const savedAppointment = await FirestoreHelpers.create(COLLECTIONS.APPOINTMENTS, appointmentData);
    console.log('Patient-initiated appointment saved:', savedAppointment);

    if (patientDoc) {
      await FirestoreHelpers.update(
        COLLECTIONS.PATIENTS,
        patientDoc.id,
        {
          appointments: [...(patientDoc.appointments || []), savedAppointment.id],
          updatedAt: new Date()
        }
      );
    }

    const populatedAppointment = await FirestoreHelpers.findOne(COLLECTIONS.APPOINTMENTS, savedAppointment.id);

    res.status(201).json(populatedAppointment);
  } catch (error) {
    console.error('Error creating patient appointment:', error.message, error.stack);
    res.status(500).json({ message: `Server error: ${error.message}` });
  }
};

const getAppointmentsByDoctor = async (req, res) => {
  try {
    // Get the doctor ID from the user object
    const doctorId = req.user.studentId || req.user.dentistId;
    const doctorModel = req.user.role === 'dentist' ? 'Dentist' : 'Student';

    console.log(`Fetching appointments for ${doctorModel} with ID: ${doctorId}`);

    // Find all appointments for this doctor, including both doctor-initiated and patient-initiated
    const appointments = await FirestoreHelpers.find(COLLECTIONS.APPOINTMENTS, [
      {
        field: 'doctor',
        operator: '==',
        value: doctorId,
      },
      {
        field: 'doctorModel',
        operator: '==',
        value: doctorModel,
      }
    ]);

    console.log(`Found ${appointments.length} appointments for ${req.user.role} ${doctorId}`);
    res.json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const getAppointmentsByPatient = async (req, res) => {
  try {
    const { nationalId } = req.params;

    const patient = await FirestoreHelpers.findOne(COLLECTIONS.PATIENTS, { field: 'nationalId', operator: '==', value: nationalId });
    let appointments;
    if (patient) {
      appointments = await FirestoreHelpers.find(COLLECTIONS.APPOINTMENTS, {
        field: 'patient',
        operator: '==',
        value: patient.id,
      });
    } else {
      // For patient-initiated appointments, get all and filter
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      appointments = allAppointments.filter(appt => 
        appt.nationalId === nationalId && 
        appt.isPatientInitiated === true
      );
    }

    console.log(`Appointments for patient ${nationalId}:`, appointments.length);
    res.status(200).json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAvailableSlots = async (req, res) => {
  try {
    const { doctorId, doctorModel, date } = req.query;

    if (!doctorId || !doctorModel || !date) {
      console.log('Missing query parameters:', { doctorId, doctorModel, date });
      return res.status(400).json({ message: 'doctorId, doctorModel, and date are required' });
    }

    if (!['Student', 'Dentist'].includes(doctorModel)) {
      console.log('Invalid doctorModel:', doctorModel);
      return res.status(400).json({ message: 'Invalid doctorModel. Must be Student or Dentist' });
    }

    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      console.log('Invalid date format:', date);
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    let doctor;
    if (doctorModel === 'Student') {
      doctor = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { field: 'studentId', operator: '==', value: doctorId });
    } else {
      doctor = await FirestoreHelpers.findOne(COLLECTIONS.DENTISTS, { field: 'dentistId', operator: '==', value: doctorId });
    }

    if (!doctor) {
      console.log(`No ${doctorModel} found with ID: ${doctorId}`);
      return res.status(404).json({ message: `No ${doctorModel} found with ID ${doctorId}` });
    }

    const targetDate = new Date(date);
    if (isNaN(targetDate)) {
      console.log('Invalid date:', date);
      return res.status(400).json({ message: 'Invalid date' });
    }
    targetDate.setHours(0, 0, 0, 0);

    const slots = [
      '9:00 - 11:00',
      '11:30 - 13:30',
      '14:00 - 16:00',
    ];

    const startOfDay = new Date(targetDate);
    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    // For now, let's get all appointments and filter in memory to avoid complex Firestore queries
    const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
    
    const bookedAppointments = allAppointments.filter(appt => 
      appt.doctor === doctorId &&
      appt.doctorModel === doctorModel &&
      new Date(appt.date) >= startOfDay &&
      new Date(appt.date) <= endOfDay &&
      appt.status !== 'cancelled'
    );

    console.log('Booked appointments:', bookedAppointments);

    const bookedTimes = bookedAppointments.map((appt) => appt.time);

    const availableSlots = slots.filter((slot) => {
      const slotStartTime = slot.split(' - ')[0];
      return !bookedTimes.includes(slotStartTime);
    });

    console.log(`Available slots for ${doctorModel} ${doctorId} on ${date}:`, availableSlots);
    res.status(200).json(availableSlots);
  } catch (error) {
    console.error('Error in getAvailableSlots:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAllAppointments = async (req, res) => {
  try {
    if (!['superadmin', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { university } = req.query;
    const query = university ? { university } : {};

    const appointments = await FirestoreHelpers.find(COLLECTIONS.APPOINTMENTS, query)
      .populate('patient', 'nationalId fullName phoneNumber gender age address occupation');

    console.log(`Fetched ${appointments.length} appointments${university ? ` for university: ${university}` : ''}`);
    res.json(appointments);
  } catch (error) {
    console.error('Error fetching all appointments:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const deleteAppointment = async (req, res) => {
  try {
    const appointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, req.params.id);
    if (!appointment) return res.status(404).json({ message: 'Appointment not found' });
    if (appointment.isPatientInitiated && !['superadmin', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    if (appointment.doctorModel === 'Dentist') {
      // For now, skip the timeSlots update as it's complex to implement in Firestore
      console.log('Skipping timeSlots update for dentist appointment deletion');
    }
    if (appointment.patient) {
      // Get the patient and remove the appointment from their appointments array
      const patient = await FirestoreHelpers.findById(COLLECTIONS.PATIENTS, appointment.patient);
      if (patient && patient.appointments) {
        const updatedAppointments = patient.appointments.filter(aptId => aptId !== appointment.id);
        await FirestoreHelpers.update(
          COLLECTIONS.PATIENTS,
          appointment.patient,
          { appointments: updatedAppointments }
        );
      }
    }
    await FirestoreHelpers.delete(COLLECTIONS.APPOINTMENTS, req.params.id);
    res.json({ message: 'Appointment deleted' });
  } catch (error) {
    console.error('Error deleting appointment:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const downloadSchedule = async (req, res) => {
  try {
    // Check if university query parameter is provided
    const { university } = req.query;

    if (university) {
          // If university is provided, fetch appointments for that university
    const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
    const appointments = allAppointments.filter(appt => 
      appt.university === university && 
      appt.status !== 'cancelled'
    );

      console.log(`Fetched ${appointments.length} appointments for university: ${university}`);
      return res.json(appointments);
    }

    // If no university provided, use the assistant's affiliation
    const { assistantId, dentistId } = req.user;
    const assistant = await FirestoreHelpers.findOne(COLLECTIONS.ASSISTANTS, { assistantId: assistantId || dentistId });
    if (!assistant) return res.status(404).json({ message: 'Assistant not found' });

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    let appointments;
    if (assistant.affiliation?.type === 'dentist') {
      const dentist = await FirestoreHelpers.findOne(COLLECTIONS.DENTISTS, { dentistId: assistant.affiliation.id });
      if (!dentist) return res.status(404).json({ message: 'Dentist not found' });
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      appointments = allAppointments.filter(appt => 
        appt.doctor === dentist.dentistId &&
        appt.doctorModel === 'Dentist' &&
        new Date(appt.date) >= today &&
        new Date(appt.date) < tomorrow &&
        appt.status !== 'cancelled'
      );
    } else {
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      appointments = allAppointments.filter(appt => 
        appt.university === (assistant.affiliation?.id || assistant.university) &&
        appt.status !== 'cancelled'
      );

      // Log the first appointment to see its structure
      if (appointments.length > 0) {
        console.log('First appointment data from backend:', {
          _id: appointments[0]._id,
          patient: appointments[0].patient,
          nationalId: appointments[0].nationalId || (appointments[0].patient && appointments[0].patient.nationalId),
          fullName: appointments[0].fullName || (appointments[0].patient && appointments[0].patient.fullName)
        });
      }
    }

    res.json(appointments);
  } catch (error) {
    console.error('Error downloading schedule:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const remindPatients = async (req, res) => {
  try {
    const { assistantId } = req.user;
    const assistant = await FirestoreHelpers.findOne(COLLECTIONS.ASSISTANTS, { assistantId });
    if (!assistant) return res.status(404).json({ message: 'Assistant not found' });

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    const dayAfter = new Date(tomorrow);
    dayAfter.setDate(dayAfter.getDate() + 1);

    let appointments;
    if (assistant.affiliation.type === 'dentist') {
      const dentist = await FirestoreHelpers.findOne(COLLECTIONS.DENTISTS, { dentistId: assistant.affiliation.id });
      if (!dentist) return res.status(404).json({ message: 'Dentist not found' });
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      appointments = allAppointments.filter(appt => 
        appt.doctor === dentist.dentistId &&
        appt.doctorModel === 'Dentist' &&
        new Date(appt.date) >= tomorrow &&
        new Date(appt.date) < dayAfter &&
        appt.status !== 'cancelled'
      );
    } else {
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      appointments = allAppointments.filter(appt => 
        appt.university === assistant.affiliation.id &&
        new Date(appt.date) >= tomorrow &&
        new Date(appt.date) < dayAfter &&
        appt.status !== 'cancelled'
      );
    }

    const reminders = appointments.map((appt) => ({
      patient: appt.patient ? appt.patient.fullName : appt.fullName,
      phoneNumber: appt.patient ? appt.patient.phoneNumber : appt.phoneNumber,
      date: appt.date,
      time: appt.time,
    }));

    res.json({ message: 'Reminders queued', reminders });
  } catch (error) {
    console.error('Error reminding patients:', error.message, error.stack);
    res.status(500).json({ message: error.message });
  }
};

const getUniversityAvailableSlots = async (req, res) => {
  try {
    const { university, date } = req.query;
    if (!university || !date) {
      console.log('Missing query parameters:', { university, date });
      return res.status(400).json({ message: 'University and date are required' });
    }

    const parsedDate = new Date(date);
    if (isNaN(parsedDate)) {
      console.log('Invalid date format:', date);
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    const uni = await FirestoreHelpers.findOne(COLLECTIONS.UNIVERSITIES, { universityId: university });
    if (!uni) {
      console.log(`No university found with universityId: ${university}`);
      return res.status(404).json({ message: `University '${university}' not found` });
    }

    if (!uni.timeSlots || !Array.isArray(uni.timeSlots)) {
      console.log(`No time slots defined for university: ${university}`);
      return res.status(400).json({ message: `No time slots defined for university '${university}'` });
    }

    const startOfDay = new Date(parsedDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(parsedDate);
    endOfDay.setHours(23, 59, 59, 999);

    const availableSlots = uni.timeSlots
      .filter((slot) => {
        const slotDate = new Date(slot.date);
        return slot.isAvailable && slotDate >= startOfDay && slotDate <= endOfDay;
      })
      .map((slot) => {
        const endTime = new Date(`1970-01-01T${slot.time}:00Z`);
        endTime.setMinutes(endTime.getMinutes() + (slot.duration || 60));
        const endTimeStr = endTime.toISOString().slice(11, 16);
        return `${slot.time} - ${endTimeStr}`;
      });

    const uniqueSlots = [...new Set(availableSlots)];
    console.log(`Available slots for ${university} on ${date}:`, uniqueSlots);

    res.json(uniqueSlots);
  } catch (error) {
    console.error('Error fetching university available slots:', error.message, error.stack);
    res.status(500).json({ message: `Server error: ${error.message}` });
  }
};

// Assign a student to an appointment
const assignAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const { doctor, doctorModel } = req.body;

    console.log('Assign appointment request:', { id, doctor, doctorModel });

    if (!doctor || !doctorModel) {
      return res.status(400).json({ message: 'Doctor ID and doctor model are required' });
    }

    // Validate that the doctor exists
    let doctorDoc;
    if (doctorModel === 'Student') {
      doctorDoc = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { studentId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Student not found' });
      }
    } else if (doctorModel === 'Dentist') {
      doctorDoc = await FirestoreHelpers.findOne(COLLECTIONS.DENTISTS, { dentistId: doctor });
      if (!doctorDoc) {
        return res.status(404).json({ message: 'Dentist not found' });
      }
    } else {
      return res.status(400).json({ message: 'Invalid doctor model' });
    }

    // Find and update the appointment
    console.log('Looking for appointment with ID:', id);
    const appointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, id);
    console.log('Found appointment:', appointment ? 'Yes' : 'No');
    if (!appointment) {
      console.log('Appointment not found with ID:', id);
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Check if the student is already assigned to another appointment at the same time
    if (doctorModel === 'Student') {
      const allAppointments = await FirestoreHelpers.getAll(COLLECTIONS.APPOINTMENTS);
      const existingAppointment = allAppointments.find(appt => 
        appt.doctor === doctor &&
        appt.doctorModel === 'Student' &&
        new Date(appt.date).toDateString() === new Date(appointment.date).toDateString() &&
        appt.time === appointment.time &&
        appt.status !== 'cancelled' &&
        appt.id !== id // Exclude the current appointment
      );

      if (existingAppointment) {
        return res.status(400).json({
          message: 'Student already has an appointment at this time',
          conflict: true
        });
      }
    }

    // Update the appointment
    appointment.doctor = doctor;
    appointment.doctorModel = doctorModel;

    // If assigning to a student, add student name information
    if (doctorModel === 'Student' && doctorDoc) {
      appointment.studentName = doctorDoc.name;
      appointment.studentId = doctorDoc.studentId;
    }

    // If the appointment has a patient, make sure the nationalId is set
    if (appointment.patient) {
      const patientDoc = await FirestoreHelpers.findOne(COLLECTIONS.PATIENTS, appointment.patient);
      if (patientDoc && patientDoc.nationalId) {
        appointment.nationalId = patientDoc.nationalId;
        console.log(`Setting nationalId ${patientDoc.nationalId} on appointment ${id}`);
      }
    }

    const updatedAppointment = await FirestoreHelpers.update(COLLECTIONS.APPOINTMENTS, id, appointment);

    // If this is a student assignment, update the student's appointments list if needed
    if (doctorModel === 'Student') {
      // Check if the student has an appointments array
      const student = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { studentId: doctor });
      if (student) {
        // Check if the appointment is already in the student's list
        if (!student.appointments || !student.appointments.includes(updatedAppointment.id)) {
          const updatedAppointments = [...(student.appointments || []), updatedAppointment.id];
          await FirestoreHelpers.update(
            COLLECTIONS.STUDENTS,
            student.id,
            { appointments: updatedAppointments }
          );
          console.log(`Added appointment ${updatedAppointment.id} to student ${doctor}'s appointments list`);
        }

        // If the appointment has a patient, add the patient to the student's patients list
        if (appointment.patient) {
          // Get the patient document
          const patientDoc = await FirestoreHelpers.findOne(COLLECTIONS.PATIENTS, appointment.patient);
          if (patientDoc) {
            // Check if the patient is already in the student's patients list
            if (!student.patients || !student.patients.includes(patientDoc.id)) {
              const updatedPatients = [...(student.patients || []), patientDoc.id];
              await FirestoreHelpers.update(
                COLLECTIONS.STUDENTS,
                student.id,
                { patients: updatedPatients }
              );
              console.log(`Added patient ${patientDoc.id} to student ${doctor}'s patients list`);
            }
          }
        }
      }
    }

    // Return the updated appointment
    const populatedAppointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, updatedAppointment.id);

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error assigning appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update an appointment with a patient ID
const updateAppointmentPatient = async (req, res) => {
  try {
    const { id } = req.params;
    const { patientId } = req.body;

    if (!patientId) {
      return res.status(400).json({ message: 'Patient ID is required' });
    }

    // Find the appointment
    const appointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Validate that the patient exists
    const patient = await FirestoreHelpers.findById(COLLECTIONS.PATIENTS, patientId);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Update the appointment
    appointment.patient = patientId;

    // Update appointment with patient details
    appointment.nationalId = patient.nationalId;
    appointment.fullName = patient.fullName;
    appointment.phoneNumber = patient.phoneNumber;
    appointment.age = patient.age;
    appointment.gender = patient.gender;
    appointment.address = patient.address || appointment.address;
    appointment.occupation = patient.occupation || appointment.occupation;

    console.log(`Updating appointment ${appointment.id} with patient details:`, {
      nationalId: patient.nationalId,
      fullName: patient.fullName,
      phoneNumber: patient.phoneNumber
    });

    const updatedAppointment = await FirestoreHelpers.update(COLLECTIONS.APPOINTMENTS, id, appointment);

    // Add the appointment to the patient's appointments list if not already there
    if (!patient.appointments || !patient.appointments.includes(updatedAppointment.id)) {
      const updatedAppointments = [...(patient.appointments || []), updatedAppointment.id];
      await FirestoreHelpers.update(
        COLLECTIONS.PATIENTS,
        patientId,
        { appointments: updatedAppointments }
      );
      console.log(`Added appointment ${updatedAppointment.id} to patient ${patientId}'s appointments list`);
    }

    // Return the updated appointment
    const populatedAppointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, updatedAppointment.id);

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error updating appointment patient:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update an appointment
const updateAppointment = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Find the appointment
    const appointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, id);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Check if nationalId is being updated
    if (updates.nationalId) {
      console.log(`Updating nationalId for appointment ${id} to ${updates.nationalId}`);
    }

    // Check if doctor is being updated to a student
    if (updates.doctor && updates.doctorModel === 'Student') {
      // Find the student to get their name - doctor field contains the studentId
      const student = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { studentId: updates.doctor });
      if (student) {
        updates.studentName = student.name;
        updates.studentId = student.studentId;
        console.log(`Setting student information for appointment ${id}: ${student.name} (${student.studentId})`);
      } else {
        console.log(`No student found with studentId ${updates.doctor} for appointment ${id}`);
      }
    }

    // Apply updates
    Object.keys(updates).forEach(key => {
      appointment[key] = updates[key];
    });

    const updatedAppointment = await FirestoreHelpers.update(COLLECTIONS.APPOINTMENTS, id, appointment);
    console.log(`Appointment ${id} updated successfully`);

    // Return the updated appointment
    const populatedAppointment = await FirestoreHelpers.findById(COLLECTIONS.APPOINTMENTS, updatedAppointment.id);

    res.json(populatedAppointment);
  } catch (error) {
    console.error('Error updating appointment:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createAppointment,
  getAvailableSlots,
  getAppointmentsByDoctor,
  getAppointmentsByPatient,
  getAllAppointments,
  deleteAppointment,
  downloadSchedule,
  remindPatients,
  createPatientAppointment,
  getUniversityAvailableSlots,
  assignAppointment,
  updateAppointmentPatient,
  updateAppointment,
};
