import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaUsers, FaUserAlt, FaUserNurse, FaUserMd, FaNotesMedical, FaCalendarAlt, FaUserGraduate } from 'react-icons/fa';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

// Helper function to format appointment date and time in Cairo timezone
const formatAppointmentDateTime = (appointment) => {
  try {
    // Get the date from the appointment
    const date = new Date(appointment.start || appointment.date || appointment.appointmentDate);

    // Format the date in Cairo timezone (UTC+2)
    const dateOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Africa/Cairo'
    };
    const formattedDate = date.toLocaleDateString('en-US', dateOptions);

    // Get the time from the appointment object or use the date's time
    let timeString = appointment.time;

    // If time is not available in the appointment object, format the time from the date
    if (!timeString) {
      const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
        timeZone: 'Africa/Cairo'
      };
      timeString = date.toLocaleTimeString('en-US', timeOptions);
    }

    return `${formattedDate} at ${timeString}`;
  } catch (error) {
    console.error('Error formatting appointment date:', error);
    return 'Date not available';
  }
};

const People = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [category, setCategory] = useState('patients');
  const [data, setData] = useState([]);
  const [students, setStudents] = useState([]); // Store students for mapping drId
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null); // Store detailed patient info
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  const categories = [
    { value: 'patients', label: 'Patients', endpoint: '/api/admin/patients', icon: <FaUserAlt /> },
    { value: 'students', label: 'Students', endpoint: '/api/admin/students', icon: <FaUsers /> },
    { value: 'assistants', label: 'Assistants', endpoint: '/api/admin/assistants', icon: <FaUserNurse /> },
    { value: 'supervisors', label: 'Supervisors', endpoint: '/api/admin/supervisors', icon: <FaUserMd /> },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view data.');
        setLoading(false);
        return;
      }

      if (!user.university) {
        setError('User profile incomplete. Missing university information.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const endpoint = categories.find((c) => c.value === category).endpoint;

        // Fetch category data
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL}${endpoint}?university=${encodeURIComponent(user.university)}`,
          config
        );
        setData(response.data || []);

        // Fetch students for mapping drId (only for patients category)
        if (category === 'patients') {
          const studentsResponse = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/students?university=${encodeURIComponent(user.university)}`,
            config
          );
          setStudents(studentsResponse.data || []);
        }

        // Remove the error message for no data found
        // if (response.data.length === 0) setError(`No ${category} found for your university.`);
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage =
          err.response?.status === 404
            ? `${category} endpoint not found.`
            : err.response?.status === 401
            ? 'Unauthorized. Please log in again.'
            : err.response?.data?.message || `Failed to load ${category}`;
        setError(errorMessage);
        if (err.response?.status === 401) navigate('/login');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate, category]);

  // Fetch detailed patient info when a patient is selected
  useEffect(() => {
    const fetchPatientDetails = async () => {
      if (selectedPerson && category === 'patients' && selectedPerson.nationalId) {
        try {
          const config = { headers: { Authorization: `Bearer ${token}` } };
          // Use the public endpoint to get patient details
          const patientResponse = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/patients/public/${selectedPerson.nationalId}`,
            config
          );

          // Fetch appointments separately
          const appointmentsResponse = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/appointments/patient/${selectedPerson.nationalId}`,
            config
          );

          console.log('Patient details:', patientResponse.data);
          console.log('Patient appointments:', appointmentsResponse.data);

          // Combine the data
          const patientData = {
            ...patientResponse.data,
            appointments: appointmentsResponse.data || []
          };

          setPatientDetails(patientData);
        } catch (err) {
          console.error('Error fetching patient details:', err.response?.data || err.message);
          setError('Failed to load patient details.');
        }
      }
    };
    fetchPatientDetails();
  }, [selectedPerson, category, token]);

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  const renderTableHeaders = () => {
    switch (category) {
      case 'patients':
        return (
          <>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">National ID</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Student</th>
          </>
        );
      case 'students':
        return (
          <>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
          </>
        );
      default:
        return (
          <>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
          </>
        );
    }
  };

  const renderTableRow = (person) => {
    switch (category) {
      case 'patients':
        return (
          <>
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.fullName}</td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{person.nationalId}</td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {students.find((s) => s._id === person.drId)?.name || 'None'}
            </td>
          </>
        );
      case 'students':
        return (
          <>
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.name}</td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{person.email}</td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{person.studentId}</td>
          </>
        );
      default:
        return (
          <>
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.name}</td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{person.email}</td>
          </>
        );
    }
  };

  const renderModalContent = () => {
    if (!selectedPerson) return null;
    if (category === 'patients' && patientDetails) {
      return (
        <div className="space-y-6">
          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaUserAlt className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Personal Information
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Full Name</h4>
                  <p className="text-sm text-gray-900 mt-1 font-medium">{patientDetails.fullName}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                  <p className="text-sm text-gray-900 mt-1">{patientDetails.nationalId}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Phone</h4>
                  <p className="text-sm text-gray-900 mt-1">{patientDetails.phoneNumber || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Age</h4>
                  <p className="text-sm text-gray-900 mt-1">{patientDetails.age || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Address</h4>
                  <p className="text-sm text-gray-900 mt-1">{patientDetails.personalInfo?.address || patientDetails.address || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Occupation</h4>
                  <p className="text-sm text-gray-900 mt-1">{patientDetails.personalInfo?.occupation || patientDetails.occupation || 'N/A'}</p>
                </div>
                <div className="md:col-span-2">
                  <h4 className="text-sm font-medium text-gray-500">Assigned Student</h4>
                  <p className="text-sm text-gray-900 mt-1 font-medium">{students.find((s) => s._id === patientDetails.drId)?.name || 'None'}</p>
                </div>
              </div>
            </div>
          </div>

          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaNotesMedical className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Medical History
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Chronic Diseases</h4>
                  {patientDetails.medicalInfo?.chronicDiseases?.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {patientDetails.medicalInfo.chronicDiseases.map((disease, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                          {disease}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">None reported</p>
                  )}
                </div>

                <div className="pt-2 border-t border-gray-100">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Recent Surgical Procedures</h4>
                  {patientDetails.medicalInfo?.recentSurgicalProcedures ? (
                    <p className="text-sm text-gray-900">{patientDetails.medicalInfo.recentSurgicalProcedures}</p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">None reported</p>
                  )}
                </div>

                <div className="pt-2 border-t border-gray-100">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Current Medications</h4>
                  {patientDetails.medicalInfo?.currentMedications ? (
                    <p className="text-sm text-gray-900">{patientDetails.medicalInfo.currentMedications}</p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">None reported</p>
                  )}
                </div>

                <div className="pt-2 border-t border-gray-100">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Chief Complaint</h4>
                  {patientDetails.medicalInfo?.chiefComplaint ? (
                    <p className="text-sm text-gray-900">{patientDetails.medicalInfo.chiefComplaint}</p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">None reported</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaCalendarAlt className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Appointments
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              {patientDetails.appointments?.length > 0 ? (
                <div className="divide-y divide-gray-100">
                  {patientDetails.appointments.map((appt, index) => (
                    <div key={appt._id || index} className="py-3 first:pt-0 last:pb-0">
                      <div className="flex items-start">
                        <div className="bg-blue-100 rounded-full p-2 mr-3 mt-1">
                          <FaCalendarAlt className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div className="font-medium text-gray-900">{appt.title || appt.description || appt.type || 'Appointment'}</div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              appt.status === 'completed' ? `bg-green-100 text-[${websiteColorPalette.accent}]` :
                              appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'
                            }`}>
                              {appt.status ? appt.status.charAt(0).toUpperCase() + appt.status.slice(1) : 'Pending'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mt-1 flex items-center">
                            <svg className="h-3 w-3 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            {formatAppointmentDateTime(appt)}
                          </div>
                          {appt.studentName && (
                            <div className="text-xs text-gray-500 mt-1">
                              Student: {appt.studentName}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-6 text-center">
                  <svg className="mx-auto h-10 w-10 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p className="mt-2 text-sm text-gray-500 italic">No appointments scheduled.</p>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
              <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
                <FaNotesMedical className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                X-Rays
              </h4>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                {patientDetails.xrays?.length > 0 ? (
                  <div className="divide-y divide-gray-100">
                    {patientDetails.xrays.map((xray, index) => (
                      <div key={index} className="py-3 first:pt-0 last:pb-0">
                        <div className="flex items-start">
                          <div className="bg-blue-100 rounded-full p-2 mr-3 mt-1 flex-shrink-0">
                            <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">X-Ray {index + 1}</div>
                            {xray.note && <div className="text-sm text-gray-600 mt-1">{xray.note}</div>}
                            <div className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="h-3 w-3 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                              </svg>
                              Uploaded: {new Date(xray.date || xray.uploadedAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-6 text-center">
                    <svg className="mx-auto h-10 w-10 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="mt-2 text-sm text-gray-500 italic">No X-Rays available.</p>
                  </div>
                )}
              </div>
            </div>

            <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
              <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
                <FaNotesMedical className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                Gallery Images
              </h4>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                {patientDetails.galleryImages?.length > 0 ? (
                  <div className="divide-y divide-gray-100">
                    {patientDetails.galleryImages.map((image, index) => (
                      <div key={index} className="py-3 first:pt-0 last:pb-0">
                        <div className="flex items-start">
                          <div className="bg-blue-100 rounded-full p-2 mr-3 mt-1 flex-shrink-0">
                            <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">Image {index + 1}</div>
                            {image.note && <div className="text-sm text-gray-600 mt-1">{image.note}</div>}
                            <div className="text-xs text-gray-500 mt-1 flex items-center">
                              <svg className="h-3 w-3 text-gray-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                              </svg>
                              Uploaded: {new Date(image.date || image.uploadedAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-6 text-center">
                    <svg className="mx-auto h-10 w-10 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p className="mt-2 text-sm text-gray-500 italic">No gallery images available.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }
    if (category === 'students') {
      return (
        <div className="space-y-6">
          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaUserGraduate className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Student Information
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Name</h4>
                  <p className="text-sm text-gray-900 mt-1 font-medium">{selectedPerson.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Email</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.email}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Student ID</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.studentId}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">University</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.university || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Created At</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.createdAt ? new Date(selectedPerson.createdAt).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Last Updated</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.updatedAt ? new Date(selectedPerson.updatedAt).toLocaleDateString() : 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    if (category === 'assistants') {
      return (
        <div className="space-y-6">
          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaUserNurse className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Assistant Information
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Name</h4>
                  <p className="text-sm text-gray-900 mt-1 font-medium">{selectedPerson.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Email</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.email}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">University</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.university || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Clinic</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.clinic || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Created At</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.createdAt ? new Date(selectedPerson.createdAt).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Last Updated</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.updatedAt ? new Date(selectedPerson.updatedAt).toLocaleDateString() : 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    if (category === 'supervisors') {
      return (
        <div className="space-y-6">
          <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
            <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
              <FaUserMd className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
              Supervisor Information
            </h4>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Name</h4>
                  <p className="text-sm text-gray-900 mt-1 font-medium">{selectedPerson.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Email</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.email}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">University</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.university || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Specialty</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.specialty || 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Created At</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.createdAt ? new Date(selectedPerson.createdAt).toLocaleDateString() : 'N/A'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Last Updated</h4>
                  <p className="text-sm text-gray-900 mt-1">{selectedPerson.updatedAt ? new Date(selectedPerson.updatedAt).toLocaleDateString() : 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    return (
      <div className="space-y-6">
        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
          <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-4 flex items-center`}>
            <FaUserAlt className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
            Person Information
          </h4>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Name</h4>
                <p className="text-sm text-gray-900 mt-1 font-medium">{selectedPerson.name}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Email</h4>
                <p className="text-sm text-gray-900 mt-1">{selectedPerson.email}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">University</h4>
                <p className="text-sm text-gray-900 mt-1">{selectedPerson.university || 'N/A'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Created At</h4>
                <p className="text-sm text-gray-900 mt-1">{selectedPerson.createdAt ? new Date(selectedPerson.createdAt).toLocaleDateString() : 'N/A'}</p>
              </div>
              {selectedPerson.updatedAt && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Last Updated</h4>
                  <p className="text-sm text-gray-900 mt-1">{new Date(selectedPerson.updatedAt).toLocaleDateString()}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>People</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Manage people in your university</p>
              </div>
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                    <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                      <FaUsers className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                      {categories.find((c) => c.value === category).label} List
                    </h2>
                    <select
                      value={category}
                      onChange={(e) => {
                        setCategory(e.target.value);
                        setData([]);
                        setError('');
                        setStudents([]);
                        setPatientDetails(null);
                      }}
                      className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                    >
                      {categories.map((cat) => (
                        <option key={cat.value} value={cat.value}>
                          {cat.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>{renderTableHeaders()}</tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {data.length === 0 ? (
                          <tr>
                            <td colSpan={category === 'patients' || category === 'students' ? 3 : 2} className="px-3 sm:px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mb-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                                <h3 className="text-base sm:text-lg font-medium text-gray-900">No {category} found</h3>
                                <p className="mt-1 text-gray-500 text-center text-sm">No {category} registered in your university.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          data.map((person) => (
                            <motion.tr
                              key={person._id}
                              variants={item}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => setSelectedPerson(person)}
                            >
                              {renderTableRow(person)}
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
            {selectedPerson && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.9, opacity: 0, y: 20 }}
                  animate={{ scale: 1, opacity: 1, y: 0 }}
                  exit={{ scale: 0.9, opacity: 0, y: 20 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="bg-white rounded-xl shadow-lg p-0 max-w-4xl w-full mx-4 overflow-hidden border border-gray-200 max-h-[85vh]"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className={`bg-gradient-to-r from-[${websiteColorPalette.primary}] to-[${websiteColorPalette.secondary}] p-4 sm:p-5 text-white`}>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        {category === 'patients' && <FaUserAlt className="h-5 w-5 sm:h-6 sm:w-6 mr-3 text-blue-200" />}
                        {category === 'students' && <FaUserGraduate className="h-5 w-5 sm:h-6 sm:w-6 mr-3 text-blue-200" />}
                        {category === 'assistants' && <FaUserNurse className="h-5 w-5 sm:h-6 sm:w-6 mr-3 text-blue-200" />}
                        {category === 'supervisors' && <FaUserMd className="h-5 w-5 sm:h-6 sm:w-6 mr-3 text-blue-200" />}
                        <h3 className="text-lg sm:text-xl font-bold">
                          {category === 'patients'
                            ? 'Patient Details'
                            : category === 'students'
                            ? 'Student Details'
                            : category === 'assistants'
                            ? 'Assistant Details'
                            : category === 'supervisors'
                            ? 'Supervisor Details'
                            : 'Person Details'}
                        </h3>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedPerson(null);
                          setPatientDetails(null);
                        }}
                        className={`text-white hover:text-blue-200 transition-colors bg-[${websiteColorPalette.primary}] hover:bg-blue-600 rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div className="p-4 sm:p-6 overflow-y-auto max-h-[calc(85vh-130px)]">
                    <div className="space-y-4 sm:space-y-6">{renderModalContent()}</div>
                  </div>
                  <div className="border-t border-gray-200 p-4 sm:p-5 flex justify-end bg-gray-50">
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={() => {
                        setSelectedPerson(null);
                        setPatientDetails(null);
                      }}
                      className={`px-4 sm:px-5 py-2 sm:py-2.5 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm flex items-center font-medium text-sm`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      Close
                    </motion.button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default People;