const admin = require('firebase-admin');
require('dotenv').config();

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAaJKFcxipd7SBS-GQK97ZIFr0oBBEKQOU",
  authDomain: "odenta-82359.firebaseapp.com",
  projectId: "odenta-82359",
  storageBucket: "odenta-82359.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:12ccba8515c9648b1d8941",
  measurementId: "G-HFXCKFE42Y"
};

// Initialize Firebase Admin SDK
let firebaseApp;

const initializeFirebase = () => {
  try {
    if (!firebaseApp) {
      // For production, you should use a service account key
      // For development, you can use the application default credentials
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        // Parse the service account key from environment variable
        const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);

        firebaseApp = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: firebaseConfig.projectId,
          storageBucket: firebaseConfig.storageBucket
        });
      } else {
        // Try to load from serviceAccountKey.json file
        const fs = require('fs');
        const path = require('path');
        // Support both legacy and new service account file names
        let serviceAccountPath = path.join(__dirname, '..', 'serviceAccountKey.json');
        if (!fs.existsSync(serviceAccountPath)) {
          // Try the new file name
          serviceAccountPath = path.join(__dirname, '..', 'odenta-82359-firebase-adminsdk-fbsvc-51ad42a6b1.json');
        }

        if (fs.existsSync(serviceAccountPath)) {
          console.log('🔑 Using service account from serviceAccountKey.json');
          const serviceAccount = require(serviceAccountPath);

          firebaseApp = admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: firebaseConfig.projectId,
            storageBucket: firebaseConfig.storageBucket
          });
        } else {
          // For development with public Firestore rules
          console.log('🔧 Initializing Firebase without credentials (development mode)');
          console.log('⚠️  Download service account key for full functionality');
          console.log('📁 Expected location: serviceAccountKey.json');
          firebaseApp = admin.initializeApp({
            projectId: firebaseConfig.projectId,
            storageBucket: firebaseConfig.storageBucket
          });
        }
      }

      console.log('✅ Firebase Admin SDK initialized successfully');
      console.log(`🔥 Project ID: ${firebaseConfig.projectId}`);
    }

    return firebaseApp;
  } catch (error) {
    console.error('❌ Firebase initialization error:', error.message);
    console.log('💡 For development, you can:');
    console.log('   1. Set FIREBASE_SERVICE_ACCOUNT_KEY environment variable');
    console.log('   2. Or use the development Firestore rules (firestore-dev.rules)');
    throw error;
  }
};

// Get Firestore database instance
const getFirestore = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.firestore();
};

// Get Firebase Auth instance
const getAuth = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.auth();
};

// Get Firebase Storage instance
const getStorage = () => {
  if (!firebaseApp) {
    initializeFirebase();
  }
  return admin.storage();
};

module.exports = {
  initializeFirebase,
  getFirestore,
  getAuth,
  getStorage,
  firebaseConfig
};
