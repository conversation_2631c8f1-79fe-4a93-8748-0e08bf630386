const mongoose = require('mongoose');
require('dotenv').config();

async function checkIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/dentlyzer');
    console.log('Connected to MongoDB');

    // Get the Dentist collection
    const db = mongoose.connection.db;
    const dentistCollection = db.collection('dentists');
    
    // Get all indexes on the Dentist collection
    const indexes = await dentistCollection.indexes();
    console.log('Indexes on dentists collection:', JSON.stringify(indexes, null, 2));

    // Close the connection
    await mongoose.connection.close();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkIndexes();
