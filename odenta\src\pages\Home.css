@tailwind base;
@tailwind components;
@tailwind utilities;
/* Custom styles that can't be easily done with Tailwind */
/* Custom styles and animations */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0px); }
  }

  @keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-pulse {
    animation: pulse 3s ease-in-out infinite;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* New color palette */
  :root {
    --primary: #0077B6;
    --secondary: #20B2AA;
    --background: #FFFFFF;
    --text: #333333;
    --accent: #28A745;
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .text-primary {
    color: var(--primary);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .text-secondary {
    color: var(--secondary);
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .text-accent {
    color: var(--accent);
  }

  .bg-gradient-primary {
    background: linear-gradient(to right, var(--primary), #0099CC);
  }

  .bg-gradient-secondary {
    background: linear-gradient(to right, var(--secondary), #5CDBCF);
  }

  .bg-gradient-radial {
    background: radial-gradient(circle 800px at 50% 60%, rgba(0, 119, 182, 0.15), rgba(32, 178, 170, 0.05), transparent);
  }

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .bg-gradient-radial {
      background: radial-gradient(circle 600px at 50% 60%, rgba(0, 119, 182, 0.15), rgba(32, 178, 170, 0.05), transparent);
    }
  }

  @media (max-width: 768px) {
    .bg-gradient-radial {
      background: radial-gradient(circle 400px at 50% 60%, rgba(0, 119, 182, 0.15), rgba(32, 178, 170, 0.05), transparent);
    }
  }

  /* About.css */
.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

/* Home.css */

/* Oval Image Styling */
.hero-image-oval {
  object-fit: cover; /* Ensures the image fits within the oval shape */
  aspect-ratio: 1 / 1; /* Perfect circle for futuristic look */
  border-radius: 50%; /* Creates the circular shape */
  max-width: 480px; /* Increased from 450px */
  max-height: 480px; /* Increased from 450px */
  border: 8px solid white; /* Keep the border but remove shadow */
  filter: saturate(1.1) brightness(1.05);
  transition: all 0.5s ease-in-out;
}

.hero-image-oval:hover {
  filter: saturate(1.2) brightness(1.1);
}

/* Background Circles for Hero */
.hero-circle {
  position: absolute;
  border-radius: 50%;
  z-index: -1; /* Places circles behind the image */
  transition: all 0.5s ease-in-out;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(2px);
}

.hero-outer-circle {
  width: 115%;
  height: 115%;
  background: linear-gradient(135deg, rgba(32, 178, 170, 0.12), rgba(32, 178, 170, 0.03));
  top: -7.5%;
  left: -7.5%;
  border: 1px solid rgba(32, 178, 170, 0.15);
  /* Removed animations to keep circle static */
}

.hero-inner-circle {
  width: 105%;
  height: 105%;
  background: linear-gradient(225deg, rgba(0, 119, 182, 0.1), rgba(0, 119, 182, 0.02));
  top: -2.5%;
  left: -2.5%;
  border: 1px solid rgba(0, 119, 182, 0.12);
  /* Removed animations to keep circle static */
}

/* Circle animations */
@keyframes pulse-slow {
  0% { transform: scale(1); opacity: 0.7; }
  100% { transform: scale(1.05); opacity: 1; }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate-reverse {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}

/* Ensure the hero image container is positioned relatively */
.hero-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  aspect-ratio: 1 / 1; /* Ensure container is a perfect square to maintain circle shape */
  width: 100%;
  max-width: 500px; /* Limit container size */
  margin-left: auto; /* Move the container to the right */
  margin-right: 0;
}

/* Workflow image styling */
.workflow-image {
  width: 100%;
  max-width: 625px; /* Half of 1250px */
  height: 0;
  padding-bottom: 68%; /* Aspect ratio for 1250x850 (850/1250 = 0.68 or 68%) */
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.workflow-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}