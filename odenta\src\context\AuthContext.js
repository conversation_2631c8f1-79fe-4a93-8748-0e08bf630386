import { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token') || null);

  useEffect(() => {
    if (token) {
      try {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        localStorage.setItem('token', token);
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        } else {
          // Fallback to decoded JWT if no stored user
          const decoded = jwtDecode(token);
          setUser({
            id: decoded.id,
            studentId: decoded.studentId || decoded.id, // Fallback to id if studentId not in JWT
            role: decoded.role,
            university: decoded.university || '', // Include university if in JWT
            name: decoded.name || 'Unknown', // Fallback name
          });
        }
      } catch (error) {
        console.error('Error decoding token:', error);
        logout(); // Clear invalid token
      }
    } else {
      delete axios.defaults.headers.common['Authorization'];
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setUser(null);
    }
  }, [token]);

  const login = async (token, userData) => {
    try {
      setToken(token);
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // Fetch role from backend to ensure it's up-to-date
      let role = userData.role;
      try {
        const res = await axios.get('/api/auth/role');
        if (res.data && res.data.role) {
          role = res.data.role;
        }
      } catch (roleErr) {
        console.warn('Could not fetch user role from backend:', roleErr);
      }
      // Prioritize userData from backend response
      const updatedUser = {
        id: userData.id,
        studentId: userData.studentId || userData.id, // Ensure studentId is set
        name: userData.name || 'Unknown',
        university: userData.university || '',
        role: role || 'student',
      };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Invalid token');
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete axios.defaults.headers.common['Authorization'];
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within an AuthProvider');
  return context;
};