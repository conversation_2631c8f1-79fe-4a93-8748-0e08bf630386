const express = require('express');
const router = express.Router();
const { createPatient, getPatientById, updatePatient, addXray, updatePatientMedicalInfo, addGalleryImage,
    deletePatient, updateXrayNote, updateGalleryImageNote, deleteXray, deleteGalleryImage, getPatientsByDoctor,
    addTreatmentSheet, getTreatmentSheets, updateTreatmentSheet, deleteTreatmentSheet, getTreatmentSheetByType,
    updatePatientConsent, getPatientsByUniversity } = require('../controllers/patientController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');
const { upload } = require('../middleware/upload');

router.post('/', auth, role('student', 'assistant', 'superadmin'), upload, createPatient); // Allow students, assistants, and superadmins to create patients
router.get('/public/:nationalId', getPatientById); // Public endpoint for patient check
router.get('/:nationalId', auth, role('student', 'superadmin', 'assistant'), getPatientById); // Authenticated endpoint for patient check
router.put('/:nationalId', auth, role('student', 'superadmin', 'assistant'), updatePatient);
router.put('/:nationalId/medical', auth, updatePatientMedicalInfo);
router.post('/:nationalId/xrays', auth, role('student', 'superadmin'), upload, addXray);
router.post('/:nationalId/gallery', auth, role('student', 'superadmin'), upload, addGalleryImage);
router.put('/:nationalId/xrays/note', auth, role('student', 'superadmin', 'assistant'), updateXrayNote);
router.put('/:nationalId/gallery/note', auth, role('student', 'superadmin', 'assistant'), updateGalleryImageNote);
router.delete('/:nationalId/xrays/:xrayId', auth, role('student', 'superadmin', 'assistant'), deleteXray);
router.delete('/:nationalId/gallery/:imageId', auth, role('student', 'superadmin', 'assistant'), deleteGalleryImage);
router.get('/', auth, role('student'), getPatientsByDoctor);
router.delete('/:nationalId', auth, role('student', 'superadmin', 'assistant'), deletePatient);

// Get patients by university (for assistants)
router.get('/university/:university', auth, role('assistant', 'admin', 'superadmin'), getPatientsByUniversity);

// Treatment sheets
router.post('/:nationalId/treatment-sheets', auth, role('student', 'superadmin', 'assistant'), addTreatmentSheet);
router.get('/:nationalId/treatment-sheets', auth, role('student', 'superadmin', 'assistant'), getTreatmentSheets);
router.get('/:nationalId/sheets/:sheetType', auth, role('student', 'superadmin', 'assistant'), getTreatmentSheetByType);
router.put('/:nationalId/treatment-sheets/:sheetId', auth, role('student', 'superadmin', 'assistant'), updateTreatmentSheet);
router.delete('/:nationalId/treatment-sheets/:sheetId', auth, role('student', 'superadmin', 'assistant'), deleteTreatmentSheet);

// Consent route
router.put('/:nationalId/consent', auth, role('student', 'superadmin', 'assistant'), updatePatientConsent);
router.post('/:nationalId/consent/signature', auth, role('student', 'superadmin', 'assistant'), upload, updatePatientConsent);

module.exports = router;
