import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import { FaLinkedin, FaGithub, FaTooth, FaCode, FaBrain, FaRocket, FaHandshake, FaLightbulb } from 'react-icons/fa';
import { RiAiGenerate } from 'react-icons/ri';
import DOMPurify from 'dompurify';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = [
  '/',
  '/universities',
  '/about',
  '/contact',
  '/universityServices',
  '/try-ai',
  '/login',
  '/forgot-password'
];

const About = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Secure navigation handler
  const safeNavigate = useCallback((path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  }, [navigate]);

  // Sanitize URLs to prevent XSS
  const sanitizeUrl = (url) => {
    return DOMPurify.sanitize(url, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
  };

  // Sanitize image sources to ensure they are from trusted paths
  const sanitizeImageSrc = (src) => {
    const sanitizedSrc = DOMPurify.sanitize(src, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
    if (sanitizedSrc.startsWith('/imgs/')) {
      return sanitizedSrc;
    }
    console.warn('Invalid image source:', sanitizedSrc);
    return '/imgs/default.jpg'; // Fallback to a default image
  };

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (loading) return <Loader />;

  // Team data
  const teamMembers = [
    {
      name: "Sousannah Magdy",
      role: t('about.aiEngineer'),
      // bio: t('about.sousannahBio'),
      icon: <FaBrain className="h-20 w-20 text-[#0077B6] opacity-30" />,
      links: [
        // { icon: <FaLinkedin />, url: "https://linkedin.com/in/sousannah" },
        // { icon: <FaGithub />, url: "https://github.com/sousannah" }
      ]
    },
    {
      name: "Jana Samy",
      role: t('about.dentalExpert'),
      // bio: t('about.janaBio'),
      icon: <FaTooth className="h-20 w-20 text-[#0077B6] opacity-30" />,
      links: [
        // { icon: <FaLinkedin />, url: "https://linkedin.com/in/janasamy" }
      ]
    },
    {
      name: "Salma Mohamed",
      role: t('about.aiEngineer'),
      // bio: t('about.salmaBio'),
      icon: <FaCode className="h-20 w-20 text-[#0077B6] opacity-30" />,
      links: [
        // { icon: <FaLinkedin />, url: "https://linkedin.com/in/salmamohamed" },
        // { icon: <FaGithub />, url: "https://github.com/salmamohamed" }
      ]
    }
  ];

// Values data
const values = [
  {
    title: t('about.innovation'),
    description: t('about.innovationText'),
    icon: <FaLightbulb className="h-6 w-6 text-[#0077B6]" />
  },
  {
    title: t('about.clinicalExcellence'),
    description: t('about.clinicalExcellenceText'),
    icon: <FaTooth className="h-6 w-6 text-[#0077B6]" />
  },
  {
    title: t('about.education'),
    description: t('about.educationText'),
    icon: <FaBrain className="h-6 w-6 text-[#0077B6]" />
  }
];

  return (
    <div className="font-sans text-gray-800 bg-white min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-28 text-center relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-6 leading-tight"
          >
            {t('about.title')}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
              {t('about.innovators')}
            </span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-xl text-[#333333] max-w-3xl mx-auto"
          >
            {t('about.subtitle')}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="mt-8"
          >
            <Link
              to="/contact"
              onClick={() => safeNavigate('/contact')}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg hover:shadow-xl"
              aria-label={t('about.getInTouch')}
            >
              {t('about.getInTouch')}
              <FaRocket className="ml-2" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col lg:flex-row items-center gap-16"
          >
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <h2 className="text-3xl font-bold text-[#0077B6] mb-6">{t('about.ourMission')}</h2>
              <p className="text-lg text-[#333333] mb-8 leading-relaxed">
                {t('about.missionDescription')}
              </p>
              <h2 className="text-3xl font-bold text-[#0077B6] mb-6">{t('about.ourVision')}</h2>
              <p className="text-lg text-[#333333] leading-relaxed">
                {t('about.visionDescription')}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2 relative"
            >
              <div className="relative overflow-hidden rounded-2xl shadow-xl">
                <img
                  src={sanitizeImageSrc("/imgs/odenta.jpg")}
                  alt={t('about.mission')}
                  className="w-full h-auto rounded-2xl"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#0077B6]/20 to-transparent"></div>
              </div>
              <motion.div
                initial={{ scale: 0.9 }}
                whileInView={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 100 }}
                viewport={{ once: true }}
                className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg border border-gray-100"
              >
                <div className="flex items-center">
                  <div className="bg-[rgba(32,178,170,0.1)] p-3 rounded-lg mr-3">
                    <FaHandshake className="h-6 w-6 text-[#0077B6]" />
                  </div>
                  <div>
                    <p className="font-bold text-[#0077B6]">2025</p>
                    <p className="text-sm text-[#333333]">{t('about.founded')}</p>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gradient-to-b from-[rgba(0,119,182,0.05)] to-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('about.meetOurTeam')}
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">
              {t('about.teamDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 group"
            >
              <div className="relative h-64 bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] flex items-center justify-center">
                <img
                  src={sanitizeImageSrc("/imgs/sousannah.jpg")}
                  alt="Sousannah Magdy"
                  className="w-40 h-40 rounded-full object-cover border-4 border-white shadow-md"
                  loading="lazy"
                />
              </div>
              <div className="p-6 text-center">
                <h3 className="text-2xl font-bold text-[#0077B6] mb-1">Sousannah Magdy</h3>
                <p className="text-[#20B2AA] font-medium mb-4">{teamMembers[0].role}</p>
                <p className="text-[#333333] mb-6">{teamMembers[0].bio}</p>
                <div className="flex justify-center space-x-4">
                  {teamMembers[0].links.map((link, i) => (
                    <a
                      key={i}
                      href={sanitizeUrl(link.url)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#0077B6] hover:text-[#20B2AA] transition-colors duration-300"
                      aria-label={`Visit ${teamMembers[0].name}'s ${link.icon.type === FaLinkedin ? 'LinkedIn' : 'GitHub'} profile`}
                    >
                      {link.icon}
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 group"
            >
              <div className="relative h-64 bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] flex items-center justify-center">
                <img
                  src={sanitizeImageSrc("/imgs/jana.jpg")}
                  alt="Jana Samy"
                  className="w-40 h-40 rounded-full object-cover border-4 border-white shadow-md"
                  loading="lazy"
                />
                {/* {teamMembers[1].icon} */}
              </div>
              <div className="p-6 text-center">
                <h3 className="text-2xl font-bold text-[#0077B6] mb-1">Jana Samy</h3>
                <p className="text-[#20B2AA] font-medium mb-4">{teamMembers[1].role}</p>
                <p className="text-[#333333] mb-6">{teamMembers[1].bio}</p>
                <div className="flex justify-center space-x-4">
                  {teamMembers[1].links.map((link, i) => (
                    <a
                      key={i}
                      href={sanitizeUrl(link.url)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#0077B6] hover:text-[#20B2AA] transition-colors duration-300"
                      aria-label={`Visit ${teamMembers[1].name}'s ${link.icon.type === FaLinkedin ? 'LinkedIn' : 'GitHub'} profile`}
                    >
                      {link.icon}
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 group"
            >
              <div className="relative h-64 bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] flex items-center justify-center">
                <img
                  src={sanitizeImageSrc("/imgs/salma.jpg")}
                  alt="Salma Mohamed"
                  className="w-40 h-40 rounded-full object-cover border-4 border-white shadow-md"
                  loading="lazy"
                />
                {/* {teamMembers[2].icon} */}
              </div>
              <div className="p-6 text-center">
                <h3 className="text-2xl font-bold text-[#0077B6] mb-1">Salma Mohamed</h3>
                <p className="text-[#20B2AA] font-medium mb-4">{teamMembers[2].role}</p>
                <p className="text-[#333333] mb-6">{teamMembers[2].bio}</p>
                <div className="flex justify-center space-x-4">
                  {teamMembers[2].links.map((link, i) => (
                    <a
                      key={i}
                      href={sanitizeUrl(link.url)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#0077B6] hover:text-[#20B2AA] transition-colors duration-300"
                      aria-label={`Visit ${teamMembers[2].name}'s ${link.icon.type === FaLinkedin ? 'LinkedIn' : 'GitHub'} profile`}
                    >
                      {link.icon}
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('about.ourValues')}
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">
              {t('about.valuesDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.03 }}
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#20B2AA]"
              >
                <div className="bg-[rgba(32,178,170,0.1)] w-12 h-12 rounded-lg flex items-center justify-center mb-4 mx-auto">
                  {React.cloneElement(value.icon, { className: "h-6 w-6 text-[#0077B6]" })}
                </div>
                <h3 className="text-xl font-bold text-[#0077B6] mb-3 text-center">{value.title}</h3>
                <p className="text-[#333333] text-center">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            {t('about.joinUs')}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-xl text-white text-opacity-90 max-w-3xl mx-auto mb-8"
          >
            {t('about.ctaText')}
          </motion.p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/contact"
              onClick={() => safeNavigate('/contact')}
              className="inline-flex items-center bg-white text-[#0077B6] px-8 py-3 rounded-full font-bold hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl"
              aria-label={t('about.contactUs')}
            >
              {t('about.contactUs')}
              <FaRocket className="ml-2" />
            </Link>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;