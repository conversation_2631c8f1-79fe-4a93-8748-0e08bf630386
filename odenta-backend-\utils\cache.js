/**
 * Simple in-memory cache for backend operations
 * Helps reduce database reads for frequently accessed data
 */

class BackendCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  /**
   * Set a value in cache with TTL
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (default: 5 minutes)
   */
  set(key, value, ttl = 5 * 60 * 1000) {
    // Clear existing timer if any
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Set the value
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  /**
   * Get a value from cache
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found/expired
   */
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * Check if a key exists in cache and is not expired
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null;
  }

  /**
   * Delete a key from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }
  }

  /**
   * Clear all cache
   */
  clear() {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    
    // Clear cache and timers
    this.cache.clear();
    this.timers.clear();
  }

  /**
   * Get cache statistics
   * @returns {object} Cache stats
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Get or set pattern - if key exists return it, otherwise execute function and cache result
   * @param {string} key - Cache key
   * @param {function} fn - Function to execute if cache miss
   * @param {number} ttl - Time to live in milliseconds
   * @returns {Promise<any>} Cached or fresh value
   */
  async getOrSet(key, fn, ttl = 5 * 60 * 1000) {
    const cached = this.get(key);
    
    if (cached !== null) {
      console.log(`Backend Cache HIT for key: ${key}`);
      return cached;
    }

    console.log(`Backend Cache MISS for key: ${key}`);
    const value = await fn();
    this.set(key, value, ttl);
    return value;
  }

  /**
   * Invalidate cache entries by pattern
   * @param {string} pattern - Pattern to match keys
   */
  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    console.log(`Invalidated ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
  }
}

// Create a singleton instance
const backendCache = new BackendCache();

// Cache key generators for common data types
const CacheKeys = {
  PATIENTS_BY_UNIVERSITY: (university, page = 1, limit = 20, search = '', studentFilter = 'all', sortBy = 'registrationDate', sortOrder = 'desc') => 
    `patients_${university}_${page}_${limit}_${search}_${studentFilter}_${sortBy}_${sortOrder}`,
  
  STUDENTS_BY_UNIVERSITY: (university, page = 1, limit = 50, search = '') => 
    `students_${university}_${page}_${limit}_${search}`,
  
  PATIENT_DETAILS: (nationalId) => `patient_${nationalId}`,
  
  PROCEDURE_REQUESTS: () => 'procedure_requests',
  
  USER_BY_ID: (collection, id) => `user_${collection}_${id}`,
  
  UNIVERSITY_DATA: (university) => `university_${university}`
};

// Cache TTL constants (in milliseconds)
const CacheTTL = {
  SHORT: 2 * 60 * 1000,      // 2 minutes
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
};

// Middleware to add cache utilities to request object
const cacheMiddleware = (req, res, next) => {
  req.cache = backendCache;
  req.CacheKeys = CacheKeys;
  req.CacheTTL = CacheTTL;
  next();
};

// Helper function to invalidate related cache entries
const invalidateRelatedCache = (type, ...args) => {
  switch (type) {
    case 'patient':
      if (args[0]) {
        backendCache.delete(CacheKeys.PATIENT_DETAILS(args[0]));
        backendCache.invalidatePattern(`patients_.*`); // Invalidate all patient lists
      }
      break;
    case 'student':
      backendCache.invalidatePattern(`students_.*`); // Invalidate all student lists
      backendCache.invalidatePattern(`patients_.*`); // Also invalidate patient lists (assignments might change)
      break;
    case 'university':
      if (args[0]) {
        backendCache.invalidatePattern(`.*_${args[0]}_.*`); // Invalidate all data for this university
      }
      break;
    case 'procedure-requests':
      backendCache.delete(CacheKeys.PROCEDURE_REQUESTS());
      break;
    case 'all':
      backendCache.clear();
      break;
  }
};

module.exports = {
  backendCache,
  CacheKeys,
  CacheTTL,
  cacheMiddleware,
  invalidateRelatedCache
};
