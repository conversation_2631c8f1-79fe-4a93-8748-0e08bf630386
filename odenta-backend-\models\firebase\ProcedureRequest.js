const Joi = require('joi');
const { commonSchemas, procedureRequestSchema, COLLECTIONS } = require('./index');

// Procedure request creation schema (without ID)
const createProcedureRequestSchema = procedureRequestSchema.fork(['id'], (schema) => schema.forbidden());

// Procedure request update schema (partial)
const updateProcedureRequestSchema = procedureRequestSchema.fork(
  ['studentId', 'studentName', 'procedureType', 'patientNationalId', 'patientName', 'notes'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Helper functions for procedure request operations
const ProcedureRequestHelpers = {
  // Validate procedure request data
  validateCreate: (data) => {
    return createProcedureRequestSchema.validate(data);
  },
  
  validateUpdate: (data) => {
    return updateProcedureRequestSchema.validate(data);
  },
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.requestDate && typeof prepared.requestDate === 'string') {
      prepared.requestDate = new Date(prepared.requestDate);
    }
    if (prepared.responseDate && typeof prepared.responseDate === 'string') {
      prepared.responseDate = new Date(prepared.responseDate);
    }
    
    return prepared;
  },
  
  // Format for client response
  formatForResponse: (procedureRequestData) => {
    // Helper function to convert various date formats to ISO string
    const formatDate = (dateField) => {
      if (!dateField) return null;
      
      try {
        // If it's already a Date object
        if (dateField instanceof Date) {
          return dateField.toISOString();
        }
        
        // If it's a Firestore timestamp with toDate method
        if (dateField && typeof dateField.toDate === 'function') {
          return dateField.toDate().toISOString();
        }
        
        // If it's a Firestore timestamp object with _seconds
        if (dateField && typeof dateField === 'object' && dateField._seconds) {
          return new Date(dateField._seconds * 1000).toISOString();
        }
        
        // If it's a string, try to parse it
        if (typeof dateField === 'string') {
          return new Date(dateField).toISOString();
        }
        
        // If it's a number (timestamp)
        if (typeof dateField === 'number') {
          return new Date(dateField).toISOString();
        }
        
        return null;
      } catch (error) {
        console.error('Error formatting date:', error);
        return null;
      }
    };

    return {
      ...procedureRequestData,
      requestDate: formatDate(procedureRequestData.requestDate),
      responseDate: formatDate(procedureRequestData.responseDate)
    };
  }
};

module.exports = {
  procedureRequestSchema,
  createProcedureRequestSchema,
  updateProcedureRequestSchema,
  ProcedureRequestHelpers,
  COLLECTION_NAME: COLLECTIONS.PROCEDURE_REQUESTS
};
