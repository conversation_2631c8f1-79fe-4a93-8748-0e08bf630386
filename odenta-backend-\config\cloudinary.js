const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Create storage for different image types
const createCloudinaryStorage = (folder) => {
  return new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
      folder: folder,
      allowed_formats: ['jpg', 'jpeg', 'png'],
      transformation: [
        { width: 1250, height: 850, crop: 'limit' }, // Resize to max 1250x850 while maintaining aspect ratio
        { quality: 'auto' }, // Automatic quality optimization
        { fetch_format: 'auto' } // Automatic format optimization
      ],
    },
  });
};

// Storage configurations for different image types
const xrayStorage = createCloudinaryStorage('odenta/xrays');
const galleryStorage = createCloudinaryStorage('odenta/gallery');
const signatureStorage = createCloudinaryStorage('odenta/signatures');

// Multer configurations
const uploadXrays = multer({
  storage: xrayStorage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  },
});

const uploadGallery = multer({
  storage: galleryStorage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  },
});

const uploadSignatures = multer({
  storage: signatureStorage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for signatures
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  },
});

// Combined upload for multiple field types
const uploadMultiple = multer({
  storage: galleryStorage, // Default storage
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  },
}).fields([
  { name: 'xrays', maxCount: 10 },
  { name: 'galleryImages', maxCount: 10 },
  { name: 'signatures', maxCount: 5 }
]);

// Helper function to delete images from Cloudinary
const deleteFromCloudinary = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return result;
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw error;
  }
};

// Helper function to extract public ID from Cloudinary URL
const extractPublicId = (url) => {
  if (!url) return null;
  
  // Extract public ID from Cloudinary URL
  // Example: https://res.cloudinary.com/cloud_name/image/upload/v1234567890/folder/filename.jpg
  const matches = url.match(/\/v\d+\/(.+)\./);
  return matches ? matches[1] : null;
};

module.exports = {
  cloudinary,
  uploadXrays,
  uploadGallery,
  uploadSignatures,
  uploadMultiple,
  deleteFromCloudinary,
  extractPublicId,
  createCloudinaryStorage
};
