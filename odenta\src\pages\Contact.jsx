import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaTooth, FaRocket, FaLinkedin, FaTwitter, FaYoutube, FaFacebook, FaInstagram } from 'react-icons/fa';
import { RiAiGenerate } from 'react-icons/ri';
import DOMPurify from 'dompurify';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = [
  '/',
  '/universities',
  '/about',
  '/contact',
  '/universityServices',
  '/try-ai',
  '/login',
  '/forgot-password'
];

const ContactUs = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState('');
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);
  const navigate = useNavigate();

  // Secure navigation handler
  const safeNavigate = useCallback((path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  }, [navigate]);

  // Sanitize input to prevent XSS
  const sanitizeInput = (input) => {
    return DOMPurify.sanitize(input, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
  };

  // Sanitize URLs to prevent XSS
  const sanitizeUrl = (url) => {
    const sanitizedUrl = DOMPurify.sanitize(url, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
    // Validate mailto: and tel: schemes
    if (url.startsWith('mailto:') || url.startsWith('tel:')) {
      if (sanitizedUrl === url) return sanitizedUrl;
    }
    // Validate social media URLs
    if (sanitizedUrl.match(/^https:\/\/(linkedin|twitter|youtube|facebook|instagram)\.com\/.+$/)) {
      return sanitizedUrl;
    }
    console.warn('Invalid URL:', url);
    return '#'; // Fallback to no-op link
  };

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitDisabled) return;

    setIsSubmitDisabled(true);
    setFormStatus('');

    // Sanitize form inputs
    const sanitizedFormData = {
      name: sanitizeInput(formData.name),
      email: sanitizeInput(formData.email),
      message: sanitizeInput(formData.message)
    };

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedFormData.email)) {
      setFormStatus(t('contact.formErrorInvalidEmail'));
      setIsSubmitDisabled(false);
      return;
    }

    try {
      // Retrieve CSRF token (assumed to be in meta tag)
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '';

      const response = await axios.post(
        'https://api.dentlyzer.com/api/contact',
        sanitizedFormData,
        {
          headers: {
            'X-CSRF-Token': csrfToken,
            'Content-Type': 'application/json',
          },
          withCredentials: true,
        }
      );

      setFormStatus(sanitizeInput(response.data.message || t('contact.formSuccess')));
      setFormData({ name: '', email: '', message: '' });
      setTimeout(() => {
        setFormStatus('');
        setIsSubmitDisabled(false);
      }, 3000);
    } catch (error) {
      setFormStatus(sanitizeInput(t('contact.formError')));
      setIsSubmitDisabled(false);
    }
  };

  if (loading) return <Loader />;

  return (
    <div className="font-sans text-gray-800 bg-white min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-28 text-center relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-6 leading-tight"
          >
            {t('contact.title')}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
              {t('contact.withUs')}
            </span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-xl text-[#333333] max-w-3xl mx-auto"
          >
            {t('contact.subtitle')}
          </motion.p>
        </div>
      </section>

      {/* Contact Info and Form Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <h2 className="text-3xl font-bold text-[#0077B6] mb-6">{t('contact.getInTouch')}</h2>
              <p className="text-lg text-[#333333] mb-8">{t('contact.description')}</p>
              <div className="space-y-6">
                {[
                  {
                    icon: <FaEnvelope className="h-6 w-6 text-[#0077B6]" />,
                    title: t('contact.email'),
                    value: '<EMAIL>',
                    link: 'mailto:<EMAIL>'
                  },
                  {
                    icon: <FaPhone className="h-6 w-6 text-[#0077B6]" />,
                    title: t('contact.phone'),
                    value: '+201276902211',
                    link: 'tel:+201276902211'
                  },
                  {
                    icon: <FaMapMarkerAlt className="h-6 w-6 text-[#0077B6]" />,
                    title: t('contact.address'),
                    value: 'Alexandria, Egypt'
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                    className="flex items-start"
                  >
                    <div className="bg-[rgba(0,119,182,0.1)] rounded-lg p-3 mr-4 flex-shrink-0">
                      {item.icon}
                    </div>
                    <div>
                      <h4 className="font-bold text-[#0077B6]">{item.title}</h4>
                      {item.link ? (
                        <a
                          href={sanitizeUrl(item.link)}
                          className="text-[#333333] hover:text-[#20B2AA] transition-colors duration-300"
                          rel="noopener noreferrer"
                          aria-label={`${item.title}: ${item.value}`}
                        >
                          {item.value}
                        </a>
                      ) : (
                        <p className="text-[#333333]">{item.value}</p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
              <div className="mt-8 flex space-x-4">
                {[
                  { icon: <FaLinkedin />, url: 'https://linkedin.com/company/dentlyzer' },
                  { icon: <FaTwitter />, url: 'https://twitter.com/dentlyzer' },
                  { icon: <FaYoutube />, url: 'https://youtube.com/dentlyzer' },
                  { icon: <FaFacebook />, url: 'https://facebook.com/dentlyzer' },
                  { icon: <FaInstagram />, url: 'https://instagram.com/dentlyzer' }
                ].map((social, index) => (
                  <a
                    key={index}
                    href={sanitizeUrl(social.url)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#0077B6] hover:text-[#20B2AA] transition-colors duration-300"
                    aria-label={`Visit Dentlyzer on ${social.icon.type === FaLinkedin ? 'LinkedIn' : social.icon.type === FaTwitter ? 'Twitter' : social.icon.type === FaYoutube ? 'YouTube' : social.icon.type === FaFacebook ? 'Facebook' : 'Instagram'}`}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-2xl font-bold text-[#0077B6] mb-6">{t('contact.sendMessage')}</h3>
                {/* Disable form submission in sandboxed environments */}
                <div onSubmit={handleSubmit}>
                  <div className="mb-6">
                    <label htmlFor="name" className="block text-[#333333] font-medium mb-2">
                      {t('contact.name')}
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]"
                      placeholder={t('contact.namePlaceholder')}
                      aria-label={t('contact.name')}
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="email" className="block text-[#333333] font-medium mb-2">
                      {t('contact.email')}
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA]"
                      placeholder={t('contact.emailPlaceholder')}
                      aria-label={t('contact.email')}
                    />
                  </div>
                  <div className="mb-6">
                    <label htmlFor="message" className="block text-[#333333] font-medium mb-2">
                      {t('contact.message')}
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] h-32"
                      placeholder={t('contact.messagePlaceholder')}
                      aria-label={t('contact.message')}
                    ></textarea>
                  </div>
                  {formStatus && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className={`mb-4 ${formStatus.includes('Error') ? 'text-red-600' : 'text-green-600'}`}
                    >
                      {formStatus}
                    </motion.p>
                  )}
                  <motion.button
                    whileHover={{ scale: isSubmitDisabled ? 1 : 1.05 }}
                    whileTap={{ scale: isSubmitDisabled ? 1 : 0.95 }}
                    type="submit"
                    disabled={isSubmitDisabled}
                    className={`w-full bg-gradient-to-r ${
                      isSubmitDisabled
                        ? 'from-[#0077B6]/70 to-[#20B2AA]/70 cursor-not-allowed'
                        : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'
                    } text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl`}
                    onClick={handleSubmit}
                    aria-label={t('contact.submit')}
                  >
                    {t('contact.submit')}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            {t('contact.exploreMore')}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-[rgba(255,255,255,0.7)]">
              {t('contact.withDentlyzer')}
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-xl text-white text-opacity-90 max-w-3xl mx-auto mb-8"
          >
            {t('contact.ctaDescription')}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/universityServices"
                onClick={() => safeNavigate('/universityServices')}
                className="bg-white text-[#0077B6] px-8 py-4 rounded-full font-bold hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                aria-label={t('contact.exploreUniversity')}
              >
                <FaRocket className="mr-2" />
                {t('contact.exploreUniversity')}
              </Link>
            </motion.button>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-8"
          >
            <Link
              to="/try-ai"
              onClick={() => safeNavigate('/try-ai')}
              className="text-white text-opacity-90 hover:text-white font-medium flex items-center justify-center mx-auto"
              aria-label={t('contact.tryOurAI')}
            >
              <RiAiGenerate className="mr-2" />
              {t('contact.tryOurAI')}
            </Link>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ContactUs;