import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaUserAlt,
  FaCalendarAlt,
  FaChartLine,
  FaArrowRight,
  FaDownload,
  FaUserMd,
  FaUserNurse,
  FaClipboardCheck,
  FaExclamationTriangle,
  FaRegCalendarCheck,
} from 'react-icons/fa';
import { Bar, Pie, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { saveAs } from 'file-saver';
import { formatDate, parseDate, isToday, isFuture } from '../utils/dateUtils';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

const AssistantDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view your dashboard.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };

        // Fetch appointments based on university using the admin endpoint
        const appointmentsRes = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/admin/appointments`,
          config
        );

        setAppointments(appointmentsRes.data || []);

        // No error message for empty data - this is normal
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage =
          err.response?.status === 404
            ? 'Data endpoint not found.'
            : err.response?.status === 401
            ? 'Unauthorized. Please log in again.'
            : err.response?.data?.message || 'Failed to load dashboard data';
        setError(errorMessage);
        if (err.response?.status === 401) navigate('/login');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate]);

  // Process appointments data
  const appointmentsAnalytics = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    completed: appointments.filter(a => a.status === 'completed').length,
    cancelled: appointments.filter(a => a.status === 'cancelled').length,
    today: appointments.filter(a => isToday(a.date)).length,
    thisWeek: appointments.filter(a => {
      const apptDate = parseDate(a.date);
      if (!apptDate) return false;
      const today = new Date();
      const weekEnd = new Date();
      weekEnd.setDate(today.getDate() + 7);
      return apptDate >= today && apptDate <= weekEnd;
    }).length
  };

  // Get appointment types
  const appointmentTypes = {};
  appointments.forEach(appt => {
    const type = appt.treatment || appt.type || 'Other';
    appointmentTypes[type] = (appointmentTypes[type] || 0) + 1;
  });

  // Get appointments by day of week for the line chart
  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const appointmentsByDay = Array(7).fill(0);

  appointments.forEach(appt => {
    const apptDate = parseDate(appt.date);
    if (apptDate) {
      const dayIndex = apptDate.getDay();
      appointmentsByDay[dayIndex]++;
    }
  });

  // Define a consistent color palette
  const colorPalette = {
    blue: { main: 'rgba(37, 99, 235, 0.8)', border: 'rgba(37, 99, 235, 1)' },
    indigo: { main: 'rgba(79, 70, 229, 0.8)', border: 'rgba(79, 70, 229, 1)' },
    purple: { main: 'rgba(139, 92, 246, 0.8)', border: 'rgba(139, 92, 246, 1)' },
    pink: { main: 'rgba(219, 39, 119, 0.8)', border: 'rgba(219, 39, 119, 1)' },
    red: { main: 'rgba(239, 68, 68, 0.8)', border: 'rgba(239, 68, 68, 1)' },
    orange: { main: 'rgba(249, 115, 22, 0.8)', border: 'rgba(249, 115, 22, 1)' },
    amber: { main: 'rgba(245, 158, 11, 0.8)', border: 'rgba(245, 158, 11, 1)' },
    green: { main: 'rgba(16, 185, 129, 0.8)', border: 'rgba(16, 185, 129, 1)' },
    teal: { main: 'rgba(20, 184, 166, 0.8)', border: 'rgba(20, 184, 166, 1)' },
  };

  // Chart data
  const appointmentsStatusChartData = {
    labels: ['Pending', 'Completed', 'Cancelled'],
    datasets: [
      {
        data: [appointmentsAnalytics.pending, appointmentsAnalytics.completed, appointmentsAnalytics.cancelled],
        backgroundColor: [colorPalette.amber.main, colorPalette.green.main, colorPalette.red.main],
        borderColor: [colorPalette.amber.border, colorPalette.green.border, colorPalette.red.border],
        borderWidth: 1,
        borderRadius: 4,
        hoverOffset: 4
      },
    ],
  };

  const appointmentsByTypeChartData = {
    labels: Object.keys(appointmentTypes),
    datasets: [
      {
        data: Object.values(appointmentTypes),
        backgroundColor: [
          colorPalette.blue.main,
          colorPalette.purple.main,
          colorPalette.green.main,
          colorPalette.orange.main,
          colorPalette.indigo.main,
        ],
        borderColor: [
          colorPalette.blue.border,
          colorPalette.purple.border,
          colorPalette.green.border,
          colorPalette.orange.border,
          colorPalette.indigo.border,
        ],
        borderWidth: 1,
        borderRadius: 4,
        hoverOffset: 4
      },
    ],
  };

  const appointmentsByDayChartData = {
    labels: daysOfWeek,
    datasets: [
      {
        label: 'Appointments',
        data: appointmentsByDay,
        backgroundColor: colorPalette.blue.main,
        borderColor: colorPalette.blue.border,
        borderWidth: 2,
        tension: 0.4,
        fill: true,
        pointBackgroundColor: '#fff',
        pointBorderColor: colorPalette.blue.border,
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          borderDash: [2, 4],
          color: 'rgba(0, 0, 0, 0.06)'
        },
        ticks: {
          precision: 0,
          font: {
            family: "'Inter', sans-serif",
            size: 12
          }
        }
      }
    }
  };

  // Download appointments as Excel
  const downloadAppointments = () => {
    const headers = ['Date', 'Time', 'Patient', 'Procedure', 'Status'];
    const data = appointments.map((appt) => [
      formatDate(appt.date),
      appt.time || 'N/A',
      appt.patient?.fullName || appt.fullName || 'Unknown',
      appt.treatment || appt.type || 'N/A',
      appt.status
    ]);

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...data.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Create blob and download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    saveAs(blob, `appointments-${new Date().toISOString().slice(0, 10)}.csv`);
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Assistant Dashboard</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Welcome back, {user?.name || 'Assistant'}</p>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="space-y-6 sm:space-y-8"
              >
                <motion.div
                  variants={container}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
                >
                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/assistant/appointments" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaCalendarAlt className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>Today's Appointments</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{appointmentsAnalytics.today}</p>
                      </div>
                    </Link>
                  </motion.div>

                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/assistant/appointments" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaCalendarAlt className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>This Week's Appointments</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{appointmentsAnalytics.thisWeek}</p>
                      </div>
                    </Link>
                  </motion.div>

                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/assistant/appointments" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaCalendarAlt className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>Total Appointments</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{appointmentsAnalytics.total}</p>
                      </div>
                    </Link>
                  </motion.div>
                </motion.div>

                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-3 sm:py-4 px-4 sm:px-6 text-center font-medium text-xs sm:text-sm"
                      style={{
                        color: websiteColorPalette.primary,
                        borderBottom: `2px solid ${websiteColorPalette.primary}`
                      }}
                    >
                      Recent Appointments
                    </div>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                      <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                        <FaCalendarAlt className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                        Appointment Overview
                      </h2>
                      <Link
                        to="/assistant/appointments"
                        className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm`}
                      >
                        <span>View All Appointments</span>
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th className="hidden sm:table-cell px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {appointments.slice(0, 5).map((appt) => (
                            <tr key={appt._id} className="hover:bg-gray-50">
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                {formatDate(appt.date, {
                                  weekday: 'short',
                                  month: 'short',
                                  day: 'numeric',
                                })}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{appt.time || 'N/A'}</td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.patient?.fullName || appt.fullName || 'Unknown'}
                              </td>
                              <td className="hidden sm:table-cell px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.treatment || appt.type || 'N/A'}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    appt.status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : appt.status === 'cancelled'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {appt.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AssistantDashboard;
