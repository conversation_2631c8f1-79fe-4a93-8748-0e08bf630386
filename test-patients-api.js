// Simple test script to verify the patients API is working
const axios = require('axios');

const API_URL = 'http://localhost:5000'; // Adjust if your backend runs on a different port

async function testPatientsAPI() {
  try {
    console.log('Testing patients API...');
    
    // Test without pagination (should return all patients)
    console.log('\n1. Testing without pagination parameters:');
    const response1 = await axios.get(`${API_URL}/api/admin/patients`, {
      headers: {
        'Authorization': 'Bearer your-test-token-here' // You'll need a valid token
      }
    });
    
    console.log('Response type:', typeof response1.data);
    console.log('Is array:', Array.isArray(response1.data));
    if (Array.isArray(response1.data)) {
      console.log('Number of patients:', response1.data.length);
    } else {
      console.log('Response structure:', Object.keys(response1.data));
    }
    
    // Test with pagination
    console.log('\n2. Testing with pagination parameters:');
    const response2 = await axios.get(`${API_URL}/api/admin/patients?page=1&limit=10`, {
      headers: {
        'Authorization': 'Bearer your-test-token-here' // You'll need a valid token
      }
    });
    
    console.log('Response type:', typeof response2.data);
    console.log('Response structure:', Object.keys(response2.data));
    if (response2.data.patients) {
      console.log('Number of patients in page:', response2.data.patients.length);
      console.log('Pagination info:', response2.data.pagination);
    }
    
  } catch (error) {
    console.error('Error testing API:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

// Run the test
testPatientsAPI();
