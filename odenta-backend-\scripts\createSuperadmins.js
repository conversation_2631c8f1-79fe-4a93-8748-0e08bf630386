const { connectFirestore, FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const createSuperadmins = async () => {
  try {
    console.log('🚀 Creating superadmin accounts...');
    
    // Connect to Firebase
    await connectFirestore();
    
    const superadmins = [
      {
        email: '<EMAIL>',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        role: 'superadmin',
        password: '', // Will be set via Google auth
        plainPassword: '',
        googleId: '',
        picture: '',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        name: 'O<PERSON>enta Ad<PERSON>',
        role: 'superadmin',
        password: '', // Will be set via Google auth
        plainPassword: '',
        googleId: '',
        picture: '',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const superadmin of superadmins) {
      // Check if superadmin already exists
      const existing = await FirestoreHelpers.findOne(
        COLLECTIONS.CONFIGS,
        { field: 'email', operator: '==', value: superadmin.email }
      );

      if (existing) {
        console.log(`⚠️  Superadmin already exists: ${superadmin.email}`);
        continue;
      }

      const created = await FirestoreHelpers.create(COLLECTIONS.CONFIGS, superadmin);
      console.log(`✅ Created superadmin: ${superadmin.email}`);
    }
    
    console.log('🎉 Superadmin creation completed!');
    console.log('');
    console.log('🔐 Google Auth Superadmins:');
    console.log('   - <EMAIL>');
    console.log('   - <EMAIL>');
    console.log('');
    console.log('📝 These accounts can now sign in using Google OAuth');
    
  } catch (error) {
    console.error('❌ Superadmin creation failed:', error);
    process.exit(1);
  }
};

// Run if this file is executed directly
if (require.main === module) {
  createSuperadmins()
    .then(() => {
      console.log('Superadmin creation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Superadmin creation failed:', error);
      process.exit(1);
    });
}

module.exports = { createSuperadmins };
