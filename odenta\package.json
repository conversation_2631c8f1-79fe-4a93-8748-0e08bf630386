{"name": "dently<PERSON>-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.9.1", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-i18next": "^13.5.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "react-scripts": "5.0.1", "recharts": "^2.15.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}