import { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import axios from 'axios';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { FaUser, FaFileMedical, FaProcedures } from 'react-icons/fa';

const chronicDiseases = [
  'Diabetes Mellitus',
  'Hypertension',
  'Cardiovascular Disease',
  'Coagulopathy / Bleeding Disorders',
  'Thyroid Disorders',
  'Pregnancy',
  'Lactation',
  'Hepatic Diseases',
  'Renal Diseases'
];

const PatientProfile = () => {
  const { user, token } = useAuth();
  const { nationalId } = useParams();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [selectedSheetType, setSelectedSheetType] = useState('');

  const [formData, setFormData] = useState({
    nationalId: '',
    fullName: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: '',
    },
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      if (!token) {
        setError('Please log in to view patient data.');
        setLoading(false);
        return;
      }
      try {
        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = `${baseUrl}/api/patients/public/${nationalId}`;
        const response = await axios.get(apiUrl);
        const patient = response.data;
        setPatientData(patient);
        console.log('Fetched patient data:', patient);
        console.log('Patient medicalInfo:', patient.medicalInfo);
        
        setFormData({
          nationalId: patient.nationalId,
          fullName: patient.fullName,
          phoneNumber: patient.phoneNumber,
          gender: patient.gender,
          age: patient.age,
          address: patient.address || '',
          occupation: patient.occupation || '',
          medicalInfo: {
            chronicDiseases: patient.medicalInfo?.chronicDiseases || [],
            recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',
            currentMedications: patient.medicalInfo?.currentMedications || '',
            chiefComplaint: patient.medicalInfo?.chiefComplaint || '',
          },
        });
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        let message;
        if (err.response?.status === 401) {
          message = 'Unauthorized: Please log in again.';
        } else if (err.response?.status === 404) {
          message = 'Patient not found. Please check the patient information and try again.';
        } else {
          message = err.response?.data?.message || 'Failed to load patient data';
        }
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [nationalId, token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'chiefComplaint') {
      setFormData(prev => ({
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chiefComplaint: value },
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleMedicalInfoChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      medicalInfo: { ...prev.medicalInfo, [name]: value },
    }));
  };

  const handleChronicDiseaseChange = (disease) => {
    setFormData(prev => {
      const diseases = prev.medicalInfo.chronicDiseases.includes(disease)
        ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease)
        : [...prev.medicalInfo.chronicDiseases, disease];
      return {
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chronicDiseases: diseases },
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const patientData = {
        ...formData,
        drId: user.studentId, // Use studentId instead of id
      };
      console.log('Sending patient update data:', patientData);
      console.log('National ID:', nationalId);
      console.log('User studentId:', user.studentId);
      
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await axios.put(
        `${baseUrl}/api/patients/${nationalId}`,
        patientData,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      if (response.data && response.data.patient) {
        setPatientData(response.data.patient);
        setIsEditing(false);
      } else {
        throw new Error('Invalid patient data received from server');
      }
    } catch (err) {
      console.error('Update error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to update patient data');
    }
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    setError(''); // Clear any previous errors when toggling edit mode
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <PatientNav
            selectedSheetType={selectedSheetType}
            setSelectedSheetType={setSelectedSheetType}
          />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center"
              >
                Loading patient data...
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error || !patientData) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <PatientNav
            selectedSheetType={selectedSheetType}
            setSelectedSheetType={setSelectedSheetType}
          />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-[#0077B6]/10"
              >
                <div className="text-[#0077B6] mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Patient Not Found</h3>
                <p className="text-gray-600 mb-4">{error || 'Patient not found'}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md"
                >
                  Try Again
                </motion.button>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav
          selectedSheetType={selectedSheetType}
          setSelectedSheetType={setSelectedSheetType}
        />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Patient Profile
                  </h1>
                  <p className="text-gray-600">{patientData.fullName}</p>
                </div>
                <Link
                  to="/student/dashboard"
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-4 md:px-6 py-2.5 rounded-full font-medium hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to Dashboard
                </Link>
              </div>

              {error && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mb-6 p-4 bg-red-100 text-[#333333] rounded-lg"
                >
                  {error}
                </motion.div>
              )}

              {isEditing ? (
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="bg-white rounded-xl p-6 shadow-sm border border-[#0077B6]/10"
                >
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold text-[#0077B6]">Edit Patient</h2>
                    <button
                      onClick={handleEditToggle}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <h3 className="text-xl font-semibold text-[#0077B6] mb-4 border-b pb-2">Personal Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Full Name*
                        </label>
                        <input
                          type="text"
                          name="fullName"
                          value={formData.fullName}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          National ID*
                        </label>
                        <input
                          type="text"
                          name="nationalId"
                          value={formData.nationalId}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                          disabled
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Phone Number*
                        </label>
                        <input
                          type="tel"
                          name="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Gender*
                        </label>
                        <select
                          name="gender"
                          value={formData.gender}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        >
                          <option value="">Select</option>
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Age*
                        </label>
                        <input
                          type="number"
                          name="age"
                          value={formData.age}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Address*
                        </label>
                        <input
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1.5">
                          Occupation*
                        </label>
                        <input
                          type="text"
                          name="occupation"
                          value={formData.occupation}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          required
                        />
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2">Medical History</h3>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Chronic Diseases
                      </label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 bg-gray-50 p-4 rounded-lg border border-gray-200">
                        {chronicDiseases.map((disease) => (
                          <div key={disease} className="flex items-center">
                            <div className="relative flex items-center">
                              <input
                                type="checkbox"
                                id={`disease-${disease}`}
                                checked={formData.medicalInfo.chronicDiseases.includes(disease)}
                                onChange={() => handleChronicDiseaseChange(disease)}
                                className="h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer"
                              />
                              <label
                                htmlFor={`disease-${disease}`}
                                className="ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-colors"
                              >
                                {disease}
                              </label>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Recent Surgical Procedures (Optional)
                        </label>
                        <textarea
                          name="recentSurgicalProcedures"
                          value={formData.medicalInfo.recentSurgicalProcedures}
                          onChange={handleMedicalInfoChange}
                          rows="2"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Current Medications (Optional)
                        </label>
                        <textarea
                          name="currentMedications"
                          value={formData.medicalInfo.currentMedications}
                          onChange={handleMedicalInfoChange}
                          rows="2"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        />
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b pb-2">Chief Complaint</h3>
                    <div>
                      <textarea
                        name="chiefComplaint"
                        value={formData.medicalInfo.chiefComplaint}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="Describe the patient's main complaint or reason for visit"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                        required
                      />
                    </div>
                    <div className="flex justify-end space-x-4 pt-4">
                      <motion.button
                        type="button"
                        onClick={handleEditToggle}
                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="submit"
                        className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Update Patient
                      </motion.button>
                    </div>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  variants={container}
                  initial="hidden"
                  animate="show"
                  className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10"
                >
                  <div className="flex justify-end mb-6">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleEditToggle}
                      className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg"
                    >
                      Edit Profile
                    </motion.button>
                  </div>
                  <motion.div variants={item} className="space-y-6">
                    <div className="bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10">
                      <h2 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                        <FaUser className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Personal Information
                      </h2>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Full Name</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.fullName}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">National ID</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.nationalId}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Phone Number</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.phoneNumber}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Gender</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.gender}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Age</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.age}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Address</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.address || 'Not provided'}</p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Occupation</h3>
                          <p className="mt-1 text-sm text-gray-900">{patientData.occupation || 'Not provided'}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10">
                      <h2 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                        <FaFileMedical className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Medical History
                      </h2>
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Chronic Diseases</h3>
                          <p className="mt-1 text-sm text-gray-900">
                            {patientData.medicalInfo?.chronicDiseases?.length > 0
                              ? patientData.medicalInfo.chronicDiseases.join(', ')
                              : 'None'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Recent Surgical Procedures</h3>
                          <p className="mt-1 text-sm text-gray-900">
                            {patientData.medicalInfo?.recentSurgicalProcedures || 'None'}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">Current Medications</h3>
                          <p className="mt-1 text-sm text-gray-900">
                            {patientData.medicalInfo?.currentMedications || 'None'}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-[#0077B6]/5 p-6 rounded-lg border border-[#0077B6]/10">
                      <h2 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                        <FaProcedures className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Chief Complaint
                      </h2>
                      <p className="text-sm text-gray-900">
                        {patientData.medicalInfo?.chiefComplaint || 'None'}
                      </p>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default PatientProfile;