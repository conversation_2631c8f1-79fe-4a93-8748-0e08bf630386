import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AssistantSidebar from './AssistantSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaSync, FaDownload, FaChartLine, FaUserMd, FaInfoCircle, FaPlus } from 'react-icons/fa';
import AppointmentDetailsModal from './AppointmentDetailsModal';
import AssignStudentModal from './AssignStudentModal';
import { saveAs } from 'file-saver';
import { formatDate, parseDate, isToday, isFuture } from '../utils/dateUtils';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

// Add Appointment Modal Component
const AddAppointmentModal = ({ isOpen, onClose, onSuccess, universityId }) => {
  const [formData, setFormData] = useState({
    date: '',
    time: '',
    type: '',
    chiefComplaint: '',
    notes: '',
    patientId: '',
    studentId: ''
  });
  const [patients, setPatients] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { token } = useAuth();

  useEffect(() => {
    if (isOpen && universityId) {
      fetchPatients();
      fetchStudents();
    }
  }, [isOpen, universityId]);

  const fetchPatients = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/patients/university/${encodeURIComponent(universityId)}`,
        config
      );
      setPatients(response.data || []);
    } catch (err) {
      console.error('Error fetching patients:', err);
      setError('Failed to load patients');
    }
  };

  const fetchStudents = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/students/university/${encodeURIComponent(universityId)}`,
        config
      );
      setStudents(response.data || []);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError('Failed to load students');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      
      // Find the selected patient and student
      const selectedPatient = patients.find(p => p.nationalId === formData.patientId);
      const selectedStudent = students.find(s => s.studentId === formData.studentId);

      if (!selectedPatient || !selectedStudent) {
        setError('Please select both patient and student');
        setLoading(false);
        return;
      }

      // Check if student already has an appointment at the same time
      try {
        const appointmentsResponse = await axios.get(
          `${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`,
          config
        );

        const allAppointments = appointmentsResponse.data || [];
        const conflictingAppointment = allAppointments.find(appt => 
          appt.doctor === selectedStudent.studentId &&
          appt.doctorModel === 'Student' &&
          appt.date === formData.date &&
          appt.time === formData.time &&
          appt.status !== 'cancelled'
        );

        if (conflictingAppointment) {
          setError('This student already has an appointment at the selected time. Please choose a different time or student.');
          setLoading(false);
          return;
        }
      } catch (checkErr) {
        console.error('Error checking student availability:', checkErr);
        // Continue with appointment creation if we can't check availability
      }

      const appointmentData = {
        date: formData.date,
        time: formData.time,
        type: formData.type,
        chiefComplaint: formData.chiefComplaint,
        notes: formData.notes,
        patient: selectedPatient.nationalId,
        doctor: selectedStudent.studentId,
        doctorModel: 'Student',
        status: 'pending'
      };

      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/api/appointments`,
        appointmentData,
        config
      );

      setFormData({
        date: '',
        time: '',
        type: '',
        chiefComplaint: '',
        notes: '',
        patientId: '',
        studentId: ''
      });

      onSuccess(response.data);
      onClose();
    } catch (err) {
      console.error('Error creating appointment:', err);
      setError(err.response?.data?.message || 'Failed to create appointment');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold" style={{ color: websiteColorPalette.primary }}>
              Add New Appointment
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Patient *
              </label>
              <select
                name="patientId"
                value={formData.patientId}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Patient</option>
                {patients.map((patient) => (
                  <option key={patient.nationalId} value={patient.nationalId}>
                    {patient.fullName} - {patient.nationalId}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Student *
              </label>
              <select
                name="studentId"
                value={formData.studentId}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Student</option>
                {students.map((student) => (
                  <option key={student.studentId} value={student.studentId}>
                    {student.name} - {student.studentId}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date *
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                required
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time *
              </label>
              <input
                type="time"
                name="time"
                value={formData.time}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Procedure Type *
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Procedure</option>
                <option value="consultation">Consultation</option>
                <option value="cleaning">Cleaning</option>
                <option value="filling">Filling</option>
                <option value="extraction">Extraction</option>
                <option value="root_canal">Root Canal</option>
                <option value="crown">Crown</option>
                <option value="bridge">Bridge</option>
                <option value="implant">Implant</option>
                <option value="orthodontics">Orthodontics</option>
                <option value="surgery">Surgery</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Chief Complaint *
              </label>
              <textarea
                name="chiefComplaint"
                value={formData.chiefComplaint}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe the patient's main complaint..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional notes..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="flex-1 px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005a8b] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Appointment'}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

const Appointments = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [students, setStudents] = useState([]);
  const [dayFilter, setDayFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  const fetchData = async () => {
    // Test date utilities
    console.log('=== Testing Date Utilities ===');
    console.log('Test 1 - Valid date string:', formatDate('2024-01-15'));
    console.log('Test 2 - Invalid date string:', formatDate('invalid'));
    console.log('Test 3 - Null date:', formatDate(null));
    console.log('Test 4 - Empty string:', formatDate(''));
    console.log('=== End Date Utilities Test ===');
    
    if (!user || !token) {
      setError('Please log in to view appointments.');
      setLoading(false);
      return;
    }

    try {
      setRefreshing(true);
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const universityId = user.university || user.affiliation?.id;

      // Fetch appointments based on university using the admin endpoint
      const appointmentsRes = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/admin/appointments`,
        config
      );

      // Log the first appointment to see its structure
      if (appointmentsRes.data && appointmentsRes.data.length > 0) {
        console.log('First appointment data:', appointmentsRes.data[0]);

        // Check if nationalId exists in the appointments
        const hasNationalId = appointmentsRes.data.some(appt =>
          appt.nationalId || (appt.patient && appt.patient.nationalId)
        );

        console.log('Appointments have nationalId:', hasNationalId);

        // Log all appointments with their nationalId
        appointmentsRes.data.forEach((appt, index) => {
          const nationalId = appt.nationalId || appt.patient?.nationalId;
          console.log(`Appointment ${index + 1} - nationalId:`, nationalId || 'N/A');
        });
      }

      console.log('First appointment sample:', appointmentsRes.data?.[0]);
      if (appointmentsRes.data?.[0]) {
        console.log('All fields in first appointment:', Object.keys(appointmentsRes.data[0]));
        console.log('Date field value:', appointmentsRes.data[0].date);
        console.log('Date field type:', typeof appointmentsRes.data[0].date);
        if (appointmentsRes.data[0].date && typeof appointmentsRes.data[0].date === 'object') {
          console.log('Date object keys:', Object.keys(appointmentsRes.data[0].date));
        }
      }
      setAppointments(appointmentsRes.data || []);

      // We don't need to fetch students separately since we can display appointments without student names
      // This avoids the access denied error from the admin endpoints
      setStudents([]);
      setError('');

      // No error message for empty data - this is normal

      console.log(`Fetched ${appointmentsRes.data.length} appointments for university: ${universityId}`);
    } catch (err) {
      console.error('Fetch error:', err.response?.data || err.message);
      const errorMessage =
        err.response?.status === 404
          ? 'Appointments endpoint not found.'
          : err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.status === 403
          ? 'Access denied. You do not have permission to view this data.'
          : err.response?.data?.message || 'Failed to load appointments';
      setError(errorMessage);
      if (err.response?.status === 401) navigate('/login');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh appointments data
  const handleRefresh = () => {
    fetchData();
  };

  // Handle opening the appointment details modal
  const handleAppointmentClick = (appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailsModal(true);
  };

  // Handle opening the assign student modal
  const handleAssignClick = (e, appointment) => {
    e.stopPropagation(); // Prevent triggering the row click
    setSelectedAppointment(appointment);
    setShowAssignModal(true);
  };

  // Handle appointment creation success
  const handleAppointmentCreated = (newAppointment) => {
    // Ensure _id is present for React key and styling
    const normalizedAppointment = {
      ...newAppointment,
      _id: newAppointment._id || newAppointment.id
    };
    setAppointments(prev => [normalizedAppointment, ...prev]);
    setSuccessMessage('Appointment created successfully!');
    setTimeout(() => setSuccessMessage(''), 5000);
  };

  // Handle assigning a student to an appointment
  const handleAssignStudent = async (appointmentId, student) => {
    try {
      console.log('Assigning student:', { appointmentId, student });
      const config = { headers: { Authorization: `Bearer ${token}` } };

      // Create the update data
      const updateData = {
        doctor: student.studentId,
        doctorModel: 'Student'
      };

      console.log('Update data:', updateData);
      console.log('API URL:', `${process.env.REACT_APP_API_URL}/api/appointments/${appointmentId}/assign`);

      // Make the API call to update the appointment
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/appointments/${appointmentId}/assign`,
        updateData,
        config
      );

      // Get the updated appointment data from the response
      const updatedAppointment = response.data;

      // If we get here, the assignment was successful

      // Find the appointment that was assigned
      const appointmentToAssign = appointments.find(appt => appt._id === appointmentId);

      if (appointmentToAssign) {
        // Create a patient record for the student using appointment information
        try {
          // Extract the national ID from the appointment
          let nationalId = '';
          if (appointmentToAssign.nationalId && appointmentToAssign.nationalId.trim() !== '') {
            nationalId = appointmentToAssign.nationalId.trim();
            console.log('Found nationalId in appointment object:', nationalId);
          } else if (appointmentToAssign.patient && appointmentToAssign.patient.nationalId && appointmentToAssign.patient.nationalId.trim() !== '') {
            nationalId = appointmentToAssign.patient.nationalId.trim();
            console.log('Found nationalId in patient object:', nationalId);
          } else {
            console.log('No valid nationalId found in appointment:', appointmentToAssign);
            // Only generate a temporary ID if we truly have no national ID
            // AND if the appointment doesn't already have a real national ID
            if (!appointmentToAssign.nationalId || appointmentToAssign.nationalId.startsWith('TEMP-')) {
              nationalId = `TEMP-${Date.now()}`;
              console.log('Generated temporary nationalId:', nationalId);

              // Update the appointment with the temporary nationalId
              try {
                // Make an API call to update the appointment with the nationalId
                await axios.put(
                  `${process.env.REACT_APP_API_URL}/api/appointments/${appointmentToAssign._id}`,
                  { nationalId },
                  { headers: { Authorization: `Bearer ${token}` } }
                );
                console.log('Updated appointment with nationalId:', nationalId);
              } catch (err) {
                console.error('Error updating appointment with nationalId:', err);
              }
            } else {
              // Use the existing national ID from the appointment
              nationalId = appointmentToAssign.nationalId;
              console.log('Using existing national ID from appointment:', nationalId);
            }
          }

          // Log the appointment data for debugging
          console.log('Appointment data:', {
            _id: appointmentToAssign._id,
            patient: appointmentToAssign.patient,
            nationalId: nationalId,
            fullName: appointmentToAssign.patient?.fullName || appointmentToAssign.fullName,
            appointmentNationalId: appointmentToAssign.nationalId,
            patientNationalId: appointmentToAssign.patient?.nationalId
          });

          // Extract the patient name
          const patientName = appointmentToAssign.patient?.fullName || appointmentToAssign.fullName || 'Unknown Patient';

          // First, check if the student already has this patient in their list
          let patientExists = false;
          let existingPatientId = null;

          if (nationalId) {
            try {
              // Get all patients for this student
              const studentsPatients = await axios.get(
                `${process.env.REACT_APP_API_URL}/api/patients/student/${student.studentId}`,
                config
              );

              // Check if any patient has the same national ID
              const existingPatient = studentsPatients.data.find(
                patient => patient.nationalId === nationalId
              );

              if (existingPatient) {
                patientExists = true;
                existingPatientId = existingPatient._id;
                console.log(`Found existing patient ${existingPatient.fullName} (${nationalId}) for student ${student.studentId}`);

                // Set flag for success message
                window.patientRecordCreated = false;
              }
            } catch (checkErr) {
              console.log('Error checking student patients:', checkErr.message);
              // Continue to create a new patient if check fails
            }
          }

          if (!patientExists) {
            // Check if we have a valid national ID - only generate temp if we truly don't have one
            if (!nationalId || nationalId.trim() === '' || nationalId.startsWith('TEMP-')) {
              console.warn('No valid national ID found for patient. Using a placeholder value.');
              nationalId = `TEMP-${Date.now()}`; // Generate a temporary ID if none exists
            } else {
              console.log('Using existing national ID:', nationalId);
            }

            // Extract patient information from the appointment
            const patientData = {
              fullName: patientName,
              nationalId: nationalId,
              phoneNumber: appointmentToAssign.patient?.phoneNumber || appointmentToAssign.phoneNumber || '************',
              gender: appointmentToAssign.patient?.gender || appointmentToAssign.gender || 'other',
              age: appointmentToAssign.patient?.age || appointmentToAssign.age || 0,
              address: appointmentToAssign.patient?.address || appointmentToAssign.address || '',
              occupation: appointmentToAssign.patient?.occupation || appointmentToAssign.occupation || '',
              drId: student.studentId, // This is required by the backend - use studentId, not _id
              medicalInfo: {
                chiefComplaint: appointmentToAssign.chiefComplaint || '',
                chronicDiseases: [],
                recentSurgicalProcedures: '',
                currentMedications: ''
              }
            };

            console.log('Creating patient with drId (studentId):', student.studentId);

            console.log('Creating patient with the following data:', {
              fullName: patientData.fullName,
              nationalId: patientData.nationalId,
              phoneNumber: patientData.phoneNumber,
              gender: patientData.gender,
              age: patientData.age,
              drId: patientData.drId
            });

            // Make API call to create a patient record
            try {
              console.log('Creating patient with data:', patientData);
              const createResponse = await axios.post(
                `${process.env.REACT_APP_API_URL}/api/patients`,
                patientData,
                config
              );

              console.log('Patient creation response:', createResponse.data);

              if (createResponse.data.patient) {
                console.log(`Created patient record for ${patientData.fullName} assigned to student ${student.studentId}`);
                window.patientRecordCreated = true;

                // Store the new patient ID for appointment association
                existingPatientId = createResponse.data.patient._id;
              } else {
                console.log('Patient created but no patient data returned');
                window.patientRecordCreated = true;
              }
            } catch (createErr) {
              console.error('Error creating patient:', createErr.response?.data || createErr.message);

              // Check if this is a duplicate key error (patient already exists)
              if (createErr.response?.data?.message?.includes('already exists')) {
                console.log(`Patient with nationalId ${nationalId} already exists in the database`);
                window.patientRecordCreated = false;
              } else {
                // Set error flag but don't re-throw
                window.patientRecordError = true;
                window.patientRecordCreated = false;
              }
            }
          } else {
            // Set flag for success message
            window.patientRecordCreated = false;
            console.log(`Patient record already exists for ${patientName} with student ${student.studentId}`);
          }

          // If we have a patient ID and the appointment has a patient field, update the appointment
          if (existingPatientId && appointmentToAssign.patient) {
            try {
              // Update the appointment to link it to this patient
              await axios.put(
                `${process.env.REACT_APP_API_URL}/api/appointments/${appointmentToAssign._id}/patient`,
                { patientId: existingPatientId },
                config
              );

              console.log(`Updated appointment ${appointmentToAssign._id} to link to patient ${existingPatientId}`);
            } catch (updateErr) {
              console.error('Error updating appointment with patient:', updateErr.message);
            }
          }
        } catch (patientErr) {
          console.error('Error creating patient record:', patientErr.response?.data || patientErr.message);
          // Set flag to indicate error
          window.patientRecordCreated = false;
          window.patientRecordError = true;
          // We don't throw here to avoid interrupting the appointment assignment process
          // Just log the error and continue
        }
      }

      // Update the appointments list with the updated appointment
      const updatedAppointments = appointments.map(appt =>
        appt._id === appointmentId
          ? {
              ...appt,
              doctor: student.studentId,
              doctorModel: 'Student',
              studentName: student.name,
              studentId: student.studentId
            }
          : appt
      );

      setAppointments(updatedAppointments);

      // If this is the currently selected appointment, update it too
      if (selectedAppointment && selectedAppointment._id === appointmentId) {
        setSelectedAppointment({
          ...selectedAppointment,
          doctor: student.studentId,
          doctorModel: 'Student',
          studentName: student.name,
          studentId: student.studentId
        });
      }

      // Show success message with information about patient record
      let successMsg = `Successfully assigned appointment to ${student.name}`;

      // Add information about the patient record if we have it
      if (appointmentToAssign) {
        const patientName = appointmentToAssign.patient?.fullName || appointmentToAssign.fullName || 'Unknown Patient';

        // We'll set these variables in the try/catch block for patient creation
        if (typeof window.patientRecordCreated !== 'undefined') {
          if (window.patientRecordError) {
            successMsg += ` (appointment assigned, but there was an error creating patient record for ${patientName})`;
          } else if (window.patientRecordCreated === true) {
            successMsg += ` and created new patient record for ${patientName}`;
          } else {
            successMsg += ` (existing patient record found for ${patientName})`;
          }
          // Reset the flags
          delete window.patientRecordCreated;
          delete window.patientRecordError;
        }
      }

      setSuccessMessage(successMsg);
      setTimeout(() => setSuccessMessage(''), 5000);

      // Refresh the appointments data to ensure consistency
      setTimeout(() => {
        fetchData();
      }, 1000);

      return updatedAppointment;
    } catch (err) {
      console.error('Error assigning student:', err.response?.data || err.message);
      throw new Error(err.response?.data?.message || 'Failed to assign student');
    }
  };

  useEffect(() => {
    fetchData();
  }, [user, token, navigate]);

  const downloadAppointments = () => {
    if (filteredAppointments.length === 0) {
      setError('No appointments to download.');
      return;
    }

    try {
      const headers = ['Date', 'Time', 'Patient', 'Procedure', 'Status', 'Student', 'University'];
      const data = filteredAppointments.map((appt) => [
        new Date(appt.date).toLocaleDateString('en-US'),
        appt.time || 'N/A',
        appt.patient?.fullName || appt.fullName || 'Unknown',
        appt.treatment || appt.type || 'N/A',
        appt.status,
        appt.studentName || appt.doctor || 'N/A',
        appt.university || user.university || user.affiliation?.id || 'N/A',
      ]);

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...data.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      // Create blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
      saveAs(blob, `appointments-${new Date().toISOString().slice(0, 10)}.csv`);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
      successMessage.textContent = 'Appointments exported successfully!';
      document.body.appendChild(successMessage);

      // Remove success message after 3 seconds
      setTimeout(() => {
        document.body.removeChild(successMessage);
      }, 3000);
    } catch (err) {
      console.error('Error downloading appointments:', err);
      setError('Failed to download appointments. Please try again.');
    }
  };

  const filterAppointments = () => {
    let filtered = appointments;

    // Filter by date
    if (dayFilter === 'today') {
      filtered = filtered.filter((a) => isToday(a.date));
    } else if (dayFilter === 'tomorrow') {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      filtered = filtered.filter((a) => {
        const apptDate = parseDate(a.date);
        if (!apptDate) return false;
        return apptDate.toDateString() === tomorrow.toDateString();
      });
    } else if (dayFilter === 'week') {
      const weekEnd = new Date();
      weekEnd.setDate(weekEnd.getDate() + 7);
      filtered = filtered.filter((a) => {
        const apptDate = parseDate(a.date);
        if (!apptDate) return false;
        const today = new Date();
        return apptDate >= today && apptDate <= weekEnd;
      });
    }

    // Filter by status
    if (statusFilter) {
      filtered = filtered.filter((a) => a.status === statusFilter);
    }

    // Sort by date and time
    return filtered.sort((a, b) => {
      const dateA = parseDate(a.date);
      const dateB = parseDate(b.date);
      if (!dateA || !dateB) return 0;
      if (dateA.getTime() !== dateB.getTime()) return dateA - dateB;
      return (a.time || '').localeCompare(b.time || '');
    });
  };

  const filteredAppointments = filterAppointments();

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Appointments</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Manage and view all appointments in your university</p>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="space-y-6 sm:space-y-8"
              >
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-3 sm:py-4 px-4 sm:px-6 text-center font-medium text-xs sm:text-sm"
                      style={{
                        color: websiteColorPalette.primary,
                        borderBottom: `2px solid ${websiteColorPalette.primary}`
                      }}
                    >
                      Appointment Management
                    </div>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                      <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                        <FaCalendarAlt className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                        Appointment List
                      </h2>
                      <div className="flex items-center gap-2 sm:gap-3">
                        <FaChartLine className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                        <span className="font-medium text-sm sm:text-base" style={{ color: websiteColorPalette.primary }}>Total: {appointments.length}</span>
                        <button
                          onClick={() => setShowAddModal(true)}
                          className="px-3 sm:px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005a8b] transition-colors flex items-center text-xs sm:text-sm"
                          title="Add Appointment"
                        >
                          <FaPlus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                          Add Appointment
                        </button>
                        <button
                          onClick={downloadAppointments}
                          className="px-3 sm:px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005a8b] transition-colors flex items-center text-xs sm:text-sm"
                          title="Download CSV"
                        >
                          <FaDownload className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                          Download CSV
                        </button>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
                      <select
                        value={dayFilter}
                        onChange={(e) => setDayFilter(e.target.value)}
                        className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg bg-white text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Dates</option>
                        <option value="today">Today</option>
                        <option value="tomorrow">Tomorrow</option>
                        <option value="week">This Week</option>
                      </select>
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg bg-white text-xs sm:text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>

                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th className="hidden sm:table-cell px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="hidden md:table-cell px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredAppointments.map((appt) => (
                            <motion.tr
                              key={appt._id}
                              variants={item}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleAppointmentClick(appt)}
                            >
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                {formatDate(appt.date, {
                                  weekday: 'short',
                                  month: 'short',
                                  day: 'numeric',
                                })}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{appt.time || 'N/A'}</td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.patient?.fullName || appt.fullName || 'Unknown'}
                                <div className="text-xs text-gray-400">
                                  ID: {appt.nationalId || appt.patient?.nationalId || 'N/A'}
                                </div>
                              </td>
                              <td className="hidden sm:table-cell px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.treatment || appt.type || 'N/A'}
                                {appt.chiefComplaint && <div className="text-xs text-gray-400 mt-1">Chief complaint: {appt.chiefComplaint}</div>}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    appt.status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : appt.status === 'cancelled'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {appt.status}
                                </span>
                              </td>
                              <td className="hidden md:table-cell px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.studentName || appt.doctor || 'N/A'}
                                {appt.studentId && <div className="text-xs text-gray-400">ID: {appt.studentId}</div>}
                                {appt.doctorModel && <div className="text-xs text-gray-400">Type: {appt.doctorModel}</div>}
                              </td>
                              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                <div className="flex space-x-1 sm:space-x-2">
                                  <button
                                    onClick={(e) => handleAssignClick(e, appt)}
                                    className="transition-colors p-1 rounded-full hover:bg-blue-50"
                                    style={{
                                      color: websiteColorPalette.primary
                                    }}
                                    title="Assign to Student"
                                  >
                                    <FaUserMd className="h-4 w-4 sm:h-5 sm:w-5" />
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleAppointmentClick(appt);
                                    }}
                                    className="transition-colors p-1 rounded-full hover:bg-gray-100"
                                    style={{
                                      color: '#4b5563'
                                    }}
                                    title="View Details"
                                  >
                                    <FaInfoCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                                  </button>
                                </div>
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="fixed bottom-4 right-4 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fade-in-out"
          style={{ backgroundColor: websiteColorPalette.accent }}>
          {successMessage}
        </div>
      )}

      {/* Appointment Details Modal */}
      <AppointmentDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        appointment={selectedAppointment}
      />

      {/* Assign Student Modal */}
      <AssignStudentModal
        isOpen={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        appointment={selectedAppointment}
        onAssign={handleAssignStudent}
      />

      {/* Add Appointment Modal */}
      <AddAppointmentModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handleAppointmentCreated}
        universityId={user?.university || user?.affiliation?.id}
      />
    </div>
  );
};

export default Appointments;
