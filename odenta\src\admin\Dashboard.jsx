import { useState, useEffect } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaUserAlt,
  FaUsers,
  FaUserGraduate,
  FaCalendarAlt,
  FaChartLine,
  FaStar,
  FaNewspaper,
  FaArrowRight,
  FaBell,
  FaUserNurse,
  FaUserMd,
  FaFileMedical
} from 'react-icons/fa';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const AdminDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [counts, setCounts] = useState({
    patients: 0,
    students: 0,
    assistants: 0,
    supervisors: 0,
    appointments: 0,
    reviews: 0,
  });
  const [analytics, setAnalytics] = useState({
    appointments: {
      pending: 0,
      completed: 0,
      cancelled: 0,
      today: 0,
      thisWeek: 0
    },
    reviews: {
      pending: 0,
      accepted: 0,
      declined: 0,
      avgProcedureQuality: 0,
      avgPatientInteraction: 0
    },
    procedureTypes: {}
  });
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [treatmentSheets, setTreatmentSheets] = useState([]);
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view your dashboard.');
        setLoading(false);
        return;
      }

      if (!user.university) {
        setError('User profile incomplete. Missing university information.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };

        // First, fetch basic data (patients, students, assistants, supervisors, appointments)
        const basicRequests = [
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/patients?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/students?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/assistants?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/appointments?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/admin/treatment-sheets`, config)
        ];

        // Execute basic requests and handle errors individually
        const [patientsRes, studentsRes, assistantsRes, supervisorsRes, appointmentsRes, treatmentSheetsRes] = await Promise.all(
          basicRequests.map(request =>
            request.catch(error => {
              console.error('API request failed:', error.message);
              return { data: [] }; // Return empty data on error
            })
          )
        );

        // Store students for later use
        const students = studentsRes.data || [];

        // Try to fetch news separately to handle potential 404
        let newsRes = { data: [] };
        try {
          newsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/news?university=${encodeURIComponent(user.university)}`, config);
        } catch (newsError) {
          console.log('News API not available:', newsError.message);
          // Continue without news data
        }

        // Now fetch reviews for each student in the university
        console.log('Fetching reviews for students...');
        let allReviews = [];

        // Create an array of promises for fetching reviews for each student
        const reviewPromises = students.map(student => {
          const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;
          console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);

          return axios.get(url, config)
            .then(response => {
              console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);
              return response;
            })
            .catch(error => {
              console.error(`Error fetching reviews for student ${student.name} (ID: ${student.studentId}):`, error.message);
              // Try with _id as fallback
              console.log(`Trying fallback with _id for student ${student.name}`);
              return axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student._id}`, config)
                .then(response => {
                  console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);
                  return response;
                })
                .catch(fallbackError => {
                  console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);
                  return { data: [] }; // Return empty array on error
                });
            });
        });

        // Execute all review promises in parallel
        const reviewsResults = await Promise.all(reviewPromises);

        // Combine all reviews into a single array
        reviewsResults.forEach(result => {
          if (result.data && Array.isArray(result.data)) {
            allReviews = [...allReviews, ...result.data];
          }
        });

        console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);

        // Remove any duplicate reviews (in case a student appears in multiple queries)
        const uniqueReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());

        // Filter out signature storage reviews
        const filteredReviews = uniqueReviews.filter(review =>
          !(review.patientId && review.patientId.nationalId === 'signature-storage')
        );

        // Basic counts
        setCounts({
          patients: patientsRes.data.length,
          students: studentsRes.data.length,
          assistants: assistantsRes.data.length,
          supervisors: supervisorsRes.data.length,
          appointments: appointmentsRes.data.length,
          reviews: filteredReviews.length,
        });

        // Process appointments data
        const appointments = appointmentsRes.data || [];
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Calculate this week's appointments
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // End of week (Saturday)

        const appointmentsAnalytics = {
          pending: appointments.filter(a => a.status === 'pending').length,
          completed: appointments.filter(a => a.status === 'completed').length,
          cancelled: appointments.filter(a => a.status === 'cancelled').length,
          today: appointments.filter(a => {
            const apptDate = new Date(a.date);
            apptDate.setHours(0, 0, 0, 0);
            return apptDate.getTime() === today.getTime();
          }).length,
          thisWeek: appointments.filter(a => {
            const apptDate = new Date(a.date);
            apptDate.setHours(0, 0, 0, 0);
            return apptDate >= startOfWeek && apptDate <= endOfWeek;
          }).length
        };

        // Process reviews data
        const pendingReviews = filteredReviews.filter(r => r.status === 'pending');
        const acceptedReviews = filteredReviews.filter(r => r.status === 'accepted');
        const declinedReviews = filteredReviews.filter(r => r.status === 'declined' || r.status === 'denied');

        // Calculate average ratings if available
        let avgProcedureQuality = 0;
        let avgPatientInteraction = 0;

        // Check different possible rating structures
        const reviewsWithRatings = filteredReviews.filter(r => {
          // Check direct fields
          if ((r.procedureQuality && typeof r.procedureQuality === 'number') ||
              (r.patientInteraction && typeof r.patientInteraction === 'number')) {
            return true;
          }

          // Check nested ratings object
          if (r.ratings &&
              ((r.ratings.procedureQuality && typeof r.ratings.procedureQuality === 'number') ||
               (r.ratings.patientInteraction && typeof r.ratings.patientInteraction === 'number'))) {
            return true;
          }

          return false;
        });

        if (reviewsWithRatings.length > 0) {
          // Calculate procedure quality
          let procedureQualitySum = 0;
          let procedureQualityCount = 0;

          reviewsWithRatings.forEach(r => {
            if (r.procedureQuality && typeof r.procedureQuality === 'number') {
              procedureQualitySum += r.procedureQuality;
              procedureQualityCount++;
            } else if (r.ratings && r.ratings.procedureQuality && typeof r.ratings.procedureQuality === 'number') {
              procedureQualitySum += r.ratings.procedureQuality;
              procedureQualityCount++;
            }
          });

          // Calculate patient interaction
          let patientInteractionSum = 0;
          let patientInteractionCount = 0;

          reviewsWithRatings.forEach(r => {
            if (r.patientInteraction && typeof r.patientInteraction === 'number') {
              patientInteractionSum += r.patientInteraction;
              patientInteractionCount++;
            } else if (r.ratings && r.ratings.patientInteraction && typeof r.ratings.patientInteraction === 'number') {
              patientInteractionSum += r.ratings.patientInteraction;
              patientInteractionCount++;
            }
          });

          avgProcedureQuality = procedureQualityCount > 0 ? procedureQualitySum / procedureQualityCount : 0;
          avgPatientInteraction = patientInteractionCount > 0 ? patientInteractionSum / patientInteractionCount : 0;
        }

        // Count procedure types
        const procedureTypes = {};
        filteredReviews.forEach(review => {
          if (review.procedureType) {
            procedureTypes[review.procedureType] = (procedureTypes[review.procedureType] || 0) + 1;
          }
        });

        // Set analytics state
        setAnalytics({
          appointments: appointmentsAnalytics,
          reviews: {
            pending: pendingReviews.length,
            accepted: acceptedReviews.length,
            declined: declinedReviews.length,
            avgProcedureQuality: avgProcedureQuality.toFixed(1),
            avgPatientInteraction: avgPatientInteraction.toFixed(1)
          },
          procedureTypes
        });

        // Set news
        setNews(newsRes.data || []);

        // Set treatment sheets
        setTreatmentSheets(treatmentSheetsRes.data || []);

        if (
          appointmentsRes.data.length === 0 &&
          allReviews.length === 0 &&
          patientsRes.data.length === 0 &&
          treatmentSheetsRes.data.length === 0
        ) {
          // Remove the error message for no data found
          // setError('No data found for your university.');
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage =
          err.response?.status === 404
            ? 'Data endpoint not found.'
            : err.response?.status === 401
            ? 'Unauthorized. Please log in again.'
            : err.response?.data?.message || 'Failed to load data';
        setError(errorMessage);
        if (err.response?.status === 401) navigate('/login');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate]);

  // Define a consistent color palette based on the website design system
  const colorPalette = {
    primary: { main: `${websiteColorPalette.primary}CC`, border: websiteColorPalette.primary },
    secondary: { main: `${websiteColorPalette.secondary}CC`, border: websiteColorPalette.secondary },
    accent: { main: `${websiteColorPalette.accent}CC`, border: websiteColorPalette.accent },
    red: { main: 'rgba(239, 68, 68, 0.8)', border: 'rgba(239, 68, 68, 1)' },
    orange: { main: 'rgba(249, 115, 22, 0.8)', border: 'rgba(249, 115, 22, 1)' },
    amber: { main: 'rgba(245, 158, 11, 0.8)', border: 'rgba(245, 158, 11, 1)' },
  };

  // Chart data for analytics preview
  const overviewChartData = {
    labels: ['Patients', 'Students', 'Assistants', 'Supervisors', 'Appointments', 'Reviews'],
    datasets: [
      {
        label: 'Counts',
        data: [
          counts.patients,
          counts.students,
          counts.assistants,
          counts.supervisors,
          counts.appointments,
          counts.reviews,
        ],
        backgroundColor: [
          colorPalette.primary.main,
          colorPalette.secondary.main,
          colorPalette.accent.main,
          colorPalette.primary.main,
          colorPalette.secondary.main,
          colorPalette.accent.main,
        ],
        borderColor: [
          colorPalette.primary.border,
          colorPalette.secondary.border,
          colorPalette.accent.border,
          colorPalette.primary.border,
          colorPalette.secondary.border,
          colorPalette.accent.border,
        ],
        borderWidth: 1,
        borderRadius: 6,
        hoverOffset: 4,
      },
    ],
  };

  // Appointments chart data
  const appointmentsChartData = {
    labels: ['Pending', 'Completed', 'Cancelled'],
    datasets: [
      {
        data: [
          analytics.appointments.pending,
          analytics.appointments.completed,
          analytics.appointments.cancelled,
        ],
        backgroundColor: [
          colorPalette.amber.main,
          colorPalette.accent.main,
          colorPalette.red.main,
        ],
        borderColor: [
          colorPalette.amber.border,
          colorPalette.accent.border,
          colorPalette.red.border,
        ],
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Reviews chart data
  const reviewsChartData = {
    labels: ['Pending', 'Accepted', 'Declined'],
    datasets: [
      {
        data: [
          analytics.reviews.pending,
          analytics.reviews.accepted,
          analytics.reviews.declined,
        ],
        backgroundColor: [
          colorPalette.amber.main,
          colorPalette.accent.main,
          colorPalette.red.main,
        ],
        borderColor: [
          colorPalette.amber.border,
          colorPalette.accent.border,
          colorPalette.red.border,
        ],
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Procedures by type chart data
  const proceduresByType = {};
  treatmentSheets.forEach(sheet => {
    const type = sheet.type || 'Other';
    proceduresByType[type] = (proceduresByType[type] || 0) + 1;
  });
  const proceduresByTypeChartData = {
    labels: Object.keys(proceduresByType),
    datasets: [
      {
        data: Object.values(proceduresByType),
        backgroundColor: Object.keys(proceduresByType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].main;
        }),
        borderColor: Object.keys(proceduresByType).map((_, index) => {
          const colorKeys = Object.keys(colorPalette);
          return colorPalette[colorKeys[index % colorKeys.length]].border;
        }),
        borderWidth: 1,
        hoverOffset: 8,
      },
    ],
  };

  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      title: {
        display: true,
        text: 'University Overview',
        font: {
          family: "'Inter', sans-serif",
          size: 16,
          weight: 'bold'
        },
        color: '#1e3a8a',
        padding: {
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(226, 232, 240, 0.6)'
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            family: "'Inter', sans-serif",
            size: 11
          },
          color: '#64748b'
        }
      }
    }
  };

  // Pie chart options
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          font: {
            family: "'Inter', sans-serif",
            size: 12
          },
          padding: 20,
          usePointStyle: true,
          boxWidth: 8
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e3a8a',
        bodyColor: '#4b5563',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        bodyFont: {
          family: "'Inter', sans-serif"
        },
        titleFont: {
          family: "'Inter', sans-serif",
          weight: 'bold'
        }
      }
    },
    cutout: '60%',
    radius: '90%'
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Admin Dashboard</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Welcome back, {user?.name || 'Admin'}</p>
              </div>
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="space-y-6 sm:space-y-8"
              >
                <motion.div
                  variants={container}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
                >
                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/admin/appointments" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaCalendarAlt className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>Total Appointments</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{counts.appointments}</p>
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/admin/reviews" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaStar className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>Total Reviews</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{counts.reviews}</p>
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    variants={item}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                  >
                    <Link to="/admin/analytics" className="flex items-center">
                      <div className="bg-blue-50 w-12 h-12 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                        <FaFileMedical className={`h-5 w-5 sm:h-6 sm:w-6 text-[${websiteColorPalette.primary}]`} />
                      </div>
                      <div>
                        <p className={`text-xs sm:text-sm font-medium text-[${websiteColorPalette.text}]`}>Total Procedures</p>
                        <p className={`text-xl sm:text-2xl font-bold text-[${websiteColorPalette.primary}]`}>{treatmentSheets.length}</p>
                      </div>
                    </Link>
                  </motion.div>
                </motion.div>
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <nav className="flex flex-wrap -mb-px">
                      <button
                        className={`py-3 sm:py-4 px-3 sm:px-6 font-medium text-xs sm:text-sm border-b-2 ${
                          activeTab === 'overview'
                            ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('overview')}
                      >
                        Overview
                      </button>
                      <button
                        className={`py-3 sm:py-4 px-3 sm:px-6 font-medium text-xs sm:text-sm border-b-2 ${
                          activeTab === 'appointments'
                            ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('appointments')}
                      >
                        Appointments
                      </button>
                      <button
                        className={`py-3 sm:py-4 px-3 sm:px-6 font-medium text-xs sm:text-sm border-b-2 ${
                          activeTab === 'reviews'
                            ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('reviews')}
                      >
                        Reviews
                      </button>
                      <button
                        className={`py-3 sm:py-4 px-3 sm:px-6 font-medium text-xs sm:text-sm border-b-2 ${
                          activeTab === 'procedures'
                            ? `border-[${websiteColorPalette.primary}] text-[${websiteColorPalette.primary}]`
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('procedures')}
                      >
                        Procedures
                      </button>
                    </nav>
                  </div>
                  <div className="p-4 sm:p-6">
                    {activeTab === 'overview' && (
                      <div>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                          <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                            <FaChartLine className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                            University Overview
                          </h2>
                          <Link
                            to="/admin/analytics"
                            className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm`}
                          >
                            <span>View Detailed Analytics</span>
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </Link>
                        </div>
                        <div className="bg-blue-50 p-4 sm:p-6 rounded-lg">
                          <div className="h-64 sm:h-96">
                            <Bar data={overviewChartData} options={barChartOptions} />
                          </div>
                        </div>
                      </div>
                    )}
                    {activeTab === 'appointments' && (
                      <div>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                          <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                            <FaCalendarAlt className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Appointment Analytics
                          </h2>
                          <Link
                            to="/admin/appointments"
                            className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm`}
                          >
                            <span>View All Appointments</span>
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </Link>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Appointment Status</h3>
                            <div className="h-48 sm:h-64">
                              <Pie data={appointmentsChartData} options={pieChartOptions} />
                            </div>
                          </div>
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Appointment Metrics</h3>
                            <div className="space-y-3 sm:space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Today's Appointments:</span>
                                <span className="font-semibold">{analytics.appointments.today}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">This Week's Appointments:</span>
                                <span className="font-semibold">{analytics.appointments.thisWeek}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Pending Appointments:</span>
                                <span className="font-semibold">{analytics.appointments.pending}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Completed Appointments:</span>
                                <span className="font-semibold">{analytics.appointments.completed}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Cancelled Appointments:</span>
                                <span className="font-semibold">{analytics.appointments.cancelled}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {activeTab === 'reviews' && (
                      <div>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                          <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                            <FaStar className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Review Analytics
                          </h2>
                          <Link
                            to="/admin/reviews"
                            className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm`}
                          >
                            <span>View All Reviews</span>
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </Link>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Review Status</h3>
                            <div className="h-48 sm:h-64">
                              <Pie data={reviewsChartData} options={pieChartOptions} />
                            </div>
                          </div>
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Review Metrics</h3>
                            <div className="space-y-3 sm:space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Pending Reviews:</span>
                                <span className="font-semibold">{analytics.reviews.pending}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Accepted Reviews:</span>
                                <span className="font-semibold">{analytics.reviews.accepted}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Declined Reviews:</span>
                                <span className="font-semibold">{analytics.reviews.declined}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Avg. Procedure Quality:</span>
                                <span className="font-semibold">{analytics.reviews.avgProcedureQuality}/5</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Avg. Patient Interaction:</span>
                                <span className="font-semibold">{analytics.reviews.avgPatientInteraction}/5</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    {activeTab === 'procedures' && (
                      <div>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                          <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                            <FaFileMedical className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                            Procedures Analytics
                          </h2>
                          <Link
                            to="/admin/analytics"
                            className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center text-sm`}
                          >
                            <span>View Detailed Analytics</span>
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </Link>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Procedures by Type</h3>
                            <div className="h-48 sm:h-64">
                              <Pie data={proceduresByTypeChartData} options={pieChartOptions} />
                            </div>
                          </div>
                          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg flex flex-col justify-center items-center">
                            <h3 className="text-base sm:text-lg font-medium text-gray-700 mb-3">Total Procedures</h3>
                            <p className="text-3xl sm:text-4xl font-bold text-[${websiteColorPalette.primary}]">{treatmentSheets.length}</p>
                            <p className="text-gray-500 mt-2 text-center text-sm">All sheets submitted by students in your university</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;