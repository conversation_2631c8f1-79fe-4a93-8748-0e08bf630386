#!/usr/bin/env node

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');

// Import your existing server setup
const { connectFirestore } = require('./config/firebaseDb');
const config = require('./config/config');
const errorHandler = require('./utils/errorHandler');

// Import routes
const authRoutes = require('./routes/authRoutes');
const patientRoutes = require('./routes/patientRoutes');
const studentRoutes = require('./routes/studentRoutes');
const supervisorRoutes = require('./routes/supervisorRoutes');
const adminRoutes = require('./routes/adminRoutes');
const superadminRoutes = require('./routes/superadminRoutes');
const appointmentRoutes = require('./routes/appointmentRoutes');
const reviewRoutes = require('./routes/reviewRoutes');
const teethChartRoutes = require('./routes/teethChartRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const universityRoutes = require('./routes/universityRoutes');
const newsRoutes = require('./routes/newsRoutes');
const accountRoutes = require('./routes/accountRoutes');
const procedureRequestRoutes = require('./routes/procedureRequestRoutes');
const labRequestRoutes = require('./routes/labRequestRoutes');
const assistantRoutes = require('./routes/assistantRoutes');

const app = express();
const server = http.createServer(app);

// Define allowed origins for both Express and Socket.io
const allowedOrigins = [
  'http://localhost:3000',
  'https://odenta.vercel.app',
  'https://Odenta.vercel.app',
  'https://odenta.vercel.app/',
  'https://Odenta.vercel.app/',
  'https://odenta-zeta.vercel.app',
  'https://odenta-backend-production-1b94.up.railway.app'
];

const io = socketIo(server, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"]
  }
});

// Connect to Firebase Firestore
try {
  connectFirestore();
  console.log('✅ Firebase connected successfully');
} catch (error) {
  console.error('❌ Firebase connection failed:', error.message);
}

// Enhanced CORS configuration
const cors = require('cors');

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));

// Middleware
app.use(express.json({ limit: config.MAX_FILE_SIZE }));
app.use(express.urlencoded({ extended: true, limit: config.MAX_FILE_SIZE }));

// Serve static files from uploads directory
const path = require('path');
app.use('/uploads', express.static(path.join(__dirname, config.UPLOAD_PATH.replace('./', ''))));

// Make Socket.io instance available to routes/controllers
app.set('io', io);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/students', studentRoutes);
app.use('/api/supervisors', supervisorRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/superadmin', superadminRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/dental', teethChartRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/universities', universityRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/procedure-requests', procedureRequestRoutes);
app.use('/api/lab-requests', labRequestRoutes);
app.use('/api/assistant', assistantRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.send('Welcome to the ODenta API!');
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date(),
    environment: config.NODE_ENV,
    port: config.PORT
  });
});

// Socket.io setup
io.on('connection', (socket) => {
  console.log('New client connected:', socket.id);

  socket.on('join', (userId) => {
    if (userId) {
      socket.join(userId);
      console.log(`User ${userId} joined their room`);
    }
  });

  socket.on('dentalChartUpdate', (data) => {
    if (data.patientId) {
      io.to(data.patientId).emit('dentalChartUpdated', data);
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  socket.on('error', (err) => {
    console.error('Socket error:', err);
  });
});

// Error handling middleware
app.use((req, res, next) => {
  const error = new Error('Not Found');
  error.status = 404;
  next(error);
});

app.use(errorHandler);

// Server setup with better error handling
const PORT = config.PORT || 5000;

console.log('🚀 Starting ODenta Backend Server...');
console.log('📡 Port:', PORT);
console.log('🌍 Environment:', config.NODE_ENV);
console.log('🔗 Frontend URL:', config.FRONTEND_URL);

// Start server with proper error handling
const serverInstance = server.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Failed to start server:', err);
    process.exit(1);
  }
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 Server URL: http://0.0.0.0:${PORT}`);
  console.log(`🔗 Health check: http://0.0.0.0:${PORT}/api/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  serverInstance.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  serverInstance.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

module.exports = server; 