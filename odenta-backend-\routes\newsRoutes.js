const express = require('express');
const router = express.Router();
const { createNews, createSpecificNews, getNews, getAllNews, getAdminNews } = require('../controllers/newsController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Get all news (for superadmin)
router.get('/', auth, role('superadmin'), getAllNews);

// Get news for admin users (university-specific)
router.get('/admin', auth, role('admin', 'superadmin'), getAdminNews);

// Get news for specific user/university
router.get('/public', getNews);

// Create general news (superadmin only)
router.post('/', auth, role('superadmin'), createNews);

// Create specific news (superadmin only)
router.post('/specific', auth, role('superadmin'), createSpecificNews);

module.exports = router;