const express = require('express');
const router = express.Router();
const accountController = require('../controllers/accountController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

router.get('/', auth, role('superadmin'), accountController.getAllAccounts);
router.post('/', auth, role('superadmin'), accountController.createAccount);
router.post('/google', auth, role('superadmin'), accountController.createGoogleAccount);
router.put('/:id', auth, role('superadmin'), accountController.updateAccount);
router.delete('/:id', auth, role('superadmin'), accountController.deleteAccount);
router.post('/:id/reset-password', auth, role('superadmin'), accountController.resetPassword);

module.exports = router;