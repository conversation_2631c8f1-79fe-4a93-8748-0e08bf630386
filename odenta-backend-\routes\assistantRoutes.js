const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const role = require('../middleware/role');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase');

// Assistant analytics endpoint
router.get('/analytics', auth, role('assistant'), async (req, res) => {
  try {
    
    // If user doesn't have university set, try to get it from their profile
    let userUniversity = req.user.university || req.user.affiliation?.id || req.user.affiliation;
    
    // Try multiple ways to find the university
    if (!userUniversity) {
      // Method 1: Try by assistantId
      if (req.user.assistantId) {
        try {
          const assistantProfile = await FirestoreHelpers.findOne(COLLECTIONS.ASSISTANTS, { 
            field: 'assistantId', 
            operator: '==', 
            value: req.user.assistantId 
          });
          if (assistantProfile) {
            userUniversity = assistantProfile.university || assistantProfile.affiliation?.id || assistantProfile.affiliation;
          }
        } catch (assistantErr) {
          console.error('Error fetching assistant profile by assistantId:', assistantErr);
        }
      }
      
      // Method 2: Try by user ID
      if (!userUniversity && req.user.id) {
        try {
          const userProfile = await FirestoreHelpers.findById(COLLECTIONS.ASSISTANTS, req.user.id);
          if (userProfile) {
            userUniversity = userProfile.university || userProfile.affiliation?.id || userProfile.affiliation;
          }
        } catch (profileErr) {
          console.error('Error fetching user profile by ID:', profileErr);
        }
      }
      
      // Method 3: Try by email
      if (!userUniversity && req.user.email) {
        try {
          const userProfile = await FirestoreHelpers.findOne(COLLECTIONS.ASSISTANTS, { 
            field: 'email', 
            operator: '==', 
            value: req.user.email 
          });
          if (userProfile) {
            userUniversity = userProfile.university || userProfile.affiliation?.id || userProfile.affiliation;
          }
        } catch (emailErr) {
          console.error('Error fetching user profile by email:', emailErr);
        }
      }
    }
    
    // If no university found, try to get all data as fallback
    if (!userUniversity) {
      userUniversity = 'ALL'; // Use a special marker to get all data
    }
    
    // Get all students for the university first (needed for patient filtering)
    let allStudents = [];
    try {
      allStudents = await FirestoreHelpers.getAll(COLLECTIONS.STUDENTS);
    } catch (studentsErr) {
      console.error('Error fetching students:', studentsErr);
      allStudents = [];
    }
    
    const universityStudents = allStudents.filter(student => {
      const studentUniversity = student.university || student.affiliation?.id || student.affiliation;
      
      // If userUniversity is 'ALL', include all students
      if (userUniversity === 'ALL') {
        return true;
      }
      
      return studentUniversity === userUniversity;
    });
    
    // Get all patients for the university
    let allPatients = [];
    try {
      allPatients = await FirestoreHelpers.getAll(COLLECTIONS.PATIENTS);
    } catch (patientsErr) {
      console.error('Error fetching patients:', patientsErr);
      allPatients = [];
    }
    
    // Filter patients by university - check multiple possible fields
    // Since patients don't have university info, we need to filter by students' university
    const universityPatients = allPatients.filter(patient => {
      // If userUniversity is 'ALL', include all patients
      if (userUniversity === 'ALL') {
        return true;
      }
      
      // If patient has a drId (assigned to a student), check if that student belongs to the university
      if (patient.drId && patient.drId !== 'unassigned' && patient.drId !== 'N/A') {
        const assignedStudent = universityStudents.find(student => student.studentId === patient.drId);
        return assignedStudent !== undefined;
      }

      // If patient is not assigned to any student, include them (assistants can manage unassigned patients)
      // Unassigned patients can be assigned to any student in the university
      return true;
    });



    // Calculate analytics
    let total = 0;
    let assigned = 0;
    let unassigned = 0;
    let newThisMonth = 0;
    let male = 0;
    let female = 0;
    let ageGroups = {};
    let studentDistribution = {};
    let monthlyGrowth = [];
    
    try {
      total = universityPatients.length;
      assigned = universityPatients.filter(patient => patient.drId).length;
      unassigned = total - assigned;
      
      // Calculate new patients this month
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      newThisMonth = universityPatients.filter(patient => {
        if (!patient.createdAt) return false;
        try {
          const createdDate = new Date(patient.createdAt);
          return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
        } catch (dateErr) {
          console.error('Error parsing patient createdAt date:', dateErr);
          return false;
        }
      }).length;

      // Gender distribution
      male = universityPatients.filter(patient => patient.gender === 'male').length;
      female = universityPatients.filter(patient => patient.gender === 'female').length;

      // Age groups
      ageGroups = {};
      universityPatients.forEach(patient => {
        if (patient.age) {
          const ageGroup = patient.age < 18 ? 'Under 18' :
                          patient.age < 30 ? '18-29' :
                          patient.age < 50 ? '30-49' :
                          patient.age < 70 ? '50-69' : '70+';
          ageGroups[ageGroup] = (ageGroups[ageGroup] || 0) + 1;
        }
      });

      // Student distribution
      studentDistribution = {};
      universityPatients.forEach(patient => {
        if (patient.drId) {
          const student = universityStudents.find(s => s.studentId === patient.drId);
          if (student) {
            studentDistribution[student.name] = (studentDistribution[student.name] || 0) + 1;
          }
        }
      });

      // Monthly growth (last 6 months)
      monthlyGrowth = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthName = date.toLocaleString('default', { month: 'short' });
        const year = date.getFullYear();
        
        const count = universityPatients.filter(patient => {
          if (!patient.createdAt) return false;
          try {
            const createdDate = new Date(patient.createdAt);
            return createdDate.getMonth() === date.getMonth() && createdDate.getFullYear() === year;
          } catch (dateErr) {
            console.error('Error parsing patient createdAt date for monthly growth:', dateErr);
            return false;
          }
        }).length;
        
        monthlyGrowth.push({ month: monthName, count });
      }
    } catch (calcErr) {
      console.error('Error calculating analytics:', calcErr);
    }

    res.json({
      total,
      assigned,
      unassigned,
      newThisMonth,
      male,
      female,
      ageGroups,
      studentDistribution,
      monthlyGrowth
    });
  } catch (error) {
    console.error('Error fetching assistant analytics:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router; 
