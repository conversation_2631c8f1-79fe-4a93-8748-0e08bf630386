import { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaUser, FaUserPlus, FaSearch, FaEdit, FaTrash, FaFileMedical, FaProcedures, FaPills, FaUpload, FaXRay, FaImages } from 'react-icons/fa';
import { RiAiGenerate } from 'react-icons/ri';

const chronicDiseases = [
  'Diabetes Mellitus',
  'Hypertension',
  'Cardiovascular Disease',
  'Coagulopathy / Bleeding Disorders',
  'Thyroid Disorders',
  'Pregnancy',
  'Lactation',
  'Hepatic Diseases',
  'Renal Diseases'
];

const Patients = () => {
  const { user, token } = useAuth();
  const navigate = useNavigate();
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadType, setUploadType] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [filePreviews, setFilePreviews] = useState([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    nationalId: '',
    fullName: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: '',
    },
  });

  useEffect(() => {
    const fetchPatients = async () => {
      if (!user || !token) {
        setError('Please log in to view patients.');
        setLoading(false);
        return;
      }
      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients`, config);
        if (Array.isArray(response.data)) {
          const validPatients = response.data.filter(patient =>
            patient &&
            typeof patient === 'object' &&
            patient.nationalId &&
            patient.fullName
          );
          setPatients(validPatients);
        } else {
          setError('Invalid patient data received from server');
          setPatients([]);
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        setError(err.response?.data?.message || 'Failed to load patients');
        setPatients([]);
      } finally {
        setLoading(false);
      }
    };
    fetchPatients();
  }, [user, token]);

  useEffect(() => {
    // Only clear selectedPatient when all modals are closed
    if (!showModal && !showDeleteModal && !showUploadModal) {
      setSelectedPatient(null);
    }
  }, [showModal, showDeleteModal, showUploadModal]);

  // Cleanup file previews when component unmounts
  useEffect(() => {
    return () => {
      filePreviews.forEach(preview => {
        URL.revokeObjectURL(preview.preview);
      });
    };
  }, [filePreviews]);

  const handleUploadClick = (e, patient, type) => {
    // Stop event propagation to prevent the row click handler from firing
    e.stopPropagation();

    setSelectedPatient(patient);
    setUploadType(type);
    setError(''); // Clear any previous errors
    setSelectedFiles([]); // Clear any previous files
    setFilePreviews([]); // Clear any previous previews
    setShowUploadModal(true);
  };

  const handleFileSelect = (e) => {
    const files = e.target.files;
    console.log('Files selected:', files);
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    setSelectedFiles(fileArray);

    // Create previews for selected files
    const previews = fileArray.map(file => ({
      file,
      preview: URL.createObjectURL(file)
    }));
    setFilePreviews(previews);

    console.log('Selected files:', fileArray);
    console.log('File previews created:', previews.length);
  };

  const handleFileUpload = async () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      setError('Please select files to upload');
      return;
    }

    setUploading(true);
    setError('');

    const formData = new FormData();
    const fieldName = uploadType === 'xray' ? 'xrays' : 'galleryImages';
    selectedFiles.forEach(file => {
      formData.append(fieldName, file);
      console.log('Added file to formData:', file.name, file.type, file.size);
    });

    console.log('Upload type:', uploadType);
    console.log('Selected patient:', selectedPatient?.nationalId);
    console.log('FormData entries:');
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    try {
      const endpoint = uploadType === 'xray'
        ? `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/xrays`
        : `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}/gallery`;

      console.log('Upload endpoint:', endpoint);

      const response = await axios.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
      });

      console.log('Upload response:', response.data);

      if (response.data) {
        // Show success notification
        setNotification({
          show: true,
          message: `${uploadType === 'xray' ? 'X-rays' : 'Gallery images'} uploaded successfully!`,
          type: 'success'
        });

        // Hide notification after 3 seconds
        setTimeout(() => {
          setNotification({ show: false, message: '', type: 'success' });
        }, 3000);

        // Clear selected files and previews
        setSelectedFiles([]);
        setFilePreviews([]);
        setShowUploadModal(false);
      }
    } catch (err) {
      console.error('Upload error:', err);
      console.error('Upload error response:', err.response?.data);
      setError(err.response?.data?.message || `Failed to upload ${uploadType === 'xray' ? 'X-rays' : 'gallery images'}`);
    } finally {
      setUploading(false);
    }
  };

  const clearSelectedFiles = () => {
    // Revoke object URLs to prevent memory leaks
    filePreviews.forEach(preview => {
      URL.revokeObjectURL(preview.preview);
    });
    setSelectedFiles([]);
    setFilePreviews([]);
  };

  const handleSearch = (e) => setSearchTerm(e.target.value);

  const filteredPatients = patients.filter(patient => {
    if (!patient || typeof patient !== 'object') return false;
    const fullName = patient.fullName || '';
    const nationalId = patient.nationalId || '';
    const phoneNumber = patient.phoneNumber || '';
    return (
      fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      nationalId.includes(searchTerm) ||
      phoneNumber.includes(searchTerm)
    );
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'chiefComplaint') {
      setFormData(prev => ({
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chiefComplaint: value },
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleMedicalInfoChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      medicalInfo: { ...prev.medicalInfo, [name]: value },
    }));
  };

  const handleChronicDiseaseChange = (disease) => {
    setFormData(prev => {
      const diseases = prev.medicalInfo.chronicDiseases.includes(disease)
        ? prev.medicalInfo.chronicDiseases.filter(d => d !== disease)
        : [...prev.medicalInfo.chronicDiseases, disease];
      return {
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chronicDiseases: diseases },
      };
    });
  };

  const handleViewPatient = (patient) => {
    // Navigate to patient medical tab
    navigate(`/patientprofile/${patient.nationalId}/medical`);
  };

  const handleAddPatient = () => {
    setFormData({
      nationalId: '',
      fullName: '',
      phoneNumber: '',
      gender: '',
      age: '',
      address: '',
      occupation: '',
      medicalInfo: {
        chronicDiseases: [],
        recentSurgicalProcedures: '',
        currentMedications: '',
        chiefComplaint: '',
      },
    });
    setSelectedPatient(null);
    setShowModal(true);
  };

  const handleEditPatient = (patient) => {
    setFormData({
      nationalId: patient.nationalId,
      fullName: patient.fullName,
      phoneNumber: patient.phoneNumber,
      gender: patient.gender,
      age: patient.age,
      address: patient.address || '',
      occupation: patient.occupation || '',
      medicalInfo: patient.medicalInfo || {
        chronicDiseases: [],
        recentSurgicalProcedures: '',
        currentMedications: '',
        chiefComplaint: '',
      },
    });
    setSelectedPatient(patient);
    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const patientData = {
        ...formData,
        drId: user.studentId, // Use studentId instead of id
      };

      console.log('Creating/updating patient with drId:', user.studentId);

      if (selectedPatient) {
        const response = await axios.put(
          `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`,
          patientData,
          config
        );
        if (response.data && response.data.patient) {
          setPatients(patients.map(p =>
            p.nationalId === selectedPatient.nationalId ? response.data.patient : p
          ));
        } else {
          throw new Error('Invalid patient data received from server');
        }
        setShowModal(false);
      } else {
        const response = await axios.post(
          `${process.env.REACT_APP_API_URL}/api/patients`,
          patientData,
          config
        );
        if (response.data && response.data.patient) {
          setPatients(prevPatients => [...prevPatients, response.data.patient]);
          setShowModal(false);
          setTimeout(() => {
            navigate(`/patientprofile/${response.data.patient.nationalId}`);
          }, 0);
        } else {
          throw new Error('Invalid patient data received from server');
        }
      }
    } catch (err) {
      console.error('Submit error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to save patient');
    }
  };

  const handleDeletePatient = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      await axios.delete(
        `${process.env.REACT_APP_API_URL}/api/patients/${selectedPatient.nationalId}`,
        config
      );
      setPatients(patients.filter(p => p.nationalId !== selectedPatient.nationalId));
      setShowDeleteModal(false);
      setSelectedPatient(null);
    } catch (err) {
      console.error('Delete error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to delete patient');
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-blue-50 to-white flex items-center justify-center">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-gray-100"
            >
              <div className="text-red-500 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">Error</h3>
              <p className="text-gray-600 mb-6">{error}</p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-full hover:from-blue-600 hover:to-blue-800 font-medium shadow-md"
              >
                Try Again
              </motion.button>
            </motion.div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Patient Management
                  </h1>
                  <p className="text-[#333333]">Manage your patient records</p>
                </div>
                <div className="flex flex-col md:flex-row items-center gap-4 w-full md:w-auto">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="relative w-full md:w-64"
                  >
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaSearch className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search patients..."
                      value={searchTerm}
                      onChange={handleSearch}
                      className="block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:bg-white sm:text-sm transition-all duration-200"
                    />
                  </motion.div>

                </div>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-4 md:p-6"
              >
                {filteredPatients.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-[rgba(0,119,182,0.05)]">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Patient</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">National ID</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Phone</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Gender</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#333333] uppercase tracking-wider">Age</th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-[#333333] uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredPatients.map((patient) => (
                          <motion.tr
                            key={patient.nationalId}
                            variants={item}
                            className="hover:bg-[rgba(0,119,182,0.05)] cursor-pointer"
                            onClick={() => handleViewPatient(patient)}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]">
                                  <FaUser className="h-5 w-5" />
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-[#333333]">{patient.fullName}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{patient.nationalId}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{patient.phoneNumber}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{patient.gender}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{patient.age}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditPatient(patient);
                                }}
                                className="text-[#0077B6] hover:text-[#20B2AA] mr-3"
                                title="Edit Patient"
                              >
                                <FaEdit className="inline" />
                              </button>
                              <button
                                onClick={(e) => handleUploadClick(e, patient, 'xray')}
                                className="text-green-600 hover:text-green-800 mr-3"
                                title="Upload X-rays"
                              >
                                <FaXRay className="inline" />
                              </button>
                              <button
                                onClick={(e) => handleUploadClick(e, patient, 'gallery')}
                                className="text-purple-600 hover:text-purple-800 mr-3"
                                title="Upload Gallery Images"
                              >
                                <FaImages className="inline" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedPatient(patient);
                                  setShowDeleteModal(true);
                                }}
                                className="text-red-600 hover:text-red-700"
                                title="Delete Patient"
                              >
                                <FaTrash className="inline" />
                              </button>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                  >
                    <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="mt-2 text-lg font-bold text-gray-900">No patients found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? 'Try a different search term' : 'No patients are currently assigned to you'}
                    </p>
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {selectedPatient && !showModal && !showDeleteModal && !showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-[#0077B6]">{selectedPatient.fullName}</h2>
              <button
                onClick={() => setSelectedPatient(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-6">
              <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                  <FaUser className="h-5 w-5 mr-2 text-[#0077B6]" />
                  Personal Information
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div><h4 className="text-sm font-medium text-gray-500">National ID</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.nationalId}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Phone</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.phoneNumber}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Gender</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.gender}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Age</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.age}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Address</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.address || 'Not provided'}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Occupation</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.occupation || 'Not provided'}</p></div>
                </div>
              </div>
              <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                  <FaFileMedical className="h-5 w-5 mr-2 text-[#0077B6]" />
                  Medical History
                </h3>
                <div className="space-y-4">
                  <div><h4 className="text-sm font-medium text-gray-500">Chronic Diseases</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.medicalInfo?.chronicDiseases?.join(', ') || 'None'}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Recent Surgical Procedures</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.medicalInfo?.recentSurgicalProcedures || 'None'}</p></div>
                  <div><h4 className="text-sm font-medium text-gray-500">Current Medications</h4><p className="text-sm text-gray-900 mt-1">{selectedPatient.medicalInfo?.currentMedications || 'None'}</p></div>
                </div>
              </div>
              <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                  <FaProcedures className="h-5 w-5 mr-2 text-[#0077B6]" />
                  Chief Complaint
                </h3>
                <p className="text-sm text-gray-900">{selectedPatient.medicalInfo?.chiefComplaint || 'None'}</p>
              </div>
              <div className="flex justify-end space-x-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setSelectedPatient(null);
                    handleEditPatient(selectedPatient);
                  }}
                  className="px-6 py-2 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full hover:bg-[rgba(0,119,182,0.2)] font-medium transition-all duration-300"
                >
                  Edit
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate(`/patientprofile/${selectedPatient.nationalId}`)}
                  className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  Full History
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-[#0077B6]">
                {selectedPatient ? 'Edit Patient' : 'New Patient'}
              </h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-6">
              <h3 className="text-xl font-semibold text-[#0077B6] mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">Personal Information</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Full Name*</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">National ID*</label>
                  <input
                    type="text"
                    name="nationalId"
                    value={formData.nationalId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                    disabled={!!selectedPatient}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Phone Number*</label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Gender*</label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  >
                    <option value="">Select</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Age*</label>
                  <input
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Address*</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Occupation*</label>
                  <input
                    type="text"
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  />
                </div>
              </div>

              <h3 className="text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">Medical History</h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Chronic Diseases</label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 bg-[rgba(0,119,182,0.05)] p-4 rounded-lg border border-[rgba(0,119,182,0.1)]">
                  {chronicDiseases.map((disease) => (
                    <div key={disease} className="flex items-center">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          id={`disease-${disease}`}
                          checked={formData.medicalInfo.chronicDiseases.includes(disease)}
                          onChange={() => handleChronicDiseaseChange(disease)}
                          className="h-5 w-5 text-[#0077B6] focus:ring-2 focus:ring-[#20B2AA] focus:ring-offset-2 border-2 border-gray-300 rounded cursor-pointer"
                        />
                        <label htmlFor={`disease-${disease}`} className="ml-2 text-sm font-medium text-gray-700 cursor-pointer hover:text-[#0077B6] transition-all duration-300">{disease}</label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recent Surgical Procedures (Optional)</label>
                  <textarea
                    name="recentSurgicalProcedures"
                    value={formData.medicalInfo.recentSurgicalProcedures}
                    onChange={handleMedicalInfoChange}
                    rows="2"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Current Medications (Optional)</label>
                  <textarea
                    name="currentMedications"
                    value={formData.medicalInfo.currentMedications}
                    onChange={handleMedicalInfoChange}
                    rows="2"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  />
                </div>
              </div>

              <h3 className="text-xl font-semibold text-[#0077B6] mt-6 mb-4 border-b border-[rgba(0,119,182,0.2)] pb-2">Chief Complaint</h3>
              <div>
                <textarea
                  name="chiefComplaint"
                  value={formData.medicalInfo.chiefComplaint}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Describe the patient's main complaint or reason for visit"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                  required
                />
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <motion.button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  type="submit"
                  className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {selectedPatient ? 'Update Patient' : 'Add Patient'}
                </motion.button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-[#0077B6]">Confirm Deletion</h2>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete <span className="font-semibold">{selectedPatient?.fullName}</span>'s record? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowDeleteModal(false)}
                className="px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleDeletePatient}
                className="px-6 py-2 bg-gradient-to-r from-red-500 to-red-700 text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
              >
                Delete Patient
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Success notification */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-[rgba(40,167,69,0.1)] border-l-4 border-[#28A745]' :
          notification.type === 'error' ? 'bg-red-100 border-l-4 border-red-500' :
          'bg-[rgba(0,119,182,0.1)] border-l-4 border-[#0077B6]'
        }`}>
          <div className="flex items-center">
            <div className={`flex-shrink-0 ${
              notification.type === 'success' ? 'text-[#28A745]' :
              notification.type === 'error' ? 'text-red-500' :
              'text-[#0077B6]'
            }`}>
              {notification.type === 'success' ? (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : notification.type === 'error' ? (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm font-medium ${
                notification.type === 'success' ? 'text-[#28A745]' :
                notification.type === 'error' ? 'text-red-800' :
                'text-[#0077B6]'
              }`}>
                {notification.message}
              </p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setNotification({ show: false, message: '', type: 'success' })}
                  className={`inline-flex rounded-md p-1.5 ${
                    notification.type === 'success' ? 'text-[#28A745] hover:bg-[rgba(40,167,69,0.2)]' :
                    notification.type === 'error' ? 'text-red-500 hover:bg-red-200' :
                    'text-[#0077B6] hover:bg-[rgba(0,119,182,0.2)]'
                  } focus:outline-none transition-all duration-300`}
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && selectedPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl p-4 sm:p-6 w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-100"
          >
            <div className="flex justify-between items-center mb-4 sm:mb-6">
              <h2 className="text-lg sm:text-2xl font-bold text-blue-900">
                Upload {uploadType === 'xray' ? 'X-rays' : 'Gallery Images'}
              </h2>
              <button
                onClick={() => {
                  clearSelectedFiles();
                  setShowUploadModal(false);
                }}
                className="text-gray-500 hover:text-gray-700 p-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="mb-4 sm:mb-6">
              <p className="text-gray-700 mb-3 sm:mb-4 text-sm sm:text-base">
                Upload {uploadType === 'xray' ? 'X-rays' : 'gallery images'} for <span className="font-semibold">{selectedPatient.fullName}</span>.
              </p>

              <div className="flex flex-col items-center justify-center p-4 sm:p-6 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer" onClick={() => {
                console.log('File input clicked');
                fileInputRef.current.click();
              }}>
                {uploadType === 'xray' ? (
                  <FaXRay className="h-8 w-8 sm:h-12 sm:w-12 text-blue-500 mb-3 sm:mb-4" />
                ) : (
                  <FaImages className="h-8 w-8 sm:h-12 sm:w-12 text-purple-500 mb-3 sm:mb-4" />
                )}
                <p className="text-gray-700 font-medium text-sm sm:text-base">Click to select files</p>
                <p className="text-gray-500 text-xs sm:text-sm mt-1">or drag and drop files here</p>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={(e) => {
                    console.log('File input change event:', e);
                    handleFileSelect(e);
                  }}
                  className="hidden"
                  accept="image/*"
                  multiple
                />
              </div>

              <div className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-500">
                <p>Supported formats: JPG, PNG</p>
                <p>Maximum file size: 5MB per file</p>
              </div>

              {/* Error Display */}
              {error && (
                <div className="mt-3 sm:mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center">
                    <svg className="h-4 w-4 sm:h-5 sm:w-5 text-red-400 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <span className="text-xs sm:text-sm text-red-700">{error}</span>
                  </div>
                </div>
              )}
            </div>

            {/* File Previews */}
            {filePreviews.length > 0 && (
              <div className="mb-4 sm:mb-6">
                <h3 className="text-base sm:text-lg font-semibold text-gray-700 mb-3">
                  Selected Files ({filePreviews.length})
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
                  {filePreviews.map((preview, index) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border border-gray-200">
                        <img
                          src={preview.preview}
                          alt={preview.file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                        <button
                          onClick={() => {
                            const newPreviews = filePreviews.filter((_, i) => i !== index);
                            const newFiles = selectedFiles.filter((_, i) => i !== index);
                            URL.revokeObjectURL(preview.preview);
                            setFilePreviews(newPreviews);
                            setSelectedFiles(newFiles);
                          }}
                          className="opacity-0 group-hover:opacity-100 bg-red-500 text-white rounded-full p-1 transition-all duration-200"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="mt-1 text-xs text-gray-500 truncate">
                        {preview.file.name}
                      </div>
                      <div className="text-xs text-gray-400">
                        {(preview.file.size / 1024 / 1024).toFixed(2)} MB
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  clearSelectedFiles();
                  setShowUploadModal(false);
                }}
                className="px-4 sm:px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors text-sm sm:text-base"
              >
                Cancel
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => fileInputRef.current.click()}
                className="px-4 sm:px-6 py-2 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 font-medium transition-colors text-sm sm:text-base"
              >
                Select More Files
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleFileUpload}
                disabled={selectedFiles.length === 0 || uploading}
                className={`px-4 sm:px-6 py-2 bg-gradient-to-r ${
                  selectedFiles.length === 0 || uploading
                    ? 'from-gray-400 to-gray-500 cursor-not-allowed'
                    : uploadType === 'xray'
                    ? 'from-green-500 to-green-700 hover:from-green-600 hover:to-green-800'
                    : 'from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800'
                } text-white rounded-full font-medium transition-colors shadow-md flex items-center justify-center text-sm sm:text-base`}
              >
                {uploading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="hidden sm:inline">Uploading...</span>
                    <span className="sm:hidden">Uploading</span>
                  </>
                ) : (
                  `Upload ${selectedFiles.length > 0 ? `(${selectedFiles.length})` : ''}`
                )}
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Patients;