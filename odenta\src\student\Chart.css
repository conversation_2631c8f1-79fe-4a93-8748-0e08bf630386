/* Main chart container */
.dental-chart {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

/* Teeth chart container */
.teeth-chart {
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Common styles for upper and lower teeth rows */
.upper-teeth,
.lower-teeth {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  gap: 2px;
  justify-content: center;
}

/* Tooth container */
.tooth-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 5px;
}

/* For upper teeth - surfaces below */
.tooth-container.upper .tooth-surfaces {
  margin-top: 10px;
}

/* For lower teeth - surfaces above */
.tooth-container.lower .tooth-surfaces {
  margin-bottom: 10px;
  order: -1;
}

/* Individual tooth styling */
.tooth {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.tooth:hover {
  transform: scale(1.1);
  z-index: 1;
}

.tooth.selected {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
}

/* Tooth image styling */
.tooth-image {
  width: 100%;
  height: auto;
  max-width: 40px;
  object-fit: contain;
}

/* Tooth number label */
.tooth-number {
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

/* Indicator dot for conditions/procedures */
.indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* Surface diagram container */
.surface-diagram {
  width: 60px;
  height: 60px;
  position: relative;
  margin: 5px 0;
}

.surface-part {
  position: absolute;
  stroke: black;
  stroke-width: 2px;
  fill: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.surface-part:hover {
  stroke-width: 3px;
}

.surface-part.selected {
  stroke-width: 3px;
  filter: drop-shadow(0 0 2px rgba(0,0,0,0.3));
}

/* Occlusal/Incisal surface (rectangle) */
.surface-occlusal {
  width: 60px;
  height: 60px;
}

/* Buccal/Facial surface (triangle) */
.surface-buccal {
  width: 60px;
  height: 60px;
  clip-path: polygon(0 0, 100% 0, 60% 40%, 40% 40%);
}

/* Mesial surface (triangle) */
.surface-mesial {
  width: 60px;
  height: 60px;
  clip-path: polygon(100% 0, 100% 100%, 60% 60%, 60% 40%);
}

/* Palatal/Lingual surface (triangle) */
.surface-palatal {
  width: 60px;
  height: 60px;
  clip-path: polygon(0 100%, 100% 100%, 60% 60%, 40% 60%);
}

/* Distal surface (triangle) */
.surface-distal {
  width: 60px;
  height: 60px;
  clip-path: polygon(0 0, 0 100%, 40% 60%, 40% 40%);
}

/* Saved records table styles */
.saved-records {
  margin-top: 2rem;
}

.saved-records table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background: white;
}

.saved-records th {
  background-color: #f3f4f6;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
}

.saved-records td {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .upper-teeth,
  .lower-teeth {
    grid-template-columns: repeat(8, 1fr);
  }
  
  .tooth-image {
    max-width: 30px;
  }
  
  .tooth-number {
    font-size: 0.7rem;
  }
  
  .surface-diagram {
    width: 40px;
    height: 40px;
  }
}

/* Add to your existing Chart.css */

.tooth-surfaces {
  margin: 10px 0;
  display: flex;
  justify-content: center;
}

.surface-diagram {
  width: 80px;
  height: 80px;
  position: relative;
}

.surface-part {
  position: absolute;
  stroke: black;
  stroke-width: 2px;
  fill: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.surface-part:hover {
  stroke-width: 3px;
}

.surface-part.selected {
  stroke-width: 3px;
  filter: drop-shadow(0 0 2px rgba(0,0,0,0.3));
}

/* Specific surface styles matching your SVG */
.surface-occlusal {
  width: 80px;
  height: 80px;
  left: 0;
  top: 0;
}

.surface-buccal {
  clip-path: polygon(0 0, 100% 0, 75% 25%, 25% 25%);
  width: 80px;
  height: 80px;
}

.surface-mesial {
  clip-path: polygon(100% 0, 100% 100%, 75% 75%, 75% 25%);
  width: 80px;
  height: 80px;
}

.surface-palatal {
  clip-path: polygon(0 100%, 100% 100%, 75% 75%, 25% 75%);
  width: 80px;
  height: 80px;
}

.surface-distal {
  clip-path: polygon(0 0, 0 100%, 25% 75%, 25% 25%);
  width: 80px;
  height: 80px;
}

/* Positioning for upper vs lower teeth */
.tooth-container.upper .tooth-surfaces {
  order: 2; /* Below tooth */
}

.tooth-container.lower .tooth-surfaces {
  order: 0; /* Above tooth */
}