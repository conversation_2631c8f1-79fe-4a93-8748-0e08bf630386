const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const updateReviewsWithUniversity = async () => {
  try {
    console.log('🔄 Starting review university update...');
    
    // Get all reviews
    const allReviews = await FirestoreHelpers.getAll(COLLECTIONS.REVIEWS);
    console.log(`Found ${allReviews.length} reviews to process`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const review of allReviews) {
      // Skip signature storage reviews
      if (review.procedureType === 'Signature Storage' || review.patientId?.nationalId === 'signature-storage') {
        skippedCount++;
        continue;
      }
      
      // Skip if already has studentUniversity
      if (review.studentUniversity) {
        skippedCount++;
        continue;
      }
      
      try {
        // Get the student to find their university
        const student = await FirestoreHelpers.findById(COLLECTIONS.STUDENTS, review.studentId);
        
        if (student && student.university) {
          // Update the review with the student's university
          await FirestoreHelpers.update(COLLECTIONS.REVIEWS, review.id, {
            studentUniversity: student.university
          });
          
          console.log(`✅ Updated review ${review.id} with university: ${student.university}`);
          updatedCount++;
        } else {
          console.log(`⚠️  Could not find student or university for review ${review.id}`);
          skippedCount++;
        }
      } catch (error) {
        console.error(`❌ Error updating review ${review.id}:`, error.message);
        skippedCount++;
      }
    }
    
    console.log('🎉 Review university update completed!');
    console.log(`📊 Results:`);
    console.log(`   - Updated: ${updatedCount} reviews`);
    console.log(`   - Skipped: ${skippedCount} reviews`);
    console.log(`   - Total processed: ${updatedCount + skippedCount} reviews`);
    
  } catch (error) {
    console.error('❌ Error in review university update:', error);
  }
};

// Run the migration
updateReviewsWithUniversity(); 