# Firebase Deployment Guide for ODenta Backend

## Overview
This guide shows you how to deploy your ODenta backend to Firebase Functions (serverless) or Firebase Hosting with Cloud Run.

## Current Status
✅ Server running locally on port 5001  
✅ Firebase Firestore connected  
✅ All routes and controllers migrated to Firebase  

## Deployment Options

### Option 1: Firebase Functions (Recommended)
Deploy as serverless functions - automatically scales and only pay for usage.

### Option 2: Firebase Hosting + Cloud Run
Deploy as a containerized application with more control over the environment.

---

## Option 1: Firebase Functions Deployment

### Step 1: Install Firebase CLI
```bash
npm install -g firebase-tools
```

### Step 2: Login to Firebase
```bash
firebase login
```

### Step 3: Initialize Firebase Functions
```bash
firebase init functions
```
- Select your project: `odenta-82359`
- Choose JavaScript
- Install dependencies: Yes

### Step 4: Prepare Your Code for Functions

Create `functions/package.json`:
```json
{
  "name": "functions",
  "description": "ODenta Backend Functions",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^4.0.0",
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "joi": "^17.9.0",
    "multer": "^1.4.5",
    "cloudinary": "^1.37.0",
    "dotenv": "^16.0.0"
  }
}
```

Create `functions/index.js`:
```javascript
const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

// Import your existing server setup
const app = express();

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'https://your-frontend-domain.vercel.app'],
  credentials: true
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Import all your routes
const authRoutes = require('./routes/authRoutes');
const studentRoutes = require('./routes/studentRoutes');
const supervisorRoutes = require('./routes/supervisorRoutes');
const adminRoutes = require('./routes/adminRoutes');
const patientRoutes = require('./routes/patientRoutes');
const appointmentRoutes = require('./routes/appointmentRoutes');
const reviewRoutes = require('./routes/reviewRoutes');
const dentalRoutes = require('./routes/dentalRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const universityRoutes = require('./routes/universityRoutes');
const accountRoutes = require('./routes/accountRoutes');
const newsRoutes = require('./routes/newsRoutes');
const procedureRequestRoutes = require('./routes/procedureRequestRoutes');
const labRequestRoutes = require('./routes/labRequestRoutes');

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/students', studentRoutes);
app.use('/api/supervisors', supervisorRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/patients', patientRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/dental', dentalRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/universities', universityRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/procedure-requests', procedureRequestRoutes);
app.use('/api/lab-requests', labRequestRoutes);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'ODenta Backend API is running',
    timestamp: new Date().toISOString()
  });
});

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);
```

### Step 5: Copy Your Code to Functions Directory
```bash
# Copy your source code to functions directory
cp -r controllers functions/
cp -r models functions/
cp -r routes functions/
cp -r middleware functions/
cp -r config functions/
cp .env functions/
```

### Step 6: Update Environment Variables
Create `functions/.env` with your production values:
```env
NODE_ENV=production
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_refresh_secret
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

### Step 7: Deploy to Firebase
```bash
firebase deploy --only functions
```

Your API will be available at:
`https://us-central1-odenta-82359.cloudfunctions.net/api`

---

## Option 2: Firebase Hosting + Cloud Run

### Step 1: Create Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 8080

CMD ["npm", "start"]
```

### Step 2: Update package.json for Cloud Run
```json
{
  "scripts": {
    "start": "node server.js"
  }
}
```

### Step 3: Build and Deploy to Cloud Run
```bash
# Build the container
gcloud builds submit --tag gcr.io/odenta-82359/odenta-backend

# Deploy to Cloud Run
gcloud run deploy odenta-backend \
  --image gcr.io/odenta-82359/odenta-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Step 4: Configure Firebase Hosting
Create `firebase.json`:
```json
{
  "hosting": {
    "public": "public",
    "rewrites": [
      {
        "source": "/api/**",
        "run": {
          "serviceId": "odenta-backend",
          "region": "us-central1"
        }
      }
    ]
  }
}
```

---

## Environment Variables for Production

### Required Environment Variables:
```env
NODE_ENV=production
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
CLOUDINARY_CLOUD_NAME=dbacwrbhg
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=U9vMOCxe0Er02fuBv7Uh1oI6sPI
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

### Firebase Service Account (for production):
Either set `FIREBASE_SERVICE_ACCOUNT_KEY` or use the individual variables from your service account JSON.

---

## Post-Deployment Steps

1. **Update Frontend API URLs**
   - Update your frontend to use the deployed API URL
   - Add the deployed domain to Firebase authorized domains

2. **Update CORS Settings**
   - Add your frontend domain to the CORS configuration

3. **Test All Endpoints**
   - Run your test suite against the deployed API
   - Verify authentication works correctly

4. **Monitor and Scale**
   - Set up Firebase monitoring
   - Configure alerts for errors and performance

---

## Recommended: Firebase Functions (Option 1)

Firebase Functions is recommended because:
- ✅ Automatic scaling
- ✅ Pay only for usage
- ✅ Built-in monitoring
- ✅ Easy deployment
- ✅ Integrated with Firebase services

Your deployed API will be:
`https://us-central1-odenta-82359.cloudfunctions.net/api`

## Next Steps

1. Choose your deployment option
2. Follow the steps above
3. Update your frontend to use the deployed API
4. Test thoroughly
5. Set up monitoring and alerts
