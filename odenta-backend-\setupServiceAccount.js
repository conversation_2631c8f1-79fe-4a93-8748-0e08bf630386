const fs = require('fs');
const path = require('path');

console.log('🔑 Firebase Service Account Setup');
console.log('='.repeat(50));

const setupServiceAccount = () => {
  console.log('📋 Instructions:');
  console.log('');
  console.log('1. Go to: https://console.firebase.google.com/project/odenta-82359/settings/serviceaccounts/adminsdk');
  console.log('2. Click "Generate new private key"');
  console.log('3. Download the JSON file');
  console.log('4. Save it as "serviceAccountKey.json" in this directory');
  console.log('');
  
  // Check if service account file exists
  const serviceAccountPath = path.join(__dirname, 'serviceAccountKey.json');
  
  if (fs.existsSync(serviceAccountPath)) {
    console.log('✅ Service account file found!');
    
    try {
      const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
      
      // Validate the service account
      if (serviceAccount.type === 'service_account' && serviceAccount.project_id === 'odenta-82359') {
        console.log('✅ Service account is valid');
        console.log(`📧 Client email: ${serviceAccount.client_email}`);
        
        // Update .env file
        const envPath = path.join(__dirname, '.env');
        let envContent = fs.readFileSync(envPath, 'utf8');
        
        // Add or update FIREBASE_SERVICE_ACCOUNT_KEY
        const serviceAccountJson = JSON.stringify(serviceAccount);
        
        if (envContent.includes('FIREBASE_SERVICE_ACCOUNT_KEY=')) {
          envContent = envContent.replace(
            /FIREBASE_SERVICE_ACCOUNT_KEY=.*/,
            `FIREBASE_SERVICE_ACCOUNT_KEY=${serviceAccountJson}`
          );
        } else {
          envContent += `\nFIREBASE_SERVICE_ACCOUNT_KEY=${serviceAccountJson}\n`;
        }
        
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Updated .env file with service account key');
        
        console.log('');
        console.log('🎉 Setup complete! You can now:');
        console.log('1. Run: npm start (to start the server)');
        console.log('2. Run: node firebaseSeed.js (to seed the database)');
        console.log('3. Test login with the seeded accounts');
        
      } else {
        console.log('❌ Invalid service account file');
        console.log('   Make sure you downloaded the correct file for project odenta-82359');
      }
      
    } catch (error) {
      console.log('❌ Error reading service account file:', error.message);
    }
    
  } else {
    console.log('⚠️  Service account file not found');
    console.log('');
    console.log('🔧 Next steps:');
    console.log('1. Download the service account JSON file');
    console.log('2. Save it as "serviceAccountKey.json" in this directory');
    console.log('3. Run this script again: node setupServiceAccount.js');
    console.log('');
    console.log('📁 Expected file location:');
    console.log(`   ${serviceAccountPath}`);
  }
};

setupServiceAccount();
