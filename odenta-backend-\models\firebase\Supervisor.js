const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Supervisor validation schema for Firebase
const supervisorSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  email: commonSchemas.email,
  password: Joi.string().required(),
  plainPassword: Joi.string().optional(), // Store unhashed password if needed
  name: Joi.string().required(),
  role: Joi.string().default('supervisor'),
  university: Joi.string().required(),
  students: Joi.array().items(Joi.string()).default([]), // Array of student IDs
  createdAt: Joi.date().default(() => new Date()),
  updatedAt: Joi.date().default(() => new Date())
});

// Supervisor creation schema (without ID)
const createSupervisorSchema = supervisorSchema.fork(['id'], (schema) => schema.forbidden());

// Supervisor update schema (partial)
const updateSupervisorSchema = supervisorSchema.fork(
  ['email', 'name', 'university'],
  (schema) => schema.optional()
).append({
  updatedAt: Joi.date().default(() => new Date())
});

// Supervisor login schema
const loginSupervisorSchema = Joi.object({
  email: commonSchemas.email,
  password: Joi.string().required()
});

// Helper functions for Supervisor operations
const SupervisorHelpers = {
  // Validate supervisor data
  validateCreate: (data) => createSupervisorSchema.validate(data),
  validateUpdate: (data) => updateSupervisorSchema.validate(data),
  validateLogin: (data) => loginSupervisorSchema.validate(data),
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    // Convert student ObjectIds to strings
    if (transformed.students) {
      transformed.students = transformed.students.map(id => 
        typeof id === 'object' ? id.toString() : id
      );
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    if (prepared.updatedAt && typeof prepared.updatedAt === 'string') {
      prepared.updatedAt = new Date(prepared.updatedAt);
    }
    
    return prepared;
  },
  
  // Remove sensitive data for client response
  sanitizeForResponse: (supervisorData) => {
    const sanitized = { ...supervisorData };
    delete sanitized.password;
    delete sanitized.plainPassword;
    return sanitized;
  },
  
  // Add student to supervisor
  addStudent: (supervisorData, studentId) => {
    const updated = { ...supervisorData };
    if (!updated.students) {
      updated.students = [];
    }
    
    if (!updated.students.includes(studentId)) {
      updated.students.push(studentId);
    }
    
    updated.updatedAt = new Date();
    return updated;
  },
  
  // Remove student from supervisor
  removeStudent: (supervisorData, studentId) => {
    const updated = { ...supervisorData };
    if (updated.students) {
      updated.students = updated.students.filter(id => id !== studentId);
    }
    
    updated.updatedAt = new Date();
    return updated;
  }
};

module.exports = {
  supervisorSchema,
  createSupervisorSchema,
  updateSupervisorSchema,
  loginSupervisorSchema,
  SupervisorHelpers,
  COLLECTION_NAME: COLLECTIONS.SUPERVISORS
};
