from experta import *
from experta.fact import Field
import uuid

# Custom Fact Classes
class CPU(Fact):
    name = Field(str, mandatory=True)
    socket = Field(str, mandatory=True)
    performance = Field(str, mandatory=True, default="Medium")
    cores = Field(int, mandatory=True)

class Motherboard(Fact):
    name = Field(str, mandatory=True)
    socket = Field(str, mandatory=True)
    ram_slots = Field(int, mandatory=True)
    pcie_slots = Field(int, mandatory=True)

class GPU(Fact):
    name = Field(str, mandatory=True)
    performance = Field(str, mandatory=True, default="Medium")
    vram = Field(int, mandatory=True)

class UserInput(Fact):
    use_case = Field(str, mandatory=True)

class Recommendation(Fact):
    cpu = Field(str)
    motherboard = Field(str)
    gpu = Field(str)

# Knowledge Engine
class HardwareExpert(KnowledgeEngine):
    def __init__(self):
        super().__init__()
        self.recommendation = None

    # Initialize component pool
    @DefFacts()
    def _initial_facts(self):
        # CPUs
        yield CPU(name="Intel i9-10900K", socket="LGA1200", performance="High", cores=10)
        yield CPU(name="AMD Ryzen 5 5600X", socket="AM4", performance="Medium", cores=6)
        yield CPU(name="Intel i5-10400", socket="LGA1200", performance="Medium", cores=6)
        
        # Motherboards
        yield Motherboard(name="ASUS ROG Strix", socket="LGA1200", ram_slots=4, pcie_slots=2)
        yield Motherboard(name="MSI B450 Tomahawk", socket="AM4", ram_slots=4, pcie_slots=1)
        yield Motherboard(name="Gigabyte H410", socket="LGA1200", ram_slots=2, pcie_slots=1)
        
        # GPUs
        yield GPU(name="NVIDIA RTX 3080", performance="High", vram=10)
        yield GPU(name="AMD RX 6600 XT", performance="Medium", vram=8)
        yield GPU(name="NVIDIA GTX 1650", performance="Low", vram=4)

    # Recommendation Rules
    @Rule(UserInput(use_case="gaming"))
    def recommend_gaming(self):
        self.declare(Recommendation(
            cpu=ANY("Intel i9-10900K", "AMD Ryzen 5 5600X"),
            motherboard=ANY("ASUS ROG Strix", "MSI B450 Tomahawk"),
            gpu=ANY("NVIDIA RTX 3080", "AMD RX 6600 XT")
        ))

    @Rule(UserInput(use_case="office"))
    def recommend_office(self):
        self.declare(Recommendation(
            cpu=ANY("Intel i5-10400", "AMD Ryzen 5 5600X"),
            motherboard=ANY("Gigabyte H410", "MSI B450 Tomahawk"),
            gpu="NVIDIA GTX 1650"
        ))

    @Rule(UserInput(use_case="video_editing"))
    def recommend_video_editing(self):
        self.declare(Recommendation(
            cpu=ANY("Intel i9-10900K", "AMD Ryzen 5 5600X"),
            motherboard=ANY("ASUS ROG Strix", "MSI B450 Tomahawk"),
            gpu=ANY("NVIDIA RTX 3080", "AMD RX 6600 XT")
        ))

    # Compatibility Rules
    @Rule(AS.recommendation << Recommendation(cpu=MATCH.cpu, motherboard=MATCH.motherboard, gpu=MATCH.gpu),
          CPU(name=MATCH.cpu, socket=MATCH.cpu_socket),
          Motherboard(name=MATCH.motherboard, socket=MATCH.mb_socket, pcie_slots=MATCH.pcie_slots),
          TEST(lambda cpu_socket, mb_socket: cpu_socket == mb_socket),
          TEST(lambda pcie_slots: pcie_slots >= 1))
    def check_compatibility(self, recommendation, cpu, motherboard, gpu, cpu_socket, mb_socket, pcie_slots):
        self.recommendation = {
            "CPU": cpu["name"],
            "Motherboard": motherboard["name"],
            "GPU": gpu["name"],
            "Socket": cpu_socket,
            "RAM Slots": motherboard["ram_slots"],
            "VRAM": gpu["vram"]
        }
        print("\nRecommendation:")
        print(f"CPU: {cpu['name']} ({cpu_socket}, {cpu['cores']} cores)")
        print(f"Motherboard: {motherboard['name']} ({mb_socket}, {motherboard['ram_slots']} RAM slots)")
        print(f"GPU: {gpu['name']} ({gpu['vram']}GB VRAM)")
        print(f"Compatibility confirmed: CPU and motherboard use {cpu_socket} socket, GPU supported by motherboard")

    @Rule(AS.recommendation << Recommendation(cpu=MATCH.cpu, motherboard=MATCH.motherboard),
          CPU(name=MATCH.cpu, socket=MATCH.cpu_socket),
          Motherboard(name=MATCH.motherboard, socket=MATCH.mb_socket),
          TEST(lambda cpu_socket, mb_socket: cpu_socket != mb_socket))
    def incompatible_components(self, recommendation, cpu_socket, mb_socket):
        print(f"\nError: Incompatible configuration. CPU socket ({cpu_socket}) does not match motherboard socket ({mb_socket}).")
        self.recommendation = None

def run_expert_system():
    while True:
        use_case = input("\nEnter your use case (gaming, office, video_editing) or 'quit' to exit: ").lower()
        
        if use_case == 'quit':
            break
            
        if use_case not in ["gaming", "office", "video_editing"]:
            print("Use case not recognized; defaulting to office")
            use_case = "office"

        engine = HardwareExpert()
        engine.reset()
        engine.declare(UserInput(use_case=use_case))
        engine.run()

if __name__ == "__main__":
    print("Hardware Configuration Expert System")
    run_expert_system()