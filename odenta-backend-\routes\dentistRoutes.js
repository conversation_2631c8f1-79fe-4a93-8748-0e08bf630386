const express = require('express');
const router = express.Router();
const dentistController = require('../controllers/dentistController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

router.get('/', dentistController.getAllDentists);
router.get('/:id', dentistController.getDentistById);
router.post('/', auth, role('superadmin'), dentistController.createDentist);
router.put('/:id', auth, role('superadmin'), dentistController.updateDentist);
router.delete('/:id', auth, role('superadmin'), dentistController.deleteDentist);

module.exports = router;