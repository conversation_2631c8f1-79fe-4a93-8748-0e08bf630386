// Frontend Firebase Configuration for ODenta
// Copy this to your frontend project

import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getAnalytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAaJKFcxipd7SBS-GQK97ZIFr0oBBEKQOU",
  authDomain: "odenta-82359.firebaseapp.com",
  projectId: "odenta-82359",
  storageBucket: "odenta-82359.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:12ccba8515c9648b1d8941",
  measurementId: "G-HFXCKFE42Y"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Google Client ID for OAuth
export const GOOGLE_CLIENT_ID = "************-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com";

// Initialize Analytics (optional)
export const analytics = getAnalytics(app);

export default app;

// Usage examples:
/*
// For regular email/password login:
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "./firebase-config";

const loginUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    return user;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

// For Google login:
import { signInWithPopup } from "firebase/auth";
import { auth, googleProvider } from "./firebase-config";

const loginWithGoogle = async () => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;
    return user;
  } catch (error) {
    console.error("Google login error:", error);
    throw error;
  }
};
*/
