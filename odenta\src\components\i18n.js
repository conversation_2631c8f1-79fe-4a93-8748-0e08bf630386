import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

const resources = {
  en: {
    translation: {
      "navbar": {
        "Home": "Home",
        "Universities": "Universities",
        "About": "About",
        "Contact": "Contact",
        "Services": "Services",
        "UniversityServices": "University Services",
        "Login": "Login",
        "ToggleMenu": "Toggle menu",
        "TryOurAI": "Try Our AI"
      },
      "footer": {
        "CompanyDescription": "AI-powered dental management system for universities, students, and supervisors.",
        "QuickLinks": "Quick Links",
        "ContactUs": "Contact Us",
        "Newsletter": "Newsletter",
        "SubscribeDescription": "Subscribe to our newsletter for the latest updates and dental insights.",
        "YourEmail": "Your Email",
        "Subscribe": "Subscribe",
        "Address": "Alexandria, Egypt",
        "Phone": "Phone",
        "Email": "Email",
        "WorkingHours": "Sun-Thu: 9 AM - 5 PM",
        "AllRightsReserved": "All Rights Reserved",
        "SocialMedia": {
          "Facebook": "Facebook",
          "Instagram": "Instagram",
          "LinkedIn": "LinkedIn",
          "YouTube": "YouTube"
        },
        "Links": {
          "Home": "Home",
          "Services": "Services",
          "UniversityServices": "University Services",
          "AboutUs": "About Us",
          "Contact": "Contact Us"
        },
        "Policies": {
          "PrivacyPolicy": "Privacy Policy",
          "TermsOfService": "Terms of Service",
          "CookiePolicy": "Cookie Policy"
        }
      },
      "hero": {
        "revolutionizing": "Revolutionizing",
        "dentalCareWithAI": "Dental Care with AI",
        "description": "A comprehensive dental management system integrating AI-driven X-ray diagnostics with workflows for university students and supervisors.",
        "forUniversities": "For Universities"
      },
      "features": {
        "comprehensiveDental": "Comprehensive Dental",
        "management": "Management",
        "description": "All the tools you need for modern dental education and practice management in one platform.",
        "aiXray": "AI X-ray Analysis",
        "aiXrayDesc": "Automated detection of dental conditions in panoramic and periapical X-rays with 95% accuracy.",
        "studentTools": "Student Workflow",
        "studentToolsDesc": "Complete case management with supervisor review system for dental students.",
        "exploreUniversity": "Explore University Tools",
        "clinicTools": "Clinic Management",
        "clinicToolsDesc": "Patient management, appointments, and billing for dental practices.",
        "exploreClinic": "Explore Clinic Tools",
        "digitalCharting": "Digital Charting",
        "digitalChartingDesc": "Interactive teeth chart with surface-level treatment tracking.",
        "supervisorReview": "Supervisor Review",
        "supervisorReviewDesc": "Faculty can review and annotate student cases.",
        "analytics": "Practice Analytics",
        "analyticsDesc": "Track clinical performance and educational progress with detailed reports.",
        "appointments": "Appointments",
        "appointmentsDesc": "Efficient scheduling system for managing student and patient appointments."
      },
      "howItWorks": {
        "simple": "Simple",
        "workflow": "Workflow",
        "description": "Discover how Odenta transforms dental education and university management.",
        "forStudents": "For Dental Students",
        "studentStep1": "Upload patient cases and X-rays",
        "studentStep2": "Receive instant AI analysis",
        "studentStep3": "Get supervisor feedback",
        "aiAnalysis": "AI-Powered Analysis",
        "aiStep1": "Automatic detection of dental conditions",
        "aiStep2": "Detailed diagnostic reports with bounding boxes",
        "aiStep3": "Support for clinical decision-making",
        "forAdministrators": "For University Administrators",
        "adminStep1": "Monitor student progress and performance",
        "adminStep2": "Track educational outcomes and metrics",
        "adminStep3": "Manage university dental services efficiently",
        "studentCTA": "Explore Student Tools",
        "adminCTA": "Explore Administrator Tools",
        "clinicCTA": "Explore Clinic Tools"
      },
      "ai": {
        "poweredBy": "Powered by",
        "advancedAI": "Advanced AI",
        "description": "Our AI analyzes panoramic and periapical X-rays, providing detailed diagnostic reports with bounding box annotations for dental education and practice.",
        "realTimeAnalysis": "Real-Time Analysis",
        "realTimeAnalysisDesc": "Instant detection of dental conditions in panoramic and periapical X-rays with annotated bounding boxes.",
        "clinicalAccuracy": "Clinical Accuracy",
        "clinicalAccuracyDesc": "Validated by dental professionals with 95% accuracy in detecting common dental conditions.",
        "learningAssistant": "Learning Assistant",
        "learningAssistantDesc": "AI-generated insights help students improve diagnostic skills.",
        "sampleDiagnosis": "Sample AI Diagnosis",
        "cariesDetection": "Caries detected on tooth #36 (Distal surface)",
        "periodontalAssessment": "Moderate bone loss in lower anterior region",
        "impactedTeeth": "Impacted wisdom teeth (#18, #28)"
      },
      "cta": {
        "readyToTransform": "Ready to Transform",
        "yourPractice": "Your University?",
        "description": "Join the future of dental education and university management today.",
        "exploreUniversity": "Explore University",
        "tryOurAI": "Try Our AI Demo"
      },
      "about": {
        "title": "Meet the",
        "innovators": "Odenta Team",
        "subtitle": "Combining dental expertise with cutting-edge AI to revolutionize oral healthcare",
        "ourMission": "Our Mission",
        "missionDescription": "We're transforming dental education and practice through intelligent technology that enhances diagnostic accuracy and improves patient outcomes.",
        "ourVision": "Our Vision",
        "visionDescription": "To become the global standard in AI-assisted dental education and practice, empowering clinicians with tools that enhance precision, efficiency, and patient care.",
        "meetOurTeam": "Meet Our Team",
        "teamDescription": "A unique combination of AI expertise and dental professionals working together",
        "aiEngineer": "AI Engineer",
        "dentalExpert": "Dentist",
        "ourValues": "Our Values",
        "valuesDescription": "The principles that Pig everything we do at Odenta",
        "innovation": "Innovation",
        "innovationText": "We constantly push boundaries to develop novel solutions for dental challenges through cutting-edge AI research.",
        "clinicalExcellence": "Clinical Excellence",
        "clinicalExcellenceText": "Every feature is validated by dental professionals to ensure real-world accuracy and clinical relevance.",
        "education": "Education",
        "educationText": "We're committed to enhancing dental training through technology that bridges theory and practice.",
        "founded": "Founded",
        "joinUs": "Join the Dental Revolution",
        "ctaText": "Whether you're a dental student, educator, or practitioner, Odenta has solutions for you",
        "contactUs": "Contact Our Team",
        "getInTouch": "Get in Touch"
      },
      "contact": {
        "title": "Get in",
        "withUs": "Touch",
        "subtitle": "We're here to answer your questions and help you explore how Odenta can transform your dental practice or education.",
        "getInTouch": "Get in Touch",
        "description": "Reach out to us via email, phone, or follow us on social media. We're excited to connect and discuss your needs!",
        "email": "Email",
        "phone": "Phone",
        "address": "Address",
        "sendMessage": "Send Us a Message",
        "name": "Name",
        "namePlaceholder": "Enter your name",
        "emailPlaceholder": "Enter your email",
        "message": "Message",
        "messagePlaceholder": "Tell us how we can help you",
        "submit": "Send Message",
        "formSuccess": "Thank you! Your message has been sent successfully.",
        "formError": "Oops! Something went wrong. Please try again.",
        "exploreMore": "Explore More",
        "withDentlyzer": "with Odenta",
        "ctaDescription": "Discover how our AI-powered solutions can enhance dental education and university practice.",
        "exploreUniversity": "For Universities",
        "tryOurAI": "Try Our AI"
      },
      "universityServices": {
        "title": "Empowering Dental",
        "withAI": "Education with AI",
        "subtitle": "Transform dental education with Odenta's AI-powered tools designed for students, educators, and universities.",
        "tryAI": "Try Our AI",
        "contactUs": "Contact Us",
        "featuresTitle": "Tools for Dental Education",
        "featuresDescription": "Discover how Odenta enhances learning and training for dental students and educators.",
        "aiXray": "AI X-ray Analysis",
        "aiXrayDesc": "Automated detection of dental conditions like caries and bone loss with 95% accuracy, aiding student diagnostics.",
        "studentWorkflow": "Student Workflow",
        "studentWorkflowDesc": "Streamlined case management for students, enabling efficient submission and tracking of patient cases.",
        "supervisorReview": "Supervisor Review",
        "supervisorReviewDesc": "Faculty can review, annotate, and approve student cases, fostering effective mentorship.",
        "digitalCharting": "Digital Charting",
        "digitalChartingDesc": "Interactive teeth chart for precise treatment planning and educational exercises.",
        "analytics": "Learning Analytics",
        "analyticsDesc": "Track student progress and performance with detailed reports to support educational goals.",
        "learningAssistant": "AI Learning Assistant",
        "learningAssistantDesc": "AI-generated feedback helps students identify and improve diagnostic and treatment skills.",
        "benefitsTitle": "Benefits for Universities",
        "benefitsDescription": "Why dental schools choose Odenta to enhance their curriculum and training programs.",
        "improvedLearning": "Enhanced Learning Outcomes",
        "improvedLearningDesc": "AI-driven diagnostics and interactive tools improve students' diagnostic accuracy and confidence.",
        "realTimeFeedback": "Real-Time Feedback",
        "realTimeFeedbackDesc": "Instant AI analysis and supervisor annotations accelerate student learning and skill development.",
        "efficientSupervision": "Efficient Faculty Supervision",
        "efficientSupervisionDesc": "Streamlined review processes save time for educators while maintaining high-quality feedback.",
        "ctaTitle": "Elevate Your Dental Program",
        "ctaDescription": "Join leading dental schools using Odenta to prepare the next generation of dentists."
      },
      "clinicsServices": {
        "title": "Optimizing Dental",
        "withAI": "Clinics with AI",
        "subtitle": "Streamline your dental practice with Odenta's AI-powered tools for diagnostics, management, and patient care.",
        "tryAI": "Try Our AI",
        "contactUs": "Contact Us",
        "featuresTitle": "Tools for Dental Clinics",
        "featuresDescription": "Explore how Odenta enhances efficiency and patient outcomes in dental practices.",
        "aiXray": "AI X-ray Analysis",
        "aiXrayDesc": "Instant detection of dental conditions with 95% accuracy, supporting precise clinical decisions.",
        "patientManagement": "Patient Management",
        "patientManagementDesc": "Centralized system for patient records, treatment history, and communication.",
        "appointments": "Appointment Scheduling",
        "appointmentsDesc": "Automated scheduling and reminders to optimize clinic operations and patient flow.",
        "digitalCharting": "Digital Charting",
        "digitalChartingDesc": "Interactive teeth chart for accurate treatment documentation and planning.",
        "analytics": "Practice Analytics",
        "analyticsDesc": "Monitor clinic performance, patient trends, and financial metrics with detailed reports.",
        "treatmentPlanning": "Treatment Planning",
        "treatmentPlanningDesc": "Easier way to do treatment planning to enhance clinical decision-making.",
        "benefitsTitle": "Benefits for Clinics",
        "benefitsDescription": "Why dental clinics rely on Odenta to improve operations and patient care.",
        "enhancedDiagnostics": "Enhanced Diagnostics",
        "enhancedDiagnosticsDesc": "AI-powered analysis improves diagnostic accuracy, reducing errors and improving outcomes.",
        "streamlinedOperations": "Streamlined Operations",
        "streamlinedOperationsDesc": "Integrated tools for scheduling, billing, and patient management save time and resources.",
        "improvedPatientCare": "Improved Patient Care",
        "improvedPatientCareDesc": "Personalized treatment plans and efficient workflows enhance patient satisfaction.",
        "ctaTitle": "Transform Your Dental Practice",
        "ctaDescription": "Join clinics worldwide using Odenta to deliver exceptional care with cutting-edge technology."
      },
      "universities": {
        "title": "University Clinics",
        "subtitle": "Explore our network of university clinics offering free dental care by supervised students.",
        "search": "Search universities or locations",
        "allLocations": "All Locations",
        "noResults": "No Universities Found",
        "adjustSearch": "Try adjusting your search or filter to find what you're looking for.",
        "viewDentists": "View Dentists"
      },
      "universityInfo": {
        "subtitle": "Excellence in Dental Care Education",
        "about": "About",
        "services": "Our Services",
        "facilities": "Our Facilities",
        "facilitiesDesc": "World-class infrastructure for dental education and patient care.",
        "modernClinics": "Modern Clinics",
        "modernClinicsDesc": "State-of-the-art dental clinics with advanced equipment.",
        "researchLabs": "Research Labs",
        "researchLabsDesc": "Cutting-edge facilities for dental research and innovation.",
        "simulationCenter": "Simulation Center",
        "simulationCenterDesc": "Training facilities for hands-on dental practice.",
        "visitClinic": "Visit Our Clinic",
        "address": "Address",
        "phone": "Phone",
        "email": "Email",
        "getDirections": "Get Directions",
        "locationMap": "Location Map",
        "bookAppointment": "Book Appointment",
        "contactUs": "Contact Us"
      },
      "clinics": {
        "title": "Find a Dentist",
        "subtitle": "Discover professional dentists offering specialized dental care across various clinics.",
        "search": "Search dentists",
        "allLocations": "All Locations",
        "noResults": "No Dentists Found",
        "adjustSearch": "Try adjusting your search or filter to find what you're looking for.",
        "experience": "Experience",
        "clinic": "Clinic",
        "viewDetails": "View Details"
      },
      "dentistInfo": {
        "about": "About",
        "specialties": "Specialties",
        "experience": "Experience",
        "clinic": "Clinic",
        "workingHours": "Working Hours",
        "workingHoursDesc": "Check the availability of your dentist for scheduling appointments.",
        "monday": "Monday",
        "tuesday": "Tuesday",
        "wednesday": "Wednesday",
        "thursday": "Thursday",
        "friday": "Friday",
        "saturday": "Saturday",
        "sunday": "Sunday",
        "visitClinic": "Visit Our Clinic",
        "address": "Address",
        "phone": "Phone",
        "email": "Email",
        "getDirections": "Get Directions",
        "locationMap": "Location Map",
        "bookAppointment": "Book Appointment",
        "contactUs": "Contact Us"
      },
        "appointment": {
          "BookYourAppointment": "Book Your Appointment",
          "SelectUniversityDateTime": "Select a university, date, and time to book your dental appointment.",
          "FailedLoadUniversities": "Failed to load universities. Please try again later.",
          "FailedLoadSlots": "Failed to load available slots. Please try again.",
          "FailedBookAppointment": "Failed to book appointment. Please try again.",
          "SelectUniversity": "Select University",
          "ChooseUniversity": "Choose a university",
          "SelectDate": "Select Date",
          "AvailableTimeSlots": "Available Time Slots",
          "PleaseSelectUniversity": "Please select a university first",
          "PleaseSelectDate": "Please select a date",
          "NoAvailableSlots": "No available slots for the selected date",
          "PatientInformation": "Patient Information",
          "SelectedSlot": "Selected Slot",
          "FullName": "Full Name",
          "PhoneNumber": "Phone Number",
          "NationalID": "National ID",
          "Age": "Age",
          "Gender": "Gender",
          "ChooseGender": "Choose gender",
          "Male": "Male",
          "Female": "Female",
          "Other": "Other",
          "Occupation": "Occupation",
          "Address": "Address",
          "ChiefComplaint": "Chief Complaint",
          "Submitting": "Submitting...",
          "BookAppointment": "Book Appointment",
          "AppointmentConfirmed": "Appointment Confirmed",
          "SuccessfullyBookedUniversity": "Your appointment has been successfully booked with the university clinic.",
          "FailedLoadUniversityInfo": "Failed to load university information.",
          "FailedGeneratePDF": "Failed to generate PDF. Please try again.",
          "AppointmentConfirmation": "Appointment Confirmation",
          "AppointmentID": "Appointment ID",
          "UniversityClinic": "University Clinic",
          "Date": "Date",
          "Time": "Time",
          "PatientName": "Patient Name",
          "NationalID": "National ID",
          "Age": "Age",
          "Gender": "Gender",
          "ChiefComplaint": "Chief Complaint",
          "Occupation": "Occupation",
          "Address": "Address",
          "ScanQRCode": "Scan this QR code to view appointment details.",
          "UniversityClinicContact": "University Clinic Contact",
          "DentlyzerContact": "Odenta Contact",
          "Email": "Email",
          "Phone": "Phone",
          "NoContactInfoAvailable": "No contact information available.",
          "DownloadConfirmation": "Download Confirmation",
          "BackToUniversities": "Back to Universities",
          "UniversityDentalCare": "University Dental Care",
          "DentalBookingPlatform": "Dental Booking Platform",
          "UnknownUniversity": "Unknown University"
        },
      "select": {
        "chooseYourDentist": "Choose Your Dentist",
        "description": "Select from university clinics or general clinics to find the best dental care for your needs.",
        "free": "Free",
        "universityClinics": "University Clinics",
        "universityClinicsDesc": "Access dental care at university clinics, supervised by experienced professionals and students.",
        "exploreUniversityClinics": "Explore University Clinics",
        "generalClinics": "Private Clinics",
        "generalClinicsDesc": "Find expert dental care at private clinics with advanced facilities and personalized services.",
        "exploreGeneralClinics": "Explore General Clinics",
        "comingSoonMessage": "Coming Soon",
        "comingSoonDescription": "General Clinics feature is under development and will be available soon. Stay tuned!",
        "closeButton": "Close"
      },
      "universityInfo": {
        "noServices": "No services available",
        "dentistryServices": "Dentistry Services",
        "slotPeriod": "Appointment Period",
        "about": "About",
        "subtitle": "Explore our dental programs and services",
        "error": "Error",
        "backToUniversities": "Back to Universities",
        "bookAppointment": "Book Appointment",
        "contactUs": "Contact Us",
        "facilities": "Facilities",
        "visitClinic": "Visit Our Clinic",
        "address": "Address",
        "phone": "Phone",
        "email": "Email",
        "getDirections": "Get Directions",
        "locationMap": "Location Map"
      },
      "universities": {
        "title": "Our Universities",
        "subtitle": "Discover top dental universities",
        "search": "Search universities...",
        "allLocations": "All Locations",
        "viewDentists": "View Dentists",
        "noResults": "No Results Found",
        "adjustSearch": "Try adjusting your search or filter"
      },
      "clinics": {
      "title": "Find a Dentist",
      "subtitle": "Discover top-rated dentists and book your appointment today.",
      "search": "Search by dentist or clinic name",
      "allLocations": "All Locations",
      "error": "Failed to load dentists",
      "noResults": "No dentists found",
      "adjustSearch": "Try adjusting your search or location filter",
      "noServices": "No services listed",
      "clinic": "Clinic",
      "viewDetails": "View Details"
    },
    "dentistInfo": {
      "error": "Error",
      "dentistNotFound": "The requested dentist could not be found.",
      "backToClinics": "Back to Clinics",
      "about": "About",
      "services": "Services",
      "noServices": "No services listed",
      "clinic": "Clinic",
      "slotPeriod": "Appointment Period",
      "workingHours": "Working Hours",
      "workingHoursDesc": "Check the availability of the dentist.",
      "visitClinic": "Visit Our Clinic",
      "address": "Address",
      "phone": "Phone",
      "email": "Email",
      "noPhone": "No phone number provided",
      "noEmail": "No email provided",
      "getDirections": "Get Directions",
      "locationMap": "Location Map",
      "bookAppointment": "Book Appointment",
      "contactUs": "Contact Us",
      "monday": "Monday",
      "tuesday": "Tuesday",
      "wednesday": "Wednesday",
      "thursday": "Thursday",
      "friday": "Friday",
      "saturday": "Saturday",
      "sunday": "Sunday"
    }
  }
},
  ar: {
    translation: {
          "navbar": {
            "Home": "الرئيسية",
            "Universities": "الجامعات",
            "About": "عننا",
            "Contact": "كلمنا", // "تواصل معانا" changed to "كلمنا" for a more direct, conversational tone.
            "Services": "خدماتنا",
            "UniversityServices": "خدمات الجامعات",
            "Login": "ادخل حسابك", // More natural than "تسجيل دخول".
            "ToggleMenu": "افتح/قفل القايمة",
            "TryOurAI": "جرب ذكائنا الاصطناعي" // Slightly rephrased for a catchier, modern feel.
          },
          "footer": {
            "CompanyDescription": "نظام ذكي لإدارة الأسنان بيستخدم الذكاء الاصطناعي، مثالي للجامعات، الطلاب، والمشرفين.", // Updated for university focus
            "QuickLinks": "روابط سريعة",
            "ContactUs": "كلمنا", // Consistent with navbar.
            "Newsletter": "النشرة بتاعتنا",
            "SubscribeDescription": "اشترك في نشرتنا عشان يوصلك كل جديد عن الأسنان ونصايح مهمة.", // More conversational and inviting.
            "YourEmail": "إيميلك",
            "Subscribe": "اشترك",
            "Address": "الإسكندرية، مصر",
            "Phone": "تليفون",
            "Email": "إيميل",
            "WorkingHours": "من الأحد للخميس: 9 الصبح - 5 العشا", // "العشا" is more colloquial than "المغرب".
            "AllRightsReserved": "كل الحقوق محفوظة",
            "SocialMedia": {
              "Facebook": "فيسبوك",
              "Instagram": "إنستجرام",
              "LinkedIn": "لينكدإن",
              "YouTube": "يوتيوب"
            },
            "Links": {
              "Home": "الرئيسية",
              "Services": "خدماتنا",
              "UniversityServices": "خدمات الجامعات",
              "AboutUs": "عننا",
              "Contact": "كلمنا"
            },
            "Policies": {
              "PrivacyPolicy": "سياسة الخصوصية",
              "TermsOfService": "شروط الاستخدام", // "الخدمة" changed to "الاستخدام" for clarity.
              "CookiePolicy": "سياسة الكوكيز"
            }
          },
          "hero": {
            "revolutionizing": "بيغيّر",
            "dentalCareWithAI": "رعاية الأسنان بالذكاء الاصطناعي", // Kept intact as it’s clear and effective.
            "description": "نظام متكامل لإدارة الأسنان بيدمج تشخيص الأشعة بالذكاء الاصطناعي مع أدوات لطلاب الجامعات والمشرفين.", // Updated for university focus
            "forUniversities": "للجامعات"
          },
          "features": {
            "comprehensiveDental": "حلول شاملة",
            "management": "لإدارة الأسنان",
            "description": "كل الأدوات اللي هتحتاجها لتعليم وإدارة عيادات الأسنان في مكان واحد.", // More concise and engaging.
            "aiXray": "تحليل الأشعة الذكي",
            "aiXrayDesc": "كشف فوري عن مشاكل الأسنان في الأشعة البانورامية والبيريابيكال بدقة 95%.", // "أوتوماتيكي" changed to "فوري" for simplicity.
            "studentTools": "أدوات الطلاب",
            "studentToolsDesc": "نظام كامل لإدارة الحالات مع مراجعة من المشرفين لطلاب الأسنان.", // Streamlined phrasing.
            "exploreUniversity": "شوف أدوات الجامعات",
            "clinicTools": "إدارة العيادات",
            "clinicToolsDesc": "إدارة المرضى، المواعيد، والفواتير لعيادات الأسنان بسهولة.", // Added "بسهولة" for emphasis.
            "exploreClinic": "شوف أدوات العيادات",
            "digitalCharting": "رسم الأسنان الرقمي",
            "digitalChartingDesc": "مخطط تفاعلي للأسنان بيتابع العلاج على مستوى السطح.", // Clarified purpose.
            "supervisorReview": "مراجعة المشرفين",
            "supervisorReviewDesc": "الدكاترة يقدروا يراجعوا حالات الطلاب ويضيفوا ملاحظات بسهولة.", // Added "بسهولة" for flow.
            "analytics": "تحليلات العيادة",
            "analyticsDesc": "تابع الأداء والتقدم التعليمي بتقارير مفصلة.",
            "appointments": "المواعيد",
            "appointmentsDesc": "نظام جدولة فعال لإدارة مواعيد الطلاب والمرضى."
          },
          "howItWorks": {
            "simple": "بسيط",
            "workflow": "وسهل",
            "description": "اعرف إزاي أودينتا بيسهّل تعليم وإدارة الأسنان في الجامعات.", // Updated for university focus
            "forStudents": "لطلاب الأسنان",
            "studentStep1": "ارفع حالات المرضى والأشعة",
            "studentStep2": "خد تحليل فوري بالذكاء الاصطناعي",
            "studentStep3": "استلم ملاحظات المشرفين",
            "aiAnalysis": "تحليل ذكي",
            "aiStep1": "كشف سريع عن مشاكل الأسنان",
            "aiStep2": "تقارير تشخيصية واضحة مع مربعات تحديد",
            "aiStep3": "دعم قراراتك الإكلينيكية",
            "forAdministrators": "لإداريي الجامعات",
            "adminStep1": "متابعة تقدم وأداء الطلاب",
            "adminStep2": "تتبع نتائج ومقاييس التعليم",
            "adminStep3": "إدارة خدمات طب الأسنان الجامعية بكفاءة",
            "studentCTA": "شوف أدوات الطلاب",
            "adminCTA": "شوف أدوات الإداريين"
          },
          "ai": {
            "poweredBy": "مدعوم بـ",
            "advancedAI": "ذكاء اصطناعي متطور",
            "description": "ذكائنا الاصطناعي بيحلل الأشعة البانورامية والبيريابيكال، وبيديك تقارير تشخيصية دقيقة مع مربعات تحديد، مثالي للتعليم والعيادات.", // More engaging and clear.
            "realTimeAnalysis": "تحليل على طول",
            "realTimeAnalysisDesc": "كشف فوري عن مشاكل الأسنان في الأشعة مع مربعات توضيح.", // "على طول" is very Egyptian and conversational.
            "clinicalAccuracy": "دقة طبية",
            "clinicalAccuracyDesc": "دقة 95% في كشف مشاكل الأسنان، موثّقة من دكاترة أسنان.", // Simplified and professional.
            "learningAssistant": "مساعد تعليمي ذكي",
            "learningAssistantDesc": "نصايح ذكية بتساعد الطلاب يحسّنوا مهارات التشخيص.", // "نصايح" is more relatable than "رؤى".
            "sampleDiagnosis": "نموذج تشخيص ذكي",
            "cariesDetection": "تسوس في الضرس #36 (السطح البعيد)",
            "periodontalAssessment": "فقدان عظم متوسط في الفك الأمامي السفلي",
            "impactedTeeth": "أضراس عقل مطمورة (#18، #28)"
          },
          "cta": {
            "readyToTransform": "جاهز تغيّر",
            "yourPractice": "جامعتك؟",
            "description": "انضم لمستقبل تعليم وإدارة الأسنان في الجامعات دلوقتي.", // Updated for university focus
            "exploreUniversity": "شوف أدوات الجامعات",
            "tryOurAI": "جرب ذكائنا الاصطناعي"
          },
          "about": {
            "title": "اعرف",
            "innovators": "فريق أودينتا",
            "subtitle": "نحو مستقبل أحسن لطب الأسنان بالذكاء الاصطناعي", // Slightly rephrased for warmth.
            "ourMission": "مهمتنا",
            "missionDescription": "بنغيّر تعليم وممارسة طب الأسنان بتقنيات ذكية بتزوّد دقة التشخيص وبتحسّن نتايج المرضى.", // More concise and engaging.
            "ourVision": "رؤيتنا",
            "visionDescription": "نبقى الأول عالميًا في حلول طب الأسنان الذكية للتعليم والعيادات.", // Simplified.
            "meetOurTeam": "فريقنا",
            "teamDescription": "خلّيط مميز من خبراء الذكاء الاصطناعي ودكاترة الأسنان.", // "خلّيط" is more conversational than "مزيج".
            "aiEngineer": "خبير ذكاء اصطناعي",
            "dentalExpert": "دكتور أسنان", // "طبيب" changed to "دكتور" for familiarity.
            "ourValues": "قيمنا",
            "valuesDescription": "المبادئ اللي بتمشّي شغلنا في أودينتا",
            "innovation": "ابتكار",
            "innovationText": "بنطور حلول جديدة لتحديات طب الأسنان باستخدام الذكاء الاصطناعي.", // Simplified.
            "clinicalExcellence": "تميّز طبي",
            "clinicalExcellenceText": "كل حاجة بنعملها موثّقة من دكاترة متخصصين عشان الدقة.", // "حاجة" is very Egyptian.
            "education": "تعليم",
            "educationText": "بنساعد في تعليم طب الأسنان بتقنيات تربط بين النظري والعملي.",
            "founded": "اتأسست",
            "joinUs": "انضم لثورة الأسنان",
            "ctaText": "سواء طالب، دكتور، أو صاحب عيادة، أودينتا عنده الحلول اللي تناسبك.", // More inclusive and engaging.
            "contactUs": "كلمنا",
            "getInTouch": "ابقى على تواصل"
          },
          "contact": {
            "title": "كلمنا",
            "withUs": "",
            "subtitle": "موجودين هنا عشان نجاوب على أسئلتك ونساعدك تستكشف إزاي أودينتا يغيّر عيادتك أو دراستك.", // Streamlined and conversational.
            "getInTouch": "ابقى على تواصل",
            "description": "كلمنا بالإيميل، التليفون، أو تابعنا على السوشيال ميديا. مستنيينك!", // "مستنيينك" adds warmth.
            "email": "إيميل",
            "phone": "تليفون",
            "address": "عنوان",
            "sendMessage": "ابعت رسالة",
            "name": "الاسم",
            "namePlaceholder": "اسمك الكريم",
            "emailPlaceholder": "إيميلك",
            "message": "رسالتك",
            "messagePlaceholder": "قولنا إزاي نقدر نساعدك؟",
            "submit": "ابعت",
            "formSuccess": "رسالتك وصلت! شكرًا ليك.", // Added "شكرًا ليك" for friendliness.
            "formError": "في حاجة غلط! جرب تاني.", // "في حاجة غلط" is very Egyptian.
            "exploreMore": "اكتشف أكتر",
            "withDentlyzer": "مع أودينتا",
            "ctaDescription": "شوف إزاي حلولنا الذكية بتحسّن تعليم وممارسة طب الأسنان في الجامعات.",
            "exploreUniversity": "للجامعات",
            "tryOurAI": "جرب ذكائنا الاصطناعي"
          },
          "universityServices": {
            "title": "تعليم الأسنان",
            "withAI": "بالذكاء الاصطناعي",
            "subtitle": "غيّر تعليم الأسنان بأدوات أودينتا الذكية للطلاب، الدكاترة، والجامعات.", // Streamlined.
            "tryAI": "جرب ذكائنا الاصطناعي",
            "contactUs": "كلمنا",
            "featuresTitle": "أدوات التعليم",
            "featuresDescription": "اعرف إزاي أودينتا بيحسّن التعلم والتدريب لطلاب الأسنان.",
            "aiXray": "تحليل الأشعة الذكي",
            "aiXrayDesc": "كشف فوري عن مشاكل زي التسوس وفقدان العظم بدقة 95%.", // Simplified dental terms.
            "studentWorkflow": "نظام الطلاب",
            "studentWorkflowDesc": "إدارة حالات الطلاب بسهولة مع تتبع التقدم.", // "سير عمل" changed to "نظام" for clarity.
            "supervisorReview": "مراجعة الدكاترة",
            "supervisorReviewDesc": "الدكاترة يقدروا يراجعوا ويعلّقوا على حالات الطلاب بسهولة.", // More natural.
            "digitalCharting": "رسم الأسنان الرقمي",
            "digitalChartingDesc": "مخطط تفاعلي لتخطيط العلاج والتدريب.", // Simplified.
            "analytics": "تحليلات التعلم",
            "analyticsDesc": "تابع تقدم الطلاب بتقارير واضحة.", // Streamlined.
            "appointments": "المواعيد",
            "appointmentsDesc": "نظام جدولة فعال لإدارة مواعيد الطلاب والمرضى.", // Consistent with earlier phrasing.
            "benefitsTitle": "إيه اللي هتستفيده؟",
            "benefitsDescription": "ليه كليات الأسنان بتختار أودينتا",
            "improvedLearning": "تعليم أحسن",
            "improvedLearningDesc": "تشخيصات ذكية وأدوات تفاعلية بتزوّد دقة الطلاب وثقتهم ",
            "realTimeFeedback": "ملاحظات فورية",
            "realTimeFeedbackDesc": "تحليل ذكي وتعليقات الدكاترة بتسرّع تعلم الطلاب.", // Streamlined.
            "efficientSupervision": "إشراف أسهل",
            "efficientSupervisionDesc": "مراجعة سهلة بتوفّر وقت الدكاترة مع الحفاظ على الجودة.", // Simplified.
            "ctaTitle": "طوّر برنامجك التعليمي",
            "ctaDescription": "انضم لكليات الأسنان اللي بتستخدم أودينتا عشان تجهّز جيل جديد من الدكاترة."
          },
          "clinicsServices": {
            "title": "عيادات ذكية",
            "withAI": "بالذكاء الاصطناعي",
            "subtitle": "سهّل شغل عيادتك مع أدوات أودينتا الذكية للتشخيص وإدارة المرضى.", // More engaging.
            "tryAI": "جرب ذكائنا الاصطناعي",
            "contactUs": "كلمنا",
            "featuresTitle": "أدوات العيادات",
            "featuresDescription": "شوف إزاي أودينتا بيحسّن كفاءة العيادات ورضا المرضى.",
            "aiXray": "تحليل الأشعة الذكي",
            "aiXrayDesc": "كشف سريع لمشاكل الأسنان بدقة 95% عشان قرارات دقيقة.", // Streamlined.
            "patientManagement": "إدارة المرضى",
            "patientManagementDesc": "نظام مركزي لسجلات المرضى وتاريخ العلاج.", // Simplified.
            "appointments": "حجز المواعيد",
            "appointmentsDesc": "جدولة ذكية مع تذكيرات عشان تنظيم أحسن.", // Added "عشان تنظيم أحسن".
            "digitalCharting": "رسم الأسنان الرقمي",
            "digitalChartingDesc": "مخطط دقيق لتوثيق وتخطيط العلاج.", // Simplified.
            "analytics": "تحليلات العيادة",
            "analyticsDesc": "تابع أداء العيادة واتجاهات المرضى بتقارير واضحة.", // Streamlined.
            "treatmentPlanning": "تخطيط العلاج",
            "treatmentPlanningDesc": "اقتراحات سريعة ودقيقة لخطط العلاج.", // Simplified.
            "benefitsTitle": "إيه اللي هتستفيده؟",
            "benefitsDescription": "ليه العيادات بتختار أودينتا",
            "enhancedDiagnostics": "تشخيصات أحسن",
            "enhancedDiagnosticsDesc": "تحليل ذكي بيقلل الأخطاء ويحسّن النتايج.", // Streamlined.
            "streamlinedOperations": "شغل أسهل",
            "streamlinedOperationsDesc": "أدوات مدمجة للجدولة والفواتير بتوفّر وقت.", // Simplified.
            "improvedPatientCare": "رعاية أفضل للمرضى",
            "improvedPatientCareDesc": "خطط علاج مخصصة وتنظيم أحسن بيزوّد رضا المرضى.", // Streamlined.
            "ctaTitle": "غيّر عيادتك",
            "ctaDescription": "انضم للعيادات حول العالم اللي بتستخدم أودينتا لرعاية متميزة."
          },
            "appointment": {
              "BookYourAppointment": "احجز موعدك",
              "SelectUniversityDateTime": "اختر جامعة وتاريخ ووقت لحجز موعدك السني.",
              "FailedLoadUniversities": "فشل تحميل الجامعات. حاول مرة أخرى لاحقًا.",
              "FailedLoadSlots": "فشل تحميل المواعيد المتاحة. حاول مرة أخرى.",
              "FailedBookAppointment": "فشل حجز الموعد. حاول مرة أخرى.",
              "SelectUniversity": "اختر الجامعة",
              "ChooseUniversity": "اختر جامعة",
              "SelectDate": "اختر التاريخ",
              "AvailableTimeSlots": "المواعيد المتاحة",
              "PleaseSelectUniversity": "يرجى اختيار جامعة أولاً",
              "PleaseSelectDate": "يرجى اختيار تاريخ",
              "NoAvailableSlots": "لا توجد مواعيد متاحة للتاريخ المحدد",
              "PatientInformation": "معلومات المريض",
              "SelectedSlot": "الموعد المختار",
              "FullName": "الاسم الكامل",
              "PhoneNumber": "رقم الهاتف",
              "NationalID": "الرقم القومي",
              "Age": "العمر",
              "Gender": "الجنس",
              "ChooseGender": "اختر الجنس",
              "Male": "ذكر",
              "Female": "أنثى",
              "Other": "آخر",
              "Occupation": "المهنة",
              "Address": "العنوان",
              "ChiefComplaint": "الشكوى الرئيسية",
              "Submitting": "جارٍ الإرسال...",
              "BookAppointment": "حجز الموعد",
              "AppointmentConfirmed": "تم تأكيد الموعد",
              "SuccessfullyBookedUniversity": "تم حجز موعدك بنجاح مع عيادة الجامعة.",
              "FailedLoadUniversityInfo": "فشل تحميل معلومات الجامعة.",
              "FailedGeneratePDF": "فشل إنشاء ملف PDF. حاول مرة أخرى.",
              "AppointmentConfirmation": "تأكيد الموعد",
              "AppointmentID": "رقم الموعد",
              "UniversityClinic": "عيادة الجامعة",
              "Date": "التاريخ",
              "Time": "الوقت",
              "PatientName": "اسم المريض",
              "NationalID": "الرقم القومي",
              "Age": "العمر",
              "Gender": "الجنس",
              "ChiefComplaint": "الشكوى الرئيسية",
              "Occupation": "المهنة",
              "Address": "العنوان",
              "ScanQRCode": "امسح رمز QR لعرض تفاصيل الموعد.",
              "UniversityClinicContact": "تواصل مع عيادة الجامعة",
              "DentlyzerContact": "تواصل مع Odenta",
              "Email": "البريد الإلكتروني",
              "Phone": "الهاتف",
              "NoContactInfoAvailable": "لا تتوفر معلومات التواصل.",
              "DownloadConfirmation": "تحميل التأكيد",
              "BackToUniversities": "العودة إلى الجامعات",
              "UniversityDentalCare": "رعاية أسنان الجامعة",
              "DentalBookingPlatform": "منصة حجز الأسنان",
              "UnknownUniversity": "جامعة غير معروفة"
            },
          "select": {
            "chooseYourDentist": "اختار دكتور أسنانك",
            "description": "اختار بين عيادات الجامعات أو العيادات الخاصة عشان تلاقي الرعاية اللي تناسبك.", // Simplified and engaging.
            "free": "مجاني",
            "universityClinics": "عيادات الجامعات",
            "universityClinicsDesc": "رعاية أسنان في عيادات الجامعات تحت إشراف دكاترة وطلاب محترفين.", // Added "محترفين" for reassurance.
            "exploreUniversityClinics": "شوف عيادات الجامعات",
            "generalClinics": "عيادات خاصة",
            "generalClinicsDesc": "رعاية أسنان متميزة في عيادات خاصة بأحدث الأجهزة.", // Simplified and appealing.
            "exploreGeneralClinics": "شوف العيادات الخاصة",
            "comingSoonMessage": "جاية قريب!",
            "comingSoonDescription": "خاصية العيادات الخاصة لسة في الطريق. خليك متابع!", // More engaging and conversational.
            "closeButton": "اقفل"
          },

  "universityInfo": {
    "noServices": "لا توجد خدمات متاحة",
    "dentistryServices": "خدمات طب الأسنان",
    "slotPeriod": "فترة المواعيد",
    "about": "عن",
    "subtitle": "استكشف برامجنا وخدماتنا في طب الأسنان",
    "error": "خطأ",
    "backToUniversities": "العودة إلى الجامعات",
    "bookAppointment": "حجز موعد",
    "contactUs": "تواصل معنا",
    "facilities": "المرافق",
    "visitClinic": "زيارة عيادتنا",
    "address": "العنوان",
    "phone": "الهاتف",
    "email": "البريد الإلكتروني",
    "getDirections": "الحصول على الاتجاهات",
    "locationMap": "خريطة الموقع"
  },
  "universities": {
    "title": "جامعاتنا",
    "subtitle": "اكتشف أفضل جامعات طب الأسنان",
    "search": "ابحث عن الجامعات...",
    "allLocations": "جميع المواقع",
    "viewDentists": "عرض أطباء الأسنان",
    "noResults": "لم يتم العثور على نتائج",
    "adjustSearch": "حاول تعديل بحثك أو الفلتر"
  },
  "clinics": {
      "title": "ابحث عن طبيب أسنان",
      "subtitle": "اكتشف أفضل أطباء الأسنان واحجز موعدك اليوم.",
      "search": "البحث باسم الطبيب أو العيادة",
      "allLocations": "جميع المواقع",
      "error": "فشل في تحميل أطباء الأسنان",
      "noResults": "لم يتم العثور على أطباء أسنان",
      "adjustSearch": "حاول تعديل البحث أو فلتر الموقع",
      "noServices": "لا توجد خدمات مدرجة",
      "clinic": "العيادة",
      "viewDetails": "عرض التفاصيل"
    },
    "dentistInfo": {
      "error": "خطأ",
      "dentistNotFound": "لم يتم العثور على طبيب الأسنان المطلوب.",
      "backToClinics": "العودة إلى العيادات",
      "about": "عن",
      "services": "الخدمات",
      "noServices": "لا توجد خدمات مدرجة",
      "clinic": "العيادة",
      "slotPeriod": "فترة المواعيد",
      "workingHours": "ساعات العمل",
      "workingHoursDesc": "تحقق من توفر الطبيب.",
      "visitClinic": "زيارة عيادتنا",
      "address": "العنوان",
      "phone": "الهاتف",
      "email": "البريد الإلكتروني",
      "noPhone": "لم يتم تقديم رقم هاتف",
      "noEmail": "لم يتم تقديم بريد إلكتروني",
      "getDirections": "الحصول على الاتجاهات",
      "locationMap": "خريطة الموقع",
      "bookAppointment": "حجز موعد",
      "contactUs": "اتصل بنا",
      "monday": "الإثنين",
      "tuesday": "الثلاثاء",
      "wednesday": "الأربعاء",
      "thursday": "الخميس",
      "friday": "الجمعة",
      "saturday": "السبت",
      "sunday": "الأحد"
    }
  }
 }
};

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    supportedLngs: ['en', 'ar'],
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupLocalStorage: 'dentlyzer-language'
    },
    interpolation: {
      escapeValue: false
    },
    react: {
      useSuspense: true
    },
    debug: process.env.NODE_ENV === 'development',
    saveMissing: true,
    parseMissingKeyHandler: (key) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Missing translation: ${key}`);
      }
      return key;
    }
  });

// Set initial document direction
const setDocumentDirection = (lng) => {
  document.documentElement.lang = lng;
  document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
};

// Initialize direction
setDocumentDirection(i18n.language || 'en');

// Update direction when language changes
i18n.on('languageChanged', (lng) => {
  setDocumentDirection(lng);
  localStorage.setItem('dentlyzer-language', lng);
});

export default i18n;