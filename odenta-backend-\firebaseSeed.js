const bcrypt = require('bcryptjs');
const { connectFirestore, FirestoreHelpers } = require('./config/firebaseDb');
const { getAuth } = require('./config/firebase');
const { COLLECTIONS } = require('./models/firebase/index');

console.log('🌱 ODenta Firebase Seed - Migrating Accounts to Firestore');
console.log('='.repeat(60));

const seedFirebaseData = async () => {
  try {
    // Connect to Firebase
    await connectFirestore();
    console.log('✅ Connected to Firebase');

    // Get Firebase Auth instance (for future use)
    const auth = getAuth();
    console.log('✅ Firebase Auth initialized');

    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('🧹 Clearing existing data...');
    
    // Get all collections and clear them
    const collections = [
      COLLECTIONS.STUDENTS,
      COLLECTIONS.SUPERVISORS,
      COLLECTIONS.ADMINS,
      COLLECTIONS.ASSISTANTS,
      COLLECTIONS.PATIENTS,
      COLLECTIONS.APPOINTMENTS,
      COLLECTIONS.REVIEWS,
      COLLECTIONS.PROCEDURE_REQUESTS,
      COLLECTIONS.LAB_REQUESTS,
      COLLECTIONS.NEWS
    ];

    for (const collection of collections) {
      try {
        const docs = await FirestoreHelpers.getAll(collection);
        for (const doc of docs) {
          await FirestoreHelpers.delete(collection, doc.id);
        }
        console.log(`   Cleared ${collection}`);
      } catch (error) {
        console.log(`   ${collection} - no existing data or error: ${error.message}`);
      }
    }

    // Helper function to create user in Firestore only (for now)
    const createUserInFirestore = async (collection, userData) => {
      try {
        // Create in Firestore
        const firestoreUser = await FirestoreHelpers.create(collection, userData);
        console.log(`   ✅ Created in Firestore: ${userData.email}`);
        return firestoreUser;
      } catch (error) {
        console.error(`   ❌ Error creating user ${userData.email}:`, error.message);
        throw error;
      }
    };

    // Seed Students
    console.log('👨‍🎓 Seeding Students...');
    const students = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        plainPassword: 'STaiu-2025', // Store for reference
        name: 'Ahmed Hassan',
        studentId: 'AIU001',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
        appointments: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        plainPassword: 'STaiu-2025',
        name: 'Sara Mohamed',
        studentId: 'AIU002',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
        appointments: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('STaiu-2025', 10),
        plainPassword: 'STaiu-2025',
        name: 'Omar Ali',
        studentId: 'AIU003',
        role: 'student',
        university: 'AIU',
        patients: [],
        reviews: [],
        appointments: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const student of students) {
      await createUserInFirestore(COLLECTIONS.STUDENTS, student);
    }
    console.log('✅ Students seeded');

    // Seed Supervisors
    console.log('👨‍⚕️ Seeding Supervisors...');
    const supervisors = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('SVaiu-2025', 10),
        plainPassword: 'SVaiu-2025',
        name: 'Dr. Mahmoud Farouk',
        role: 'supervisor',
        university: 'AIU',
        pendingReviews: [],
        completedReviews: [],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('SVaiu-2025', 10),
        plainPassword: 'SVaiu-2025',
        name: 'Dr. Fatma Abdel Rahman',
        role: 'supervisor',
        university: 'AIU',
        pendingReviews: [],
        completedReviews: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const supervisor of supervisors) {
      await createUserInFirestore(COLLECTIONS.SUPERVISORS, supervisor);
    }
    console.log('✅ Supervisors seeded');

    // Seed Admins
    console.log('👨‍💼 Seeding Admins...');
    const admins = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('Aaiu-2025', 10),
        plainPassword: 'Aaiu-2025',
        name: 'Dr. Khaled Ibrahim',
        role: 'admin',
        university: 'AIU',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('Aaiu-2025', 10),
        plainPassword: 'Aaiu-2025',
        name: 'Dr. Nadia Saleh',
        role: 'admin',
        university: 'AIU',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('superadmin-2025', 10),
        plainPassword: 'superadmin-2025',
        name: 'Sousannah Magdy',
        role: 'superadmin',
        university: 'AIU',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('superadmin-2025', 10),
        plainPassword: 'superadmin-2025',
        name: 'ODenta Admin',
        role: 'superadmin',
        university: 'AIU',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const admin of admins) {
      await createUserInFirestore(COLLECTIONS.ADMINS, admin);
    }
    console.log('✅ Admins seeded');

    // Seed Assistants
    console.log('👩‍💼 Seeding Assistants...');
    const assistants = [
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('ASTaiu-2025', 10),
        plainPassword: 'ASTaiu-2025',
        name: 'Mona Youssef',
        role: 'assistant',
        university: 'AIU',
        affiliation: {
          type: 'university',
          id: 'AIU',
          name: 'AIU'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: '<EMAIL>',
        password: await bcrypt.hash('ASTaiu-2025', 10),
        plainPassword: 'ASTaiu-2025',
        name: 'Heba Mostafa',
        role: 'assistant',
        university: 'AIU',
        affiliation: {
          type: 'university',
          id: 'AIU',
          name: 'AIU'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    for (const assistant of assistants) {
      await createUserInFirestore(COLLECTIONS.ASSISTANTS, assistant);
    }
    console.log('✅ Assistants seeded');

    console.log('');
    console.log('🎉 Firebase seeding completed successfully!');
    console.log('');
    console.log('📋 Account Summary:');
    console.log('   👨‍🎓 Students: 3 accounts');
    console.log('   👨‍⚕️ Supervisors: 2 accounts');
    console.log('   👨‍💼 Admins: 4 accounts (including 2 superadmins)');
    console.log('   👩‍💼 Assistants: 2 accounts');
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log('   Students: <EMAIL> / STaiu-2025');
    console.log('   Supervisors: <EMAIL> / SVaiu-2025');
    console.log('   Admins: <EMAIL> / Aaiu-2025');
    console.log('   Assistants: <EMAIL> / ASTaiu-2025');
    console.log('   Superadmins: <EMAIL> / superadmin-2025');
    console.log('                <EMAIL> / superadmin-2025');
    console.log('');
    console.log('✅ All users created in Firestore');
    console.log('⚠️  Note: Users are NOT in Firebase Auth (will not appear in Firebase Console → Authentication)');
    console.log('   This is fine for backend authentication, but Google Sign-In may not work until Firebase Auth permissions are fixed.');
    console.log('');
    console.log('🔧 To fix Firebase Auth permissions:');
    console.log('   1. Go to: https://console.developers.google.com/iam-admin/iam/project?project=odenta-82359');
    console.log('   2. Find your service account and add "Firebase Admin" role');
    console.log('   3. Run: node firebaseSeed.js (with Firebase Auth enabled)');

  } catch (error) {
    console.error('❌ Error seeding Firebase data:', error);
    console.error(error.stack);
  } finally {
    console.log('🏁 Seeding process finished');
    process.exit(0);
  }
};

// Run the seed function
seedFirebaseData();
