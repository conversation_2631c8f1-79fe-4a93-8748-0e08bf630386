import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import cache, { CacheKeys, CacheTTL } from '../utils/cache';

/**
 * Custom hook for optimized data fetching with caching and pagination
 */
export const useOptimizedData = () => {
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Create axios config
  const getConfig = useCallback(() => {
    return token ? { headers: { Authorization: `Bearer ${token}` } } : {};
  }, [token]);

  /**
   * Fetch patients with pagination and caching
   */
  const fetchPatients = useCallback(async (options = {}) => {
    const {
      page = 1,
      limit = 20,
      search = '',
      studentFilter = 'all',
      sortBy = 'registrationDate',
      sortOrder = 'desc',
      useCache = true
    } = options;

    if (!user || !token) {
      throw new Error('Authentication required');
    }

    const cacheKey = CacheKeys.PATIENTS(page, limit, search, studentFilter, sortBy, sortOrder);
    
    if (useCache) {
      return await cache.getOrSet(
        cacheKey,
        async () => {
          console.log('Fetching patients from API...');
          const config = getConfig();
          const params = { page, limit, search, studentFilter, sortBy, sortOrder };
          
          const response = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/admin/patients`,
            { ...config, params }
          );
          
          return response.data;
        },
        CacheTTL.MEDIUM
      );
    } else {
      // Force fresh fetch
      cache.delete(cacheKey);
      const config = getConfig();
      const params = { page, limit, search, studentFilter, sortBy, sortOrder };
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/admin/patients`,
        { ...config, params }
      );
      
      // Update cache with fresh data
      cache.set(cacheKey, response.data, CacheTTL.MEDIUM);
      return response.data;
    }
  }, [user, token, getConfig]);

  /**
   * Fetch students with pagination and caching
   */
  const fetchStudents = useCallback(async (options = {}) => {
    const {
      page = 1,
      limit = 50,
      search = '',
      useCache = true
    } = options;

    if (!user || !token) {
      throw new Error('Authentication required');
    }

    const cacheKey = CacheKeys.STUDENTS(page, limit, search);
    
    if (useCache) {
      return await cache.getOrSet(
        cacheKey,
        async () => {
          console.log('Fetching students from API...');
          const config = getConfig();
          const params = { page, limit, search };
          
          const response = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/admin/students`,
            { ...config, params }
          );
          
          return response.data;
        },
        CacheTTL.LONG
      );
    } else {
      // Force fresh fetch
      cache.delete(cacheKey);
      const config = getConfig();
      const params = { page, limit, search };
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/admin/students`,
        { ...config, params }
      );
      
      // Update cache with fresh data
      cache.set(cacheKey, response.data, CacheTTL.LONG);
      return response.data;
    }
  }, [user, token, getConfig]);

  /**
   * Fetch procedure requests with caching
   */
  const fetchProcedureRequests = useCallback(async (useCache = true) => {
    if (!user || !token) {
      throw new Error('Authentication required');
    }

    const cacheKey = CacheKeys.PROCEDURE_REQUESTS();
    
    if (useCache) {
      return await cache.getOrSet(
        cacheKey,
        async () => {
          console.log('Fetching procedure requests from API...');
          const config = getConfig();
          
          const response = await axios.get(
            `${process.env.REACT_APP_API_URL}/api/procedure-requests`,
            config
          );
          
          return Array.isArray(response.data) ? response.data : [];
        },
        CacheTTL.SHORT
      );
    } else {
      // Force fresh fetch
      cache.delete(cacheKey);
      const config = getConfig();
      
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/procedure-requests`,
        config
      );
      
      const data = Array.isArray(response.data) ? response.data : [];
      cache.set(cacheKey, data, CacheTTL.SHORT);
      return data;
    }
  }, [user, token, getConfig]);

  /**
   * Fetch patient details with caching
   */
  const fetchPatientDetails = useCallback(async (nationalId, useCache = true) => {
    if (!nationalId || !token) {
      throw new Error('National ID and authentication required');
    }

    const cacheKey = CacheKeys.PATIENT_DETAILS(nationalId);
    
    if (useCache) {
      return await cache.getOrSet(
        cacheKey,
        async () => {
          console.log(`Fetching patient details for ${nationalId} from API...`);
          const config = getConfig();
          
          const [patientResponse, appointmentsResponse] = await Promise.all([
            axios.get(
              `${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`,
              config
            ),
            axios.get(
              `${process.env.REACT_APP_API_URL}/api/appointments/patient/${nationalId}`,
              config
            ).catch(() => ({ data: [] })) // Handle appointments fetch failure gracefully
          ]);
          
          return {
            ...patientResponse.data,
            appointments: appointmentsResponse.data || []
          };
        },
        CacheTTL.MEDIUM
      );
    } else {
      // Force fresh fetch
      cache.delete(cacheKey);
      const config = getConfig();
      
      const [patientResponse, appointmentsResponse] = await Promise.all([
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`,
          config
        ),
        axios.get(
          `${process.env.REACT_APP_API_URL}/api/appointments/patient/${nationalId}`,
          config
        ).catch(() => ({ data: [] }))
      ]);
      
      const data = {
        ...patientResponse.data,
        appointments: appointmentsResponse.data || []
      };
      
      cache.set(cacheKey, data, CacheTTL.MEDIUM);
      return data;
    }
  }, [token, getConfig]);

  /**
   * Invalidate cache for specific data types
   */
  const invalidateCache = useCallback((type, ...args) => {
    switch (type) {
      case 'patients':
        // Clear all patient-related cache entries
        const stats = cache.getStats();
        stats.keys.forEach(key => {
          if (key.startsWith('patients_')) {
            cache.delete(key);
          }
        });
        break;
      case 'students':
        // Clear all student-related cache entries
        const studentStats = cache.getStats();
        studentStats.keys.forEach(key => {
          if (key.startsWith('students_')) {
            cache.delete(key);
          }
        });
        break;
      case 'patient':
        if (args[0]) {
          cache.delete(CacheKeys.PATIENT_DETAILS(args[0]));
        }
        break;
      case 'procedure-requests':
        cache.delete(CacheKeys.PROCEDURE_REQUESTS());
        break;
      case 'all':
        cache.clear();
        break;
      default:
        console.warn(`Unknown cache type: ${type}`);
    }
  }, []);

  return {
    loading,
    error,
    setLoading,
    setError,
    fetchPatients,
    fetchStudents,
    fetchProcedureRequests,
    fetchPatientDetails,
    invalidateCache,
    cacheStats: cache.getStats()
  };
};

export default useOptimizedData;
