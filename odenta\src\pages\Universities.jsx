import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';

const Universities = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [universities, setUniversities] = useState([]);

  // Fetch universities from API
  useEffect(() => {
    const fetchUniversities = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/universities`);
        if (!response.ok) {
          throw new Error('Failed to fetch universities');
        }
        const data = await response.json();
        setUniversities(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching universities:', error);
        setLoading(false);
      }
    };
    fetchUniversities();
  }, []);

  // Unique locations for filter
  const locations = [...new Set(universities.map((uni) => 
    uni.address?.city?.[i18n.language] || uni.address?.city || 'Unknown Location'
  ).filter(Boolean))];

  // Filter universities
  const filteredUniversities = universities.filter((university) => {
    const matchesSearch = university.name?.[i18n.language]?.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         university.name?.toLowerCase().includes(searchQuery.toLowerCase()) || false;
    const matchesLocation = selectedLocation
      ? (university.address?.city?.[i18n.language] || university.address?.city || 'Unknown Location') === selectedLocation
      : true;
    return matchesSearch && matchesLocation;
  });

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div
      className={`font-sans text-[#333333] bg-white min-h-screen ${
        document.documentElement.dir === 'rtl' ? 'text-right' : 'text-left'
      }`}
    >
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-28 text-center relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-6 leading-tight"
          >
            {t('universities.title')}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
              {t('universities.subtitle')}
            </span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-xl text-[#333333] max-w-3xl mx-auto"
          >
            {t('universities.description')}
          </motion.p>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="max-w-7xl mx-auto px-6 py-12 bg-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-white p-6 rounded-xl shadow-sm border border-[#20B2AA] border-opacity-30 mb-12"
        >
          <div className="flex flex-col md:flex-row gap-4 justify-between items-center">
            <div className="w-full md:w-1/2 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={t('universities.search')}
                className="w-full pl-12 pr-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
              />
              <svg
                className="h-5 w-5 text-[#333333] text-opacity-50 absolute left-4 top-3.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <div className="w-full md:w-1/3 relative">
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full pl-4 pr-10 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] appearance-none bg-white"
              >
                <option value="">{t('universities.allLocations')}</option>
                {locations.map((location, index) => (
                  <option key={index} value={location}>{location}</option>
                ))}
              </select>
              <svg
                className="h-5 w-5 text-[#333333] absolute right-2 top-3.5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* University List */}
        {filteredUniversities.length > 0 ? (
          <motion.div
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredUniversities.map((university) => (
              <motion.div
                key={university.universityId}
                variants={item}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[#20B2AA] border-opacity-30 hover:border-[#0077B6] group"
                whileHover={{ y: -5 }}
              >
                <div className="h-48 overflow-hidden rounded-t-xl">
                  <img
                    src={university.image || './imgs/default-campus.jpeg'}
                    alt={university.name?.[i18n.language] || university.name || 'University'}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <img
                      src={university.logo || './imgs/default-logo.png'}
                      alt={university.name?.[i18n.language] || university.name || 'University'}
                      className="w-12 h-12 mr-4 object-contain"
                    />
                    <h2 className="text-xl font-bold text-[#0077B6]">{university.name?.[i18n.language] || university.name || 'University'}</h2>
                  </div>
                  <p className="text-[#333333] mb-4">{university.description?.[i18n.language] || university.description || 'No description available'}</p>
                  <div className="flex items-center text-[#333333] text-opacity-70 mb-6">
                    <svg
                      className="h-5 w-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    <span>{`${university.address?.city?.[i18n.language] || university.address?.city || 'Unknown City'}, ${university.address?.country?.[i18n.language] || university.address?.country || 'Unknown Country'}`}</span>
                  </div>
                  <motion.button
                    onClick={() => {
                      console.log(`Navigating to university with ID: ${university.universityId}`); // Debug log
                      navigate(`/university-info/${university.universityId}`);
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-lg font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                  >
                    {t('universities.viewDentists')}
                    <svg
                      className="h-5 w-5 ml-2"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-white rounded-xl shadow-sm border border-[#20B2AA] border-opacity-30 p-12 text-center"
          >
            <svg
              className="h-16 w-16 mx-auto text-[#0077B6] text-opacity-50 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="text-xl font-medium text-[#0077B6] mb-2">{t('universities.noResults')}</h3>
            <p className="text-[#333333] text-opacity-70">{t('universities.adjustSearch')}</p>
          </motion.div>
        )}
      </section>

      <Footer />
    </div>
  );
};

export default Universities;