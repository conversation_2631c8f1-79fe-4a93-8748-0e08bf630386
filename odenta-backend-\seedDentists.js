const mongoose = require('mongoose');
const Dentist = require('./models/Dentist'); // Adjust path to your Dentist model
const config = require('./config/config');

// MongoDB connection using environment variables
const connectDB = async () => {
  try {
    await mongoose.connect(config.MONGO_URI, {
      dbName: 'dentlyzer', // Database name
    });
    console.log('✅ MongoDB connected');
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Dentist data
const dentistData = [
  {
    name: 'Dr. <PERSON>',
    specialties: ['Cosmetic Dentistry', 'General Dentistry'],
    experience: '10 years',
    about:
      'Dr. <PERSON> is a highly skilled cosmetic dentist with a passion for creating beautiful smiles. She specializes in veneers, whitening, and aesthetic restorations.',
    clinic: 'AIU Dentistry Hospital',
    location: 'New Alamein City, Egypt',
    workingHours: {
      monday: '9:00 AM - 5:00 PM',
      tuesday: '9:00 AM - 5:00 PM',
      wednesday: '9:00 AM - 5:00 PM',
      thursday: '9:00 AM - 5:00 PM',
      friday: 'Closed',
      saturday: '10:00 AM - 2:00 PM',
      sunday: 'Closed',
    },
    contact: {
      phone: '+20 3 502 6001',
      email: '<EMAIL>',
    },
    mapUrl:
      'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
    image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  {
    name: 'Dr. Omar Khaled',
    specialties: ['Oral Surgery', 'Implantology'],
    experience: '8 years',
    about:
      'Dr. Omar Khaled is an expert oral surgeon with extensive experience in complex dental surgeries and implant placements, ensuring patient comfort and precision.',
    clinic: 'AIU Dentistry Hospital',
    location: 'New Alamein City, Egypt',
    workingHours: {
      monday: '10:00 AM - 6:00 PM',
      tuesday: '10:00 AM - 6:00 PM',
      wednesday: '10:00 AM - 6:00 PM',
      thursday: '10:00 AM - 6:00 PM',
      friday: 'Closed',
      saturday: 'Closed',
      sunday: 'Closed',
    },
    contact: {
      phone: '+20 3 502 6002',
      email: '<EMAIL>',
    },
    mapUrl:
      'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
    image: 'https://images.unsplash.com/photo-1629904853716-f0bc54eea481?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  {
    name: 'Dr. Sara Mostafa',
    specialties: ['Pediatric Dentistry'],
    experience: '6 years',
    about:
      'Dr. Sara Mostafa specializes in pediatric dentistry, providing gentle and effective dental care for children, with a focus on preventive treatments.',
    clinic: 'AIU Dentistry Hospital',
    location: 'New Alamein City, Egypt',
    workingHours: {
      monday: '8:00 AM - 4:00 PM',
      tuesday: '8:00 AM - 4:00 PM',
      wednesday: '8:00 AM - 4:00 PM',
      thursday: '8:00 AM - 4:00 PM',
      friday: 'Closed',
      saturday: '9:00 AM - 1:00 PM',
      sunday: 'Closed',
    },
    contact: {
      phone: '+20 3 502 6003',
      email: '<EMAIL>',
    },
    mapUrl:
      'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
    image: 'https://images.unsplash.com/photo-1657815487740-2b07d6a9b3d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
  {
    name: 'Dr. Youssef Ahmed',
    specialties: ['Prosthodontics', 'Restorative Dentistry'],
    experience: '12 years',
    about:
      'Dr. Youssef Ahmed is a leading prosthodontist, renowned for his expertise in dental restorations, crowns, bridges, and dentures, enhancing patient smiles.',
    clinic: 'AIU Dentistry Hospital',
    location: 'New Alamein City, Egypt',
    workingHours: {
      monday: '9:00 AM - 5:00 PM',
      tuesday: '9:00 AM - 5:00 PM',
      wednesday: '9:00 AM - 5:00 PM',
      thursday: '9:00 AM - 5:00 PM',
      friday: 'Closed',
      saturday: 'Closed',
      sunday: 'Closed',
    },
    contact: {
      phone: '+20 3 502 6004',
      email: '<EMAIL>',
    },
    mapUrl:
      'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
    image: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  },
];

// Seed function
const seedDentists = async () => {
  await connectDB();

  try {
    // Clear existing dentist data
    await Dentist.deleteMany({});
    console.log('Existing dentist data cleared');

    // Insert dentist data
    await Dentist.insertMany(dentistData);
    console.log('Dentists seeded successfully');

    // Close connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('Error seeding dentists:', error);
    mongoose.connection.close();
    process.exit(1);
  }
};

// Run the seed function
seedDentists();