const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { UniversityHelpers } = require('../models/firebase/University');

// Helper function to generate time slots
const generateTimeSlots = (startDateStr, endDateStr, availableTimes, duration, holidays = []) => {
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);
  const timeSlots = [];

  // Map day of week to day name
  const dayMap = {
    0: 'Sunday',
    1: 'Monday',
    2: 'Tuesday',
    3: 'Wednesday',
    4: 'Thursday',
    5: 'Friday',
    6: 'Saturday'
  };

  // Loop through each day from start date to end date
  for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
    const dayOfWeek = date.getDay();
    const dayName = dayMap[dayOfWeek];

    // Skip days that are holidays
    if (holidays.includes(dayName)) {
      continue;
    }

    // For each available time slot
    availableTimes.forEach(time => {
      timeSlots.push({
        date: new Date(date),
        time: time,
        isAvailable: true,
        duration: duration
      });
    });
  }

  return timeSlots;
};

// Get all universities
exports.getAllUniversities = async (req, res) => {
  try {
    const universities = await FirestoreHelpers.find(COLLECTIONS.UNIVERSITIES);

    // Return only necessary fields
    const universitiesData = universities.map(university => ({
      id: university.id,
      name: university.name,
      universityId: university.universityId,
      address: university.address,
      contactInfo: university.contactInfo,
      description: university.description,
      logo: university.logo,
      image: university.image,
      dentistryServices: university.dentistryServices,
      dentistryInfo: university.dentistryInfo,
      holidays: university.holidays
    }));

    res.status(200).json(universitiesData);
  } catch (error) {
    console.error('Error fetching universities:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get single university by universityId
exports.getUniversityById = async (req, res) => {
  try {
    const universityId = req.params.id;
    console.log(`Fetching university with universityId: ${universityId}`);

    const university = await FirestoreHelpers.findOne(
      COLLECTIONS.UNIVERSITIES,
      { field: 'universityId', operator: '==', value: universityId }
    );

    if (!university) {
      console.log(`University not found for universityId: ${universityId}`);
      return res.status(404).json({ message: 'University not found' });
    }
    res.status(200).json(university);
  } catch (error) {
    console.error('Error fetching university:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Create a new university
exports.createUniversity = async (req, res) => {
  try {
    const {
      universityId,
      name,
      description,
      dentistryInfo,
      facilities,
      program,
      dentistryServices,
      address,
      contactInfo,
      slotBeginDate,
      slotEndDate,
      slotDuration,
      availableSlots,
      holidays,
    } = req.body;

    // Validate required fields
    if (!universityId || !name || !address || !contactInfo || !slotBeginDate || !slotEndDate || !slotDuration || !availableSlots) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Check if universityId is unique
    const existingUniversity = await FirestoreHelpers.findOne(
      COLLECTIONS.UNIVERSITIES,
      { field: 'universityId', operator: '==', value: universityId }
    );
    if (existingUniversity) {
      return res.status(400).json({ message: 'University ID already exists' });
    }

    // Process holidays
    const processedHolidays = Array.isArray(holidays) && holidays.length > 0
      ? holidays.map(day => day.charAt(0).toUpperCase() + day.slice(1).toLowerCase())
      : ['Friday', 'Sunday']; // Default holidays

    // Generate time slots based on the provided parameters
    const timeSlots = generateTimeSlots(slotBeginDate, slotEndDate, availableSlots, slotDuration, processedHolidays);

    // Ensure all required bilingual fields have values
    const defaultBilingualValue = { en: 'Not provided', ar: 'غير متوفر' };

    // Create a deep copy of the data to avoid modifying the request object directly
    const processedDescription = description && description.en && description.ar ? description : defaultBilingualValue;
    const processedDentistryInfo = dentistryInfo && dentistryInfo.en && dentistryInfo.ar ? dentistryInfo : defaultBilingualValue;
    const processedFacilities = facilities && facilities.en && facilities.ar ? facilities : defaultBilingualValue;
    const processedProgram = program && program.en && program.ar ? program : defaultBilingualValue;

    // Process dentistry services
    const processedDentistryServices = Array.isArray(dentistryServices) && dentistryServices.length > 0
      ? dentistryServices.map(service => {
          if (service && service.en && service.ar) {
            return service;
          }
          return { en: 'General Dentistry', ar: 'طب الأسنان العام' };
        })
      : [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];

    const universityData = {
      universityId,
      name,
      description: processedDescription,
      dentistryInfo: processedDentistryInfo,
      facilities: processedFacilities,
      program: processedProgram,
      dentistryServices: processedDentistryServices,
      address,
      contactInfo,
      slotBeginDate: new Date(slotBeginDate),
      slotEndDate: new Date(slotEndDate),
      slotDuration,
      timeSlots,
      holidays: processedHolidays,
      students: [],
      supervisors: [],
      admins: [],
      assistants: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const university = await FirestoreHelpers.create(COLLECTIONS.UNIVERSITIES, universityData);
    res.status(201).json(university);
  } catch (error) {
    console.error('Error creating university:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update an existing university
exports.updateUniversity = async (req, res) => {
  try {
    const universityId = req.params.id;
    const updates = { ...req.body };

    // Ensure all required bilingual fields have values if they are being updated
    const defaultBilingualValue = { en: 'Not provided', ar: 'غير متوفر' };

    if (updates.description && (!updates.description.en || !updates.description.ar)) {
      updates.description = defaultBilingualValue;
    }

    if (updates.dentistryInfo && (!updates.dentistryInfo.en || !updates.dentistryInfo.ar)) {
      updates.dentistryInfo = defaultBilingualValue;
    }

    if (updates.facilities && (!updates.facilities.en || !updates.facilities.ar)) {
      updates.facilities = defaultBilingualValue;
    }

    if (updates.program && (!updates.program.en || !updates.program.ar)) {
      updates.program = defaultBilingualValue;
    }

    // Process dentistry services if they are being updated
    if (updates.dentistryServices) {
      updates.dentistryServices = Array.isArray(updates.dentistryServices) && updates.dentistryServices.length > 0
        ? updates.dentistryServices.map(service => {
            if (service && service.en && service.ar) {
              return service;
            }
            return { en: 'General Dentistry', ar: 'طب الأسنان العام' };
          })
        : [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];
    }

    // Process holidays if they are being updated
    if (updates.holidays) {
      updates.holidays = Array.isArray(updates.holidays) && updates.holidays.length > 0
        ? updates.holidays.map(day => day.charAt(0).toUpperCase() + day.slice(1).toLowerCase())
        : ['Friday', 'Sunday']; // Default holidays
    }

    // Check if slot settings are being updated
    if (updates.slotBeginDate && updates.slotEndDate && updates.availableSlots && updates.slotDuration) {
      // Regenerate time slots based on the new parameters
      updates.timeSlots = generateTimeSlots(
        updates.slotBeginDate,
        updates.slotEndDate,
        updates.availableSlots,
        updates.slotDuration,
        updates.holidays || []
      );

      console.log(`Regenerated ${updates.timeSlots.length} time slots with duration ${updates.slotDuration} minutes`);
    }

    // First find the university
    const existingUniversity = await FirestoreHelpers.findOne(
      COLLECTIONS.UNIVERSITIES,
      { field: 'universityId', operator: '==', value: universityId }
    );

    if (!existingUniversity) {
      return res.status(404).json({ message: 'University not found' });
    }

    // Update with new data
    updates.updatedAt = new Date();
    const university = await FirestoreHelpers.update(
      COLLECTIONS.UNIVERSITIES,
      existingUniversity.id,
      updates
    );

    res.status(200).json(university);
  } catch (error) {
    console.error('Error updating university:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete a university
exports.deleteUniversity = async (req, res) => {
  try {
    const universityId = req.params.id;

    // First find the university
    const university = await FirestoreHelpers.findOne(
      COLLECTIONS.UNIVERSITIES,
      { field: 'universityId', operator: '==', value: universityId }
    );

    if (!university) {
      return res.status(404).json({ message: 'University not found' });
    }

    // Delete the university
    await FirestoreHelpers.delete(COLLECTIONS.UNIVERSITIES, university.id);

    res.status(200).json({ message: 'University deleted successfully' });
  } catch (error) {
    console.error('Error deleting university:', error);
    res.status(500).json({ message: 'Server error' });
  }
};