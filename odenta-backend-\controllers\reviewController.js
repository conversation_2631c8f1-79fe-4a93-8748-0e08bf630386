const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { ReviewHelpers, COLLECTION_NAME: REVIEW_COLLECTION } = require('../models/firebase/Review');

// Helper function to handle Firestore timestamps and date conversion
const parseFirestoreDate = (dateValue) => {
  try {
    if (!dateValue) return null;
    
    // Handle Firestore timestamp objects
    if (dateValue && typeof dateValue === 'object' && dateValue.toDate) {
      return dateValue.toDate();
    }
    
    // Handle Firestore timestamp with _seconds
    if (dateValue && typeof dateValue === 'object' && dateValue._seconds) {
      return new Date(dateValue._seconds * 1000);
    }
    
    // Handle regular date strings or Date objects
    if (typeof dateValue === 'string' || dateValue instanceof Date) {
      const date = new Date(dateValue);
      return isNaN(date.getTime()) ? null : date;
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing date:', error, 'Value:', dateValue);
    return null;
  }
};

// Helper function to get default review steps by procedure type
const getDefaultReviewSteps = (procedureType) => {
  const defaultSteps = {
    'Operative': ['Preparation', 'Restoration', 'Finishing'],
    'Fixed Prosthodontics': ['Preparation', 'Impression', 'Cementation'],
    'Removable Prosthodontics': ['Impression', 'Try-in', 'Delivery'],
    'Endodontics': ['Access', 'Cleaning', 'Filling'],
    'Periodontics': ['Scaling', 'Root Planning', 'Maintenance']
  };
  return defaultSteps[procedureType] || ['Step 1', 'Step 2', 'Step 3'];
};

const submitReview = async (req, res) => {
  try {
    const { patientId, procedureType, chartId, comment, reviewSteps, note } = req.body;

    // Log the request body for debugging
    console.log('Review submission request body:', req.body);

    const student = await FirestoreHelpers.findOne(
      COLLECTIONS.STUDENTS,
      { field: 'studentId', operator: '==', value: req.user.studentId }
    );
    if (!student) return res.status(404).json({ message: 'Student not found' });

    // Get default review steps for the procedure type if not provided
    const steps = reviewSteps || getDefaultReviewSteps(procedureType);

    // Create review object with all fields
    const reviewData = {
      patientId,
      studentId: student.studentId, // Use the actual student ID (like AIU001) instead of Firestore ID
      studentFirestoreId: student.id, // Store Firestore ID separately for internal use
      studentName: student.name,
      studentUniversity: student.university, // Add student's university
      procedureType,
      comment: comment || 'N/A',
      note: note || 'N/A',
      reviewSteps: steps,
      status: 'pending', // Set initial status
      submittedDate: new Date(), // Add submittedDate for consistency
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Only add chartId if it's provided
    if (chartId) {
      reviewData.chartId = chartId;
    }

    const review = await FirestoreHelpers.create(REVIEW_COLLECTION, reviewData);
    res.status(201).json(review);
  } catch (error) {
    console.error('Error submitting review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error: ' + error.message });
  }
};

const getReviewById = async (req, res) => {
  try {
    const review = await FirestoreHelpers.findById(REVIEW_COLLECTION, req.params.id);
    if (!review) return res.status(404).json({ message: 'Review not found' });
    res.json(review);
  } catch (error) {
    console.error('Error fetching review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getStudentReviews = async (req, res) => {
  try {
    console.log('getStudentReviews called with query:', req.query);
    console.log('User info:', req.user);
    console.log('User studentId:', req.user.studentId);
    console.log('User role:', req.user.role);

    // If studentId is provided in query params, fetch reviews for that student
    if (req.query.studentId) {
      console.log(`Looking for student with studentId: ${req.query.studentId}`);
      const student = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { field: 'studentId', operator: '==', value: req.query.studentId });

      if (!student) {
        console.log(`Student not found with studentId: ${req.query.studentId}`);
        return res.status(404).json({ message: 'Student not found' });
      }

      console.log(`Found student:`, student);
      console.log(`Student ID (Firestore): ${student.id}`);
      console.log(`Student studentId field: ${student.studentId}`);

      // Verify the requesting user is the same student or a supervisor/admin
      if (req.user.role === 'student' && req.user.studentId !== req.query.studentId) {
        console.log(`Access denied: User ${req.user.studentId} tried to access reviews for ${req.query.studentId}`);
        return res.status(403).json({ message: 'Access denied' });
      }

      console.log(`Looking for reviews with studentId: ${student.studentId}`);
      const reviews = await FirestoreHelpers.find(REVIEW_COLLECTION, { field: 'studentId', operator: '==', value: student.studentId });
      console.log('Raw reviews from database:', reviews);
      
      // Sort reviews by date using the helper function
      reviews.sort((a, b) => {
        const dateA = parseFirestoreDate(b.createdAt || b.submittedDate);
        const dateB = parseFirestoreDate(a.createdAt || a.submittedDate);
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1;
        if (!dateB) return -1;
        return dateA.getTime() - dateB.getTime();
      });
      console.log(`Found ${reviews.length} reviews for student ${req.query.studentId}`);
      return res.json(reviews);
    }

    // Original functionality for supervisors
    console.log(`Looking for reviews with supervisorId: ${req.user.id}`);
    const reviews = await FirestoreHelpers.find(REVIEW_COLLECTION, { field: 'supervisorId', operator: '==', value: req.user.id });
    reviews.sort((a, b) => {
      const dateA = parseFirestoreDate(b.createdAt || b.submittedDate);
      const dateB = parseFirestoreDate(a.createdAt || a.submittedDate);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateA.getTime() - dateB.getTime();
    });
    console.log(`Found ${reviews.length} reviews for supervisor ${req.user.id}`);
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching student reviews:', error.message);
    console.error('Error stack:', error.stack);
    console.error('Error details:', error);
    res.status(500).json({ message: 'Server error: ' + error.message });
  }
};

const getPendingReviews = async (req, res) => {
  try {
    // If supervisorId is not assigned yet, fetch all pending reviews for the university
    // This allows supervisors to see all pending reviews that need to be assigned
    let pendingReviews = await FirestoreHelpers.find(REVIEW_COLLECTION, { field: 'status', operator: '==', value: 'pending' });
    if (req.user.university) {
      pendingReviews = pendingReviews.filter(r =>
        (r.supervisorId === req.user.id) ||
        (!r.supervisorId && r.studentUniversity === req.user.university)
      );
    } else {
      pendingReviews = pendingReviews.filter(r => r.supervisorId === req.user.id);
    }
    pendingReviews.sort((a, b) => {
      const dateA = parseFirestoreDate(b.createdAt || b.submittedDate);
      const dateB = parseFirestoreDate(a.createdAt || a.submittedDate);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateA.getTime() - dateB.getTime();
    });
    res.json(pendingReviews);
  } catch (error) {
    console.error('Error fetching pending reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const updateReview = async (req, res) => {
  try {
    console.log('updateReview called with params:', req.params);
    console.log('updateReview called with body:', req.body);
    console.log('updateReview called with user:', req.user);
    
    const { procedureQuality, patientInteraction, status, comment, supervisorSignature, stepStatuses } = req.body;
    const review = await FirestoreHelpers.findById(REVIEW_COLLECTION, req.params.reviewId);
    
    console.log('Found review:', review);
    
    if (!review) return res.status(404).json({ message: 'Review not found' });

    // If the review already has a supervisor assigned, verify it's the current user
    if (review.supervisorId && review.supervisorId !== req.user.id) {
      return res.status(403).json({ message: 'Access denied: This review is assigned to another supervisor' });
    }

    console.log('Updating review with supervisor info:', {
      supervisorId: req.user.id,
      supervisorName: req.user.name || 'Unknown Supervisor'
    });

    // Create update object with only the fields that need to be updated
    const updateData = {
      procedureQuality: procedureQuality || review.procedureQuality,
      patientInteraction: patientInteraction || review.patientInteraction,
      status: status || review.status,
      comment: comment || review.comment,
      supervisorId: req.user.id,
      supervisorName: req.user.name || 'Unknown Supervisor',
      reviewedDate: new Date(),
      updatedAt: new Date()
    };

    console.log('Update data:', updateData);

    // Only update signature if provided
    if (supervisorSignature) {
      updateData.supervisorSignature = supervisorSignature;
      console.log('Including supervisor signature in update');
    }

    // Update step statuses if provided
    if (stepStatuses && typeof stepStatuses === 'object') {
      updateData.stepStatuses = stepStatuses;
      console.log('Including step statuses in update:', stepStatuses);
      
      // Update the reviewSteps with supervisor decisions
      if (review.reviewSteps && Array.isArray(review.reviewSteps)) {
        updateData.reviewSteps = review.reviewSteps.map((step, index) => ({
          ...step,
          supervisorStatus: stepStatuses[index] || 'pending'
        }));
      }
    }

    console.log('Final update data:', updateData);
    const updatedReview = await FirestoreHelpers.update(REVIEW_COLLECTION, req.params.reviewId, updateData);
    console.log('Review updated successfully with supervisor name:', updatedReview.supervisorName);
    res.json(updatedReview);
  } catch (error) {
    console.error('Error updating review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error: ' + error.message });
  }
};

const getAllReviews = async (req, res) => {
  try {
    if (!['admin', 'superadmin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    allReviews.sort((a, b) => {
      const dateA = parseFirestoreDate(b.createdAt || b.submittedDate);
      const dateB = parseFirestoreDate(a.createdAt || a.submittedDate);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateA.getTime() - dateB.getTime();
    });
    res.json(allReviews);
  } catch (error) {
    console.error('Error fetching all reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getReviewStepsByType = async (req, res) => {
  try {
    const { procedureType } = req.params;
    const { subType } = req.query;

    if (!procedureType) {
      return res.status(400).json({ message: 'Procedure type is required' });
    }

    // Pass the subType parameter to the model method
    const steps = ReviewHelpers.getReviewStepsByType(procedureType, subType);
    res.json(steps);
  } catch (error) {
    console.error('Error fetching review steps:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const updateReviewSteps = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reviewSteps, note } = req.body;

    const review = await FirestoreHelpers.findById(REVIEW_COLLECTION, reviewId);
    if (!review) return res.status(404).json({ message: 'Review not found' });

    // Verify the student owns this review
    if (review.studentId !== req.user.id) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updatedData = {
      ...review,
      reviewSteps: reviewSteps,
      note: note || review.note,
    };

    const updatedReview = await FirestoreHelpers.update(REVIEW_COLLECTION, reviewId, updatedData);
    res.json(updatedReview);
  } catch (error) {
    console.error('Error updating review steps:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const updateStepStatuses = async (req, res) => {
  try {
    const { stepStatuses } = req.body;
    const review = await FirestoreHelpers.findById(REVIEW_COLLECTION, req.params.reviewId);
    if (!review) return res.status(404).json({ message: 'Review not found' });

    // If the review already has a supervisor assigned, verify it's the current user
    if (review.supervisorId && review.supervisorId !== req.user.id) {
      return res.status(403).json({ message: 'Access denied: This review is assigned to another supervisor' });
    }

    console.log('Updating step statuses for review:', req.params.reviewId, 'Statuses:', stepStatuses);

    // Update the review with step statuses
    const updatedData = {
      ...review,
      stepStatuses: stepStatuses,
      updatedAt: new Date()
    };

    // Also update the reviewSteps with supervisor decisions
    if (review.reviewSteps && Array.isArray(review.reviewSteps)) {
      updatedData.reviewSteps = review.reviewSteps.map((step, index) => ({
        ...step,
        supervisorStatus: stepStatuses[index] || 'pending'
      }));
    }

    const updatedReview = await FirestoreHelpers.update(REVIEW_COLLECTION, req.params.reviewId, updatedData);
    console.log('Step statuses updated successfully');
    res.json(updatedReview);
  } catch (error) {
    console.error('Error updating step statuses:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getDoneReviews = async (req, res) => {
  try {
    // Get all reviews and filter in memory
    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    
    let filteredReviews = allReviews.filter(review => review.status !== 'pending');
    
    if (req.user.university) {
      filteredReviews = filteredReviews.filter(review => 
        review.supervisorId === req.user.id || 
        review.studentUniversity === req.user.university
      );
    } else {
      filteredReviews = filteredReviews.filter(review => review.supervisorId === req.user.id);
    }
    
    filteredReviews.sort((a, b) => new Date(b.reviewedDate || b.submittedDate) - new Date(a.reviewedDate || a.submittedDate));
    res.json(filteredReviews);
  } catch (error) {
    console.error('Error fetching done reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorReviews = async (req, res) => {
  try {
    // Extract filter parameters from query
    const {
      status,
      procedureType,
      studentId,
      startDate,
      endDate,
      search
    } = req.query;

    console.log('Supervisor reviews query params:', req.query);

    // Get all reviews and filter in memory
    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    
    // Filter out signature storage reviews
    let filteredReviews = allReviews.filter(review => 
      review.procedureType !== 'Signature Storage' && 
      review.patientId?.nationalId !== 'signature-storage'
    );
    
    // Filter by supervisor or university
    if (req.user.university) {
      filteredReviews = filteredReviews.filter(review => 
        review.supervisorId === req.user.id || 
        review.studentUniversity === req.user.university
      );
    } else {
      filteredReviews = filteredReviews.filter(review => review.supervisorId === req.user.id);
    }
    
    // Add filters if provided
    if (status) {
      filteredReviews = filteredReviews.filter(review => review.status === status);
    }
    
    if (procedureType) {
      filteredReviews = filteredReviews.filter(review => review.procedureType === procedureType);
    }
    
    if (studentId) {
      // Find the student by studentId
      const student = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { field: 'studentId', operator: '==', value: studentId });
      if (student) {
        filteredReviews = filteredReviews.filter(review => review.studentId === student.id);
      }
    }
    
    // Date range filter
    if (startDate || endDate) {
      filteredReviews = filteredReviews.filter(review => {
        const reviewDate = parseFirestoreDate(review.submittedDate);
        if (!reviewDate) return false;
        if (startDate && reviewDate < new Date(startDate)) return false;
        if (endDate && reviewDate > new Date(endDate)) return false;
        return true;
      });
    }
    
    // Search by patient name or student name
    if (search) {
      const searchLower = search.toLowerCase();
      filteredReviews = filteredReviews.filter(review => 
        (review.patientId?.fullName && review.patientId.fullName.toLowerCase().includes(searchLower)) ||
        (review.studentName && review.studentName.toLowerCase().includes(searchLower))
      );
    }
    
    console.log('Final filtered reviews count:', filteredReviews.length);
    
    const reviews = filteredReviews;
    reviews.sort((a, b) => {
      const dateA = parseFirestoreDate(a.submittedDate);
      const dateB = parseFirestoreDate(b.submittedDate);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateB.getTime() - dateA.getTime();
    });
    console.log(`Found ${reviews.length} reviews for supervisor`);
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching supervisor reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorSignature = async (req, res) => {
  try {
    // Find the most recent review with a signature by this supervisor
    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    const reviewsWithSignature = allReviews.filter(review => 
      review.supervisorId === req.user.id && 
      review.supervisorSignature
    );
    const review = reviewsWithSignature.sort((a, b) => {
      const dateA = parseFirestoreDate(b.reviewedDate || b.createdAt);
      const dateB = parseFirestoreDate(a.reviewedDate || a.createdAt);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateA.getTime() - dateB.getTime();
    })[0];

    if (review && review.supervisorSignature) {
      return res.json({ signature: review.supervisorSignature });
    }

    res.json({ signature: null });
  } catch (error) {
    console.error('Error fetching supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const saveSupervisorSignature = async (req, res) => {
  try {
    const { supervisorSignature } = req.body;

    if (!supervisorSignature) {
      return res.status(400).json({ message: 'Signature is required' });
    }

    console.log('Saving supervisor signature for user:', req.user);

    // First, try to find an existing review by this supervisor
    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    let existingReview = allReviews.find(review => 
      review.supervisorId === req.user.id &&
      review.patientId?.nationalId === 'signature-storage'
    );

    if (existingReview) {
      // Update the existing review with the new signature
      console.log('Updating existing signature review');
      const updatedData = {
        ...existingReview,
        supervisorSignature: supervisorSignature,
        reviewedDate: new Date(),
      };
      const updatedReview = await FirestoreHelpers.update(REVIEW_COLLECTION, existingReview.id, updatedData);
    } else {
      // Find any existing review to get a valid studentId
      console.log('Looking for any existing review to use for signature storage');
      const anyReview = allReviews[0]; // Use the first review from the already fetched list

      if (!anyReview) {
        console.error('No existing reviews found to use for signature storage');
        return res.status(500).json({ message: 'Cannot save signature: No reviews exist in the system' });
      }

      console.log('Creating new signature review with studentId from existing review');
      // Create a dummy review to store the signature using a valid studentId
      const dummyReview = await FirestoreHelpers.create(REVIEW_COLLECTION, {
        patientId: { nationalId: 'signature-storage', fullName: 'Signature Storage' },
        studentId: anyReview.studentId, // Using an existing valid studentId
        studentName: 'Signature Storage',
        supervisorId: req.user.id,
        supervisorName: req.user.name,
        supervisorSignature: supervisorSignature,
        procedureType: 'Signature Storage',
        status: 'accepted',
        reviewSteps: [],
        reviewedDate: new Date()
      });
    }

    res.json({ message: 'Signature saved successfully', signature: supervisorSignature });
  } catch (error) {
    console.error('Error saving supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorAnalytics = async (req, res) => {
  try {
    // Extract time range from query params
    const { timeRange = 'all' } = req.query;
    console.log('Analytics request for supervisor:', req.user.id, 'timeRange:', timeRange);

    // Determine date range based on timeRange
    let startDate = new Date(0); // Default to beginning of time
    const endDate = new Date(); // Current date

    if (timeRange === 'week') {
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
    } else if (timeRange === 'month') {
      startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
    } else if (timeRange === 'year') {
      startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);
    }

    console.log('Date range:', { startDate, endDate });

    // Get all reviews and filter in memory
    const allReviews = await FirestoreHelpers.getAll(REVIEW_COLLECTION);
    
    // Filter out signature storage reviews and apply date range
    let filteredReviews = allReviews.filter(review => {
      const reviewDate = parseFirestoreDate(review.submittedDate);
      return review.procedureType !== 'Signature Storage' && 
        review.patientId?.nationalId !== 'signature-storage' &&
        reviewDate && reviewDate >= startDate && reviewDate <= endDate;
    });
    
    // Filter by supervisor or university
    console.log('User info:', { id: req.user.id, university: req.user.university, role: req.user.role });
    
    if (req.user.university) {
      filteredReviews = filteredReviews.filter(review => {
        const matchesSupervisor = review.supervisorId === req.user.id;
        const matchesUniversity = review.studentUniversity === req.user.university;
        const isPendingWithoutSupervisor = review.status === 'pending' && !review.supervisorId;
        console.log('Review filtering:', { 
          reviewId: review._id, 
          supervisorId: review.supervisorId, 
          studentUniversity: review.studentUniversity,
          status: review.status,
          matchesSupervisor,
          matchesUniversity,
          isPendingWithoutSupervisor
        });
        return matchesSupervisor || matchesUniversity || isPendingWithoutSupervisor;
      });
    } else {
      filteredReviews = filteredReviews.filter(review => {
        const matches = review.supervisorId === req.user.id;
        const isPendingWithoutSupervisor = review.status === 'pending' && !review.supervisorId;
        console.log('Review filtering (no university):', { 
          reviewId: review._id, 
          supervisorId: review.supervisorId, 
          status: review.status,
          matches,
          isPendingWithoutSupervisor
        });
        return matches || isPendingWithoutSupervisor;
      });
    }
    
    console.log('Filtered reviews count:', filteredReviews.length);
    
    const reviews = filteredReviews;
    reviews.sort((a, b) => new Date(b.submittedDate) - new Date(a.submittedDate));

    console.log('Found', reviews.length, 'reviews for analytics');

    // Calculate analytics
    const analytics = {
      totalReviews: reviews.length,
      statusDistribution: {
        pending: reviews.filter(r => r.status === 'pending').length,
        accepted: reviews.filter(r => r.status === 'accepted').length,
        denied: reviews.filter(r => r.status === 'denied').length
      },
      procedureTypeDistribution: {},
      studentPerformance: {},
      reviewTrends: [],
      qualityMetrics: {
        avgProcedureQuality: 0,
        avgPatientInteraction: 0
      }
    };

    console.log('Status distribution:', analytics.statusDistribution);

    // Calculate procedure type distribution
    reviews.forEach(review => {
      const type = review.procedureType || 'Unknown';
      if (!analytics.procedureTypeDistribution[type]) {
        analytics.procedureTypeDistribution[type] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0
        };
      }
      analytics.procedureTypeDistribution[type].total++;
      if (review.status === 'accepted') analytics.procedureTypeDistribution[type].accepted++;
      else if (review.status === 'denied') analytics.procedureTypeDistribution[type].denied++;
      else analytics.procedureTypeDistribution[type].pending++;
    });

    console.log('Procedure types found:', Object.keys(analytics.procedureTypeDistribution));

    // Calculate student performance
    reviews.forEach(review => {
      const studentName = review.studentName || review.studentId?.name || 'Unknown';
      if (!analytics.studentPerformance[studentName]) {
        analytics.studentPerformance[studentName] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0,
          avgProcedureQuality: 0,
          avgPatientInteraction: 0,
          qualityRatings: [],
          interactionRatings: []
        };
      }

      analytics.studentPerformance[studentName].total++;
      if (review.status === 'accepted') analytics.studentPerformance[studentName].accepted++;
      else if (review.status === 'denied') analytics.studentPerformance[studentName].denied++;
      else analytics.studentPerformance[studentName].pending++;

      if (review.procedureQuality) {
        analytics.studentPerformance[studentName].qualityRatings.push(review.procedureQuality);
      }

      if (review.patientInteraction) {
        analytics.studentPerformance[studentName].interactionRatings.push(review.patientInteraction);
      }
    });

    // Calculate averages for each student
    Object.keys(analytics.studentPerformance).forEach(student => {
      const { qualityRatings, interactionRatings } = analytics.studentPerformance[student];

      if (qualityRatings.length > 0) {
        analytics.studentPerformance[student].avgProcedureQuality =
          (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);
      }

      if (interactionRatings.length > 0) {
        analytics.studentPerformance[student].avgPatientInteraction =
          (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);
      }

      // Remove the arrays from the response
      delete analytics.studentPerformance[student].qualityRatings;
      delete analytics.studentPerformance[student].interactionRatings;
    });

    console.log('Students found:', Object.keys(analytics.studentPerformance));

    // Calculate review trends by month
    const reviewsByMonth = {};
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    reviews.forEach(review => {
      const date = new Date(review.submittedDate);
      if (date >= sixMonthsAgo) {
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
        if (!reviewsByMonth[monthYear]) {
          reviewsByMonth[monthYear] = { total: 0, accepted: 0, denied: 0, pending: 0 };
        }
        reviewsByMonth[monthYear].total++;
        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;
        else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;
        else reviewsByMonth[monthYear].pending++;
      }
    });

    // Convert to array and sort by date
    analytics.reviewTrends = Object.keys(reviewsByMonth).map(month => ({
      month,
      ...reviewsByMonth[month]
    })).sort((a, b) => {
      const [aMonth, aYear] = a.month.split('/').map(Number);
      const [bMonth, bYear] = b.month.split('/').map(Number);
      return aYear === bYear ? aMonth - bMonth : aYear - bYear;
    });

    console.log('Trends calculated for', analytics.reviewTrends.length, 'months');

    // Calculate quality metrics
    const doneReviews = reviews.filter(r => r.status !== 'pending');
    if (doneReviews.length > 0) {
      analytics.qualityMetrics.avgProcedureQuality =
        (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1);
      analytics.qualityMetrics.avgPatientInteraction =
        (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1);
    }

    console.log('Quality metrics:', analytics.qualityMetrics);
    console.log('Sending analytics response');

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching supervisor analytics:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  submitReview,
  getReviewById,
  getStudentReviews,
  getPendingReviews,
  getDoneReviews,
  updateReview,
  getAllReviews,
  getReviewStepsByType,
  updateReviewSteps,
  updateStepStatuses,
  getSupervisorReviews,
  getSupervisorSignature,
  saveSupervisorSignature,
  getSupervisorAnalytics,
};