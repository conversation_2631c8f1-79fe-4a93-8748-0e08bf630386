const mongoose = require('mongoose');
const Dentist = require('./models/Dentist');
const config = require('./config/config');

// MongoDB connection using environment variables
const connectDB = async () => {
  try {
    await mongoose.connect(config.MONGO_URI, {
      dbName: 'dentlyzer', // Database name
    });
    console.log('✅ MongoDB connected');
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to generate time slots
const generateTimeSlots = (startDate, endDate, slotDuration, holidays) => {
  const slots = [];
  let currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.toLocaleString('en-US', { weekday: 'long' });
    if (!holidays.includes(dayOfWeek)) {
      for (let hour = 9; hour < 17; hour++) {
        for (let minute = 0; minute < 60; minute += slotDuration) {
          const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          slots.push({
            date: new Date(currentDate),
            time,
            isAvailable: true,
            duration: slotDuration,
          });
        }
      }
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return slots;
};

// Validate dentist data
const validateDentist = (dentist, index) => {
  if (!dentist.dentistId) {
    console.error(`Validation error: Dentist at index ${index} is missing dentistId`);
    return false;
  }
  if (!dentist.contactInfo || !dentist.contactInfo.email) {
    console.error(`Validation error: Dentist at index ${index} is missing contactInfo.email`);
    return false;
  }
  if (!dentist.address || !dentist.address.city || !dentist.address.city.en || !dentist.address.city.ar) {
    console.error(`Validation error: Dentist at index ${index} has invalid address.city`);
    return false;
  }
  return true;
};

// Sample dentist data with unique emails
const dentists = [
  {
    dentistId: 'D001',
    name: {
      en: 'Dr. Ahmed Khaled',
      ar: 'د. أحمد خالد',
    },
    clinicName: {
      en: 'Smile Bright Clinic',
      ar: 'عيادة سمايل برايت',
    },
    about: {
      en: 'Dr. Ahmed Khaled is a highly experienced dentist specializing in cosmetic dentistry and orthodontics.',
      ar: 'د. أحمد خالد طبيب أسنان ذو خبرة عالية متخصص في طب الأسنان التجميلي وتقويم الأسنان.',
    },
    services: [
      { en: 'Teeth Whitening', ar: 'تبييض الأسنان' },
      { en: 'Orthodontics', ar: 'تقويم الأسنان' },
      { en: 'Dental Implants', ar: 'زراعة الأسنان' },
    ],
    address: {
      street: {
        en: '123 Nile Street',
        ar: '123 شارع النيل',
      },
      city: {
        en: 'Cairo',
        ar: 'القاهرة',
      },
      country: {
        en: 'Egypt',
        ar: 'مصر',
      },
      postalCode: '11511',
    },
    contactInfo: {
      phone: '+201234567890',
      email: '<EMAIL>',
      website: 'https://smilebright.com',
    },
    image: '/imgs/dentist1.jpg',
    logo: '/imgs/smilebright-logo.png',
    mapUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3453.123456789!2d31.235711615125!3d30.044419881886!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzDCsDAyJzQwLjciTiAzMcKwMTQnMDguNiJF!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
    workingHours: {
      monday: '09:00-17:00',
      tuesday: '09:00-17:00',
      wednesday: '09:00-17:00',
      thursday: '09:00-17:00',
      friday: 'Closed',
      saturday: '09:00-13:00',
      sunday: 'Closed',
    },
    holidays: ['Friday', 'Sunday'],
    slotDuration: 60,
    slotBeginDate: new Date('2025-05-01'),
    slotEndDate: new Date('2025-08-31'),
    timeSlots: [],
  },
  // {
  //   dentistId: 'D002',
  //   name: {
  //     en: 'Dr. Sarah Mostafa',
  //     ar: 'د. سارة مصطفى',
  //   },
  //   clinicName: {
  //     en: 'Healthy Smile Clinic',
  //     ar: 'عيادة الابتسامة الصحية',
  //   },
  //   about: {
  //     en: 'Dr. Sarah Mostafa specializes in pediatric dentistry and preventive care.',
  //     ar: 'د. سارة مصطفى متخصصة في طب أسنان الأطفال والعناية الوقائية.',
  //   },
  //   services: [
  //     { en: 'Pediatric Dentistry', ar: 'طب أسنان الأطفال' },
  //     { en: 'Teeth Cleaning', ar: 'تنظيف الأسنان' },
  //     { en: 'Root Canal Therapy', ar: 'علاج قناة الجذر' },
  //   ],
  //   address: {
  //     street: {
  //       en: '456 Coastal Road',
  //       ar: '456 طريق الساحل',
  //     },
  //     city: {
  //       en: 'Alexandria',
  //       ar: 'الإسكندرية',
  //     },
  //     country: {
  //       en: 'Egypt',
  //       ar: 'مصر',
  //     },
  //     postalCode: '21500',
  //   },
  //   contactInfo: {
  //     phone: '+201987654321',
  //     email: '<EMAIL>', // Unique email
  //     website: 'https://healthysmile.com',
  //   },
  //   image: '/imgs/dentist2.jpg',
  //   logo: '/imgs/healthysmile-logo.png',
  //   mapUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3421.987654321!2d29.918711615125!3d31.200419881886!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzHCsDEyJzAxLjUiTiAyOcKwNTUnMDcuNiJF!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
  //   workingHours: {
  //     monday: '10:00-18:00',
  //     tuesday: '10:00-18:00',
  //     wednesday: '10:00-18:00',
  //     thursday: '10:00-18:00',
  //     friday: 'Closed',
  //     saturday: '10:00-14:00',
  //     sunday: 'Closed',
  //   },
  //   holidays: ['Friday', 'Sunday'],
  //   slotDuration: 45,
  //   slotBeginDate: new Date('2025-05-01'),
  //   slotEndDate: new Date('2025-08-31'),
  //   timeSlots: [],
  // },
];

const seedDentists = async () => {
  await connectDB();
  
  try {
    // Clear existing dentists
    await Dentist.deleteMany({});
    console.log('Existing dentists cleared');

    // Validate data
    const validDentists = dentists.filter((dentist, index) => validateDentist(dentist, index));

    // Check for duplicate dentistId or email
    const dentistIds = new Set();
    const emails = new Set();
    for (const dentist of validDentists) {
      if (dentistIds.has(dentist.dentistId)) {
        console.error(`Duplicate dentistId found: ${dentist.dentistId}`);
        return;
      }
      dentistIds.add(dentist.dentistId);
      if (emails.has(dentist.contactInfo.email)) {
        console.error(`Duplicate email found: ${dentist.contactInfo.email}`);
        return;
      }
      emails.add(dentist.contactInfo.email);
    }

    // Generate time slots for each dentist
    const dentistsWithSlots = validDentists.map((dentist) => ({
      ...dentist,
      timeSlots: generateTimeSlots(
        dentist.slotBeginDate,
        dentist.slotEndDate,
        dentist.slotDuration,
        dentist.holidays
      ),
    }));

    // Insert new dentists
    const insertedDentists = await Dentist.insertMany(dentistsWithSlots, { ordered: false });
    console.log(`Successfully seeded ${insertedDentists.length} dentists`);
  } catch (error) {
    console.error('Error seeding dentists:', error);
    if (error.writeErrors) {
      error.writeErrors.forEach((err, index) => {
        console.error(`Write error at document index ${err.index}:`, err.err);
      });
    }
  } finally {
    mongoose.connection.close();
  }
};

seedDentists();