const express = require('express');
const router = express.Router();
const {
  createAppointment,
  getAvailableSlots,
  getAppointmentsByDoctor,
  getAppointmentsByPatient,
  deleteAppointment,
  createPatientAppointment,
  getUniversityAvailableSlots,
  getAllAppointments,
  downloadSchedule,
  remindPatients,
  assignAppointment,
  updateAppointmentPatient,
  updateAppointment,
} = require('../controllers/appointmentController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Doctor-initiated appointments
router.post('/', createAppointment);
router.get('/available-slots', getAvailableSlots);
router.get('/my-appointments', auth, role('student', 'dentist'), getAppointmentsByDoctor);
router.get('/patient/:nationalId', auth, role('student', 'supervisor', 'admin', 'superadmin'), getAppointmentsByPatient);
router.delete('/:id', auth, role('student', 'superadmin'), deleteAppointment);

// Patient-initiated university appointments
router.post('/patient', createPatientAppointment);
router.get('/university-slots', getUniversityAvailableSlots);

// Admin and assistant endpoints
router.get('/all', auth, role('superadmin'), getAllAppointments);
router.get('/schedule', auth, role('assistant', 'superadmin', 'admin'), downloadSchedule);
router.post('/remind', auth, role('assistant'), remindPatients);
router.put('/:id/assign', auth, role('assistant'), assignAppointment);
router.put('/:id/patient', auth, role('assistant'), updateAppointmentPatient);
router.put('/:id', auth, role('assistant', 'superadmin', 'admin'), updateAppointment);

module.exports = router;