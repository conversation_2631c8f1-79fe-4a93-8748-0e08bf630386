// Utility functions for date formatting

// Parse date from various formats and return a valid Date object
export const parseDate = (dateString) => {
  console.log('parseDate input:', dateString, typeof dateString);
  
  if (!dateString) {
    console.log('parseDate: null/undefined input');
    return null;
  }
  
  // If it's already a Date object, return it
  if (dateString instanceof Date) {
    console.log('parseDate: already a Date object');
    return dateString;
  }
  
  // Handle Firestore Timestamp objects with toDate method
  if (dateString && typeof dateString === 'object' && dateString.toDate) {
    console.log('parseDate: Firestore Timestamp with toDate detected');
    return dateString.toDate();
  }
  
  // Handle Firestore Timestamp objects with _seconds and _nanoseconds
  if (dateString && typeof dateString === 'object' && dateString._seconds !== undefined) {
    console.log('parseDate: Firestore Timestamp with _seconds detected');
    const seconds = dateString._seconds;
    const nanoseconds = dateString._nanoseconds || 0;
    const milliseconds = seconds * 1000 + nanoseconds / 1000000;
    return new Date(milliseconds);
  }
  
  // If it's a timestamp (number), convert to Date
  if (typeof dateString === 'number') {
    console.log('parseDate: number timestamp');
    return new Date(dateString);
  }
  
  // Try to parse as ISO string
  if (typeof dateString === 'string') {
    console.log('parseDate: string input');
    
    // Handle ISO date strings
    if (dateString.includes('T') || dateString.includes('Z')) {
      const parsed = new Date(dateString);
      if (!isNaN(parsed.getTime())) {
        console.log('parseDate: parsed ISO string successfully');
        return parsed;
      }
    }
    
    // Handle date strings without time
    if (dateString.includes('-')) {
      const parsed = new Date(dateString + 'T00:00:00');
      if (!isNaN(parsed.getTime())) {
        console.log('parseDate: parsed date string with time added');
        return parsed;
      }
    }
    
    // Handle other date formats
    const parsed = new Date(dateString);
    if (!isNaN(parsed.getTime())) {
      console.log('parseDate: parsed other format');
      return parsed;
    }
  }
  
  console.log('parseDate: failed to parse, returning null');
  return null;
};

// Format date for display
export const formatDate = (dateString, options = {}) => {
  console.log('formatDate input:', dateString, typeof dateString);
  
  // Handle null, undefined, or empty string
  if (!dateString || dateString === '') {
    console.log('Empty date string, returning Invalid Date');
    return 'Invalid Date';
  }
  
  const date = parseDate(dateString);
  console.log('parseDate result:', date);
  
  if (!date) {
    console.log('parseDate returned null, returning Invalid Date');
    return 'Invalid Date';
  }
  
  const defaultOptions = {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  };
  
  const result = date.toLocaleDateString('en-US', { ...defaultOptions, ...options });
  console.log('formatDate result:', result);
  return result;
};

// Format date for short display (e.g., "Jan 15")
export const formatShortDate = (dateString) => {
  const date = parseDate(dateString);
  if (!date) return 'Invalid Date';
  
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
};

// Format date for full display (e.g., "Monday, January 15, 2024")
export const formatFullDate = (dateString) => {
  const date = parseDate(dateString);
  if (!date) return 'Invalid Date';
  
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Check if date is today
export const isToday = (dateString) => {
  const date = parseDate(dateString);
  if (!date) return false;
  
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

// Check if date is in the future
export const isFuture = (dateString) => {
  const date = parseDate(dateString);
  if (!date) return false;
  
  const today = new Date();
  return date > today;
};

// Get relative date string (e.g., "Today", "Tomorrow", "Yesterday")
export const getRelativeDate = (dateString) => {
  const date = parseDate(dateString);
  if (!date) return 'Invalid Date';
  
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (date.toDateString() === today.toDateString()) return 'Today';
  if (date.toDateString() === tomorrow.toDateString()) return 'Tomorrow';
  if (date.toDateString() === yesterday.toDateString()) return 'Yesterday';
  
  return formatShortDate(dateString);
}; 