import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaStar, FaSearch, FaUserGraduate, FaUserMd, FaCalendarAlt, FaClipboardList } from 'react-icons/fa';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const Reviews = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [reviews, setReviews] = useState([]);
  const [filteredReviews, setFilteredReviews] = useState([]);
  const [selectedReview, setSelectedReview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [procedureFilter, setProcedureFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [students, setStudents] = useState([]);
  const [supervisors, setSupervisors] = useState([]);
  const [procedureTypes, setProcedureTypes] = useState([]);
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view reviews.');
        setLoading(false);
        return;
      }

      if (!user.university) {
        setError('User profile incomplete. Missing university information.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };

        // First, fetch students and supervisors from the university
        const [studentsRes, supervisorsRes] = await Promise.all([
          axios.get(`http://localhost:5000/api/admin/students?university=${encodeURIComponent(user.university)}`, config),
          axios.get(`http://localhost:5000/api/admin/supervisors?university=${encodeURIComponent(user.university)}`, config)
        ]);

        // Get students and supervisors from this university
        const students = studentsRes.data || [];
        const supervisors = supervisorsRes.data || [];

        console.log(`University has ${students.length} students and ${supervisors.length} supervisors`);

        // Log student IDs for debugging
        if (students.length > 0) {
          console.log('Sample student data:', {
            name: students[0].name,
            _id: students[0]._id,
            studentId: students[0].studentId
          });
        }

        setStudents(students);
        setSupervisors(supervisors);

        // If no students found, no need to fetch reviews
        if (students.length === 0) {
          setError('No students found in your university.');
          setLoading(false);
          return;
        }

        // Now fetch reviews for each student in the university
        let allReviews = [];

        // Create an array of promises for fetching reviews for each student
        console.log('Fetching reviews for students...');

        const reviewPromises = students.map(student => {
          const url = `${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student.studentId}`;
          console.log(`Fetching reviews for student ${student.name} with URL: ${url}`);

          return axios.get(url, config)
            .then(response => {
              console.log(`Successfully fetched ${response.data.length} reviews for student ${student.name}`);
              return response;
            })
            .catch(error => {
              console.error(`Error fetching reviews for student ${student.name} (ID: ${student.studentId}):`, error.message);
              // Try with _id as fallback
              console.log(`Trying fallback with _id for student ${student.name}`);
              return axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/student?studentId=${student._id}`, config)
                .then(response => {
                  console.log(`Fallback successful! Fetched ${response.data.length} reviews for student ${student.name}`);
                  return response;
                })
                .catch(fallbackError => {
                  console.error(`Fallback also failed for student ${student.name}:`, fallbackError.message);
                  return { data: [] }; // Return empty array on error
                });
            });
        });

        // Execute all promises in parallel
        const reviewsResults = await Promise.all(reviewPromises);

        // Combine all reviews into a single array
        reviewsResults.forEach(result => {
          if (result.data && Array.isArray(result.data)) {
            allReviews = [...allReviews, ...result.data];
          }
        });

        console.log(`Found ${allReviews.length} total reviews for students in university ${user.university}`);

        // Remove any duplicate reviews (in case a student appears in multiple queries)
        const uniqueReviews = Array.from(new Map(allReviews.map(review => [review._id, review])).values());

        // Filter out signature storage reviews
        const filteredReviews = uniqueReviews.filter(review =>
          !(review.patientId && review.patientId.nationalId === 'signature-storage')
        );

        setReviews(filteredReviews);
        setFilteredReviews(filteredReviews);

        // Extract unique procedure types
        const uniqueProcedureTypes = [...new Set(filteredReviews.map(review => review.procedureType).filter(Boolean))];
        setProcedureTypes(uniqueProcedureTypes);

        // Remove the error message for no reviews found
        // if (filteredReviews.length === 0) {
        //   setError('No reviews found for students in your university.');
        // }
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage =
          err.response?.status === 404
            ? 'Reviews endpoint not found.'
            : err.response?.status === 401
            ? 'Unauthorized. Please log in again.'
            : err.response?.data?.message || 'Failed to load reviews';
        setError(errorMessage);
        if (err.response?.status === 401) navigate('/login');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate]);

  // Filter and sort reviews when filters change
  useEffect(() => {
    if (!reviews.length) return;

    let result = [...reviews];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(
        review =>
          review.studentName?.toLowerCase().includes(searchLower) ||
          review.supervisorName?.toLowerCase().includes(searchLower) ||
          review.patientId?.fullName?.toLowerCase().includes(searchLower) ||
          review.procedureType?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'declined') {
        // Handle both 'declined' and 'denied' statuses
        result = result.filter(review => review.status === 'declined' || review.status === 'denied');
      } else {
        result = result.filter(review => review.status === statusFilter);
      }
    }

    // Apply procedure filter
    if (procedureFilter !== 'all') {
      result = result.filter(review => review.procedureType === procedureFilter);
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0;

      if (sortBy === 'date') {
        comparison = new Date(a.submittedDate) - new Date(b.submittedDate);
      } else if (sortBy === 'student') {
        comparison = a.studentName.localeCompare(b.studentName);
      } else if (sortBy === 'procedure') {
        comparison = a.procedureType.localeCompare(b.procedureType);
      } else if (sortBy === 'status') {
        comparison = a.status.localeCompare(b.status);
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredReviews(result);
  }, [reviews, searchTerm, statusFilter, procedureFilter, sortBy, sortOrder]);

  // Helper function to get student details by ID
  const getStudentDetails = (studentId) => {
    if (!studentId || !students.length) return null;

    try {
      // Handle case where studentId is an object
      let idToFind;

      if (typeof studentId === 'object') {
        // Safely access properties with null checks
        idToFind = studentId ? (studentId.studentId || (studentId._id ? studentId._id : null)) : null;
      } else {
        idToFind = studentId;
      }

      if (!idToFind) return null;

      const student = students.find(student =>
        student && (student.studentId === idToFind || student._id === idToFind)
      );

      if (!student) return null;

      // Return a plain object with just the properties we need
      return {
        name: student.name || 'N/A',
        studentId: student.studentId || 'N/A',
        university: student.university || 'N/A',
        email: student.email || 'N/A'
      };
    } catch (error) {
      console.error('Error getting student details:', error);
      return null;
    }
  };

  // Helper function to get supervisor details by ID
  const getSupervisorDetails = (supervisorId) => {
    if (!supervisorId || !supervisors.length) return null;

    try {
      // Handle case where supervisorId is an object
      let idToFind;

      if (typeof supervisorId === 'object') {
        // Safely access properties with null checks
        idToFind = supervisorId && supervisorId._id ? supervisorId._id : null;
      } else {
        idToFind = supervisorId;
      }

      if (!idToFind) return null;

      const supervisor = supervisors.find(supervisor => supervisor && supervisor._id === idToFind);
      if (!supervisor) return null;

      // Return a plain object with just the properties we need
      return {
        name: supervisor.name || 'N/A',
        university: supervisor.university || 'N/A',
        email: supervisor.email || 'N/A'
      };
    } catch (error) {
      console.error('Error getting supervisor details:', error);
      return null;
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Reviews</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Manage student reviews in your university</p>
              </div>

              {/* Search and Filter Controls */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden mb-6"
              >
                <div className="p-4 sm:p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 sm:mb-6 gap-4">
                    <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                      <FaSearch className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                      Search & Filter
                    </h2>
                    <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                      <input
                        type="text"
                        placeholder="Search reviews..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm w-full sm:w-64`}
                      />
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                      >
                        <option value="all">All Statuses</option>
                        <option value="pending">Pending</option>
                        <option value="accepted">Accepted</option>
                        <option value="declined">Declined</option>
                      </select>
                      <select
                        value={procedureFilter}
                        onChange={(e) => setProcedureFilter(e.target.value)}
                        className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                      >
                        <option value="all">All Procedures</option>
                        {procedureTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                      >
                        <option value="date">Sort by Date</option>
                        <option value="student">Sort by Student</option>
                        <option value="procedure">Sort by Procedure</option>
                      </select>
                      <button
                        onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                        className={`px-3 sm:px-4 py-2 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors text-xs sm:text-sm`}
                      >
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Statistics Cards */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"
              >
                <motion.div variants={item} className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full bg-yellow-100 text-[${websiteColorPalette.primary}]`}>
                      <FaClipboardList className="h-4 w-4 sm:h-5 sm:w-5" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm text-gray-500 font-medium">Pending Reviews</p>
                      <p className="text-xl sm:text-2xl font-bold text-gray-900">
                        {reviews.filter(r => r.status === 'pending').length}
                      </p>
                    </div>
                  </div>
                </motion.div>
                <motion.div variants={item} className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full bg-green-100 text-[${websiteColorPalette.accent}]`}>
                      <FaStar className="h-4 w-4 sm:h-5 sm:w-5" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm text-gray-500 font-medium">Accepted Reviews</p>
                      <p className="text-xl sm:text-2xl font-bold text-gray-900">
                        {reviews.filter(r => r.status === 'accepted').length}
                      </p>
                    </div>
                  </div>
                </motion.div>
                <motion.div variants={item} className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full bg-red-100 text-red-500`}>
                      <FaClipboardList className="h-4 w-4 sm:h-5 sm:w-5" />
                    </div>
                    <div className="ml-3 sm:ml-4">
                      <p className="text-xs sm:text-sm text-gray-500 font-medium">Declined Reviews</p>
                      <p className="text-xl sm:text-2xl font-bold text-gray-900">
                        {reviews.filter(r => r.status === 'declined' || r.status === 'denied').length}
                      </p>
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Review List */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-4 sm:p-6">
                  <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] mb-4 sm:mb-6 flex items-center`}>
                    <FaStar className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                    Review List
                  </h2>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supervisor</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredReviews.length === 0 ? (
                          <tr>
                            <td colSpan="6" className="px-3 sm:px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mb-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                  />
                                </svg>
                                <h3 className="text-base sm:text-lg font-medium text-gray-900">No reviews found</h3>
                                <p className="mt-1 text-gray-500 text-center text-sm">
                                  {reviews.length > 0
                                    ? "No reviews match your current filters."
                                    : "No reviews submitted for your university."}
                                </p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          filteredReviews.map((review) => (
                            <motion.tr
                              key={review._id}
                              variants={item}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => {
                                // Create a safe copy of the review to prevent rendering objects directly
                                const safeReview = {
                                  ...review,
                                  // Ensure studentId is a string if it's an object
                                  studentId: review.studentId
                                    ? (typeof review.studentId === 'object'
                                      ? (review.studentId.studentId || review.studentId._id || 'Unknown ID')
                                      : review.studentId)
                                    : 'Unknown ID',
                                  // Ensure supervisorId is a string if it's an object
                                  supervisorId: review.supervisorId
                                    ? (typeof review.supervisorId === 'object'
                                      ? (review.supervisorId._id || 'Unknown ID')
                                      : review.supervisorId)
                                    : null,
                                  // Ensure patientId is properly formatted
                                  patientId: review.patientId
                                    ? (typeof review.patientId === 'object'
                                      ? {
                                          ...review.patientId,
                                          // Ensure fullName is a string
                                          fullName: review.patientId.fullName || 'Unknown Patient',
                                          // Ensure nationalId is a string
                                          nationalId: review.patientId.nationalId || 'Unknown ID'
                                        }
                                      : review.patientId)
                                    : 'Unknown Patient',
                                  // Ensure steps are properly formatted
                                  steps: Array.isArray(review.steps)
                                    ? review.steps.map((step, index) => {
                                        if (!step) return { name: `Step ${index + 1}`, completed: false, description: '' };
                                        return {
                                          ...step,
                                          name: step.name || step.description || `Step ${index + 1}`,
                                          completed: !!step.completed,
                                          description: step.description || ''
                                        };
                                      })
                                    : [],
                                  // Ensure reviewSteps are properly formatted
                                  reviewSteps: Array.isArray(review.reviewSteps)
                                    ? review.reviewSteps.map((step, index) => {
                                        if (!step) return { description: `Step ${index + 1}`, completed: false };
                                        return {
                                          ...step,
                                          description: step.description || `Step ${index + 1}`,
                                          completed: !!step.completed
                                        };
                                      })
                                    : [],
                                  // Include supervisor signature if available
                                  supervisorSignature: review.supervisorSignature || null
                                };

                                setSelectedReview(safeReview);
                              }}
                            >
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                <div className="flex items-center">
                                  <FaUserGraduate className={`h-3 w-3 sm:h-4 sm:w-4 text-[${websiteColorPalette.primary}] mr-2`} />
                                  {review.studentName}
                                </div>
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {typeof review.patientId === 'object' && review.patientId?.fullName
                                  ? review.patientId.fullName
                                  : (typeof review.patientId === 'string'
                                      ? review.patientId
                                      : "Unknown Patient")}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{review.procedureType}</td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    review.status === 'accepted'
                                      ? `bg-green-100 text-[${websiteColorPalette.accent}]`
                                      : review.status === 'denied' || review.status === 'declined'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {review.status === 'denied' ? 'declined' : review.status}
                                </span>
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {review.supervisorName ? (
                                  <div className="flex items-center">
                                    <FaUserMd className={`h-3 w-3 sm:h-4 sm:w-4 text-[${websiteColorPalette.secondary}] mr-2`} />
                                    {review.supervisorName}
                                  </div>
                                ) : (
                                  <span className="text-gray-400">Not assigned</span>
                                )}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {review.createdAt ? new Date(review.createdAt).toLocaleDateString() : 'N/A'}
                              </td>
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
            {selectedReview && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm"
                onClick={(e) => {
                  if (e.target === e.currentTarget) setSelectedReview(null);
                }}
              >
                <motion.div
                  initial={{ scale: 0.9, opacity: 0, y: 20 }}
                  animate={{ scale: 1, opacity: 1, y: 0 }}
                  exit={{ scale: 0.9, opacity: 0, y: 20 }}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  className="bg-white rounded-xl shadow-lg p-0 max-w-4xl w-full mx-4 overflow-hidden border border-gray-200"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className={`bg-gradient-to-r from-[${websiteColorPalette.primary}] to-[${websiteColorPalette.secondary}] p-5 text-white`}>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <FaStar className="h-6 w-6 mr-3 text-yellow-300" />
                        <h3 className="text-xl font-bold">Review Details</h3>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                          selectedReview.status === 'accepted'
                            ? 'bg-green-100 text-green-800'
                            : selectedReview.status === 'denied' || selectedReview.status === 'declined'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {selectedReview.status === 'accepted' ? 'Accepted' :
                           selectedReview.status === 'denied' ? 'Declined' :
                           selectedReview.status === 'declined' ? 'Declined' :
                           selectedReview.status || 'Pending'}
                        </span>
                        <button
                          onClick={() => setSelectedReview(null)}
                          className={`text-white hover:text-blue-200 transition-colors bg-[${websiteColorPalette.primary}] hover:bg-blue-600 rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-opacity-50`}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="p-6 max-h-[80vh] overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                          <FaUserGraduate className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Student Information
                        </h4>
                        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`}>
                          <div className="flex items-center">
                            <span className="w-24 text-sm font-medium text-gray-600">Name:</span>
                            <span className="text-gray-900 font-medium">{selectedReview.studentName}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="w-24 text-sm font-medium text-gray-600">ID:</span>
                            <span className="text-gray-900">
                              {typeof selectedReview.studentId === 'object'
                                ? (selectedReview.studentId.studentId || selectedReview.studentId._id || 'N/A')
                                : (selectedReview.studentId || 'N/A')}
                            </span>
                          </div>
                          {(() => {
                            const studentDetails = getStudentDetails(selectedReview.studentId);
                            return studentDetails ? (
                              <div className="flex items-center">
                                <span className="w-24 text-sm font-medium text-gray-600">University:</span>
                                <span className="text-gray-900">{studentDetails.university || 'N/A'}</span>
                              </div>
                            ) : null;
                          })()}
                        </div>
                      </div>

                      <div>
                        <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                          <FaUserMd className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Supervisor Information
                        </h4>
                        <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-3`}>
                          {selectedReview.supervisorName ? (
                            <>
                              <div className="flex items-center">
                                <span className="w-24 text-sm font-medium text-gray-600">Name:</span>
                                <span className="text-gray-900 font-medium">{selectedReview.supervisorName}</span>
                              </div>
                              <div className="flex items-center">
                                <span className="w-24 text-sm font-medium text-gray-600">Reviewed:</span>
                                <span className="text-gray-900">{selectedReview.reviewedDate ?
                                  new Date(selectedReview.reviewedDate).toLocaleString() : 'Not yet reviewed'}</span>
                              </div>
                              {(() => {
                                const supervisorDetails = getSupervisorDetails(selectedReview.supervisorId);
                                return supervisorDetails ? (
                                  <div className="flex items-center">
                                    <span className="w-24 text-sm font-medium text-gray-600">University:</span>
                                    <span className="text-gray-900">{supervisorDetails.university || 'N/A'}</span>
                                  </div>
                                ) : null;
                              })()}
                            </>
                          ) : (
                            <p className="text-gray-500 italic">No supervisor assigned yet</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Patient Information */}
                    <div className="mb-6">
                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                        <FaUserMd className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Patient Information
                      </h4>
                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500">Name</h4>
                            <p className="text-sm text-gray-900 mt-1">
                              {typeof selectedReview.patientId === 'object' && selectedReview.patientId?.fullName
                                ? selectedReview.patientId.fullName
                                : (typeof selectedReview.patientId === 'string'
                                    ? selectedReview.patientId
                                    : 'Unknown')}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                            <p className="text-sm text-gray-900 mt-1">
                              {typeof selectedReview.patientId === 'object' && selectedReview.patientId?.nationalId
                                ? selectedReview.patientId.nationalId
                                : 'N/A'}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Procedure Details */}
                    <div className="mb-6">
                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                        <FaClipboardList className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Procedure Details
                      </h4>
                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-500">Procedure Type</h4>
                            <p className="text-sm text-gray-900 mt-1 font-medium">{selectedReview.procedureType}</p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-500">Status</h4>
                            <p className="text-sm mt-1">
                              <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                selectedReview.status === 'accepted'
                                  ? 'bg-green-100 text-green-800'
                                  : selectedReview.status === 'denied' || selectedReview.status === 'declined'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {selectedReview.status === 'denied' ? 'Declined' :
                                 selectedReview.status === 'accepted' ? 'Accepted' :
                                 selectedReview.status === 'pending' ? 'Pending' :
                                 selectedReview.status}
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                        <FaStar className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Ratings
                      </h4>
                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <p className="font-medium mb-3 text-gray-700">Procedure Quality</p>
                            <div className="flex items-center">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <FaStar
                                  key={star}
                                  className={`h-6 w-6 ${
                                    star <= selectedReview.procedureQuality
                                      ? 'text-yellow-400'
                                      : 'text-gray-200'
                                  }`}
                                />
                              ))}
                              <span className="ml-3 text-gray-700 font-bold">{selectedReview.procedureQuality}/5</span>
                            </div>
                          </div>
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <p className="font-medium mb-3 text-gray-700">Patient Interaction</p>
                            <div className="flex items-center">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <FaStar
                                  key={star}
                                  className={`h-6 w-6 ${
                                    star <= selectedReview.patientInteraction
                                      ? 'text-yellow-400'
                                      : 'text-gray-200'
                                  }`}
                                />
                              ))}
                              <span className="ml-3 text-gray-700 font-bold">{selectedReview.patientInteraction}/5</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Comments Section */}
                    <div className="mb-6">
                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 text-[${websiteColorPalette.primary}]`} viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
                        </svg>
                        Comments
                      </h4>
                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100 space-y-5`}>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <p className="font-medium text-gray-700 mb-2 flex items-center">
                            <FaUserGraduate className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Student Comment:
                          </p>
                          <p className="mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100">
                            {selectedReview.comment || 'No comment provided'}
                          </p>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <p className="font-medium text-gray-700 mb-2 flex items-center">
                            <FaUserMd className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                            Supervisor Note:
                          </p>
                          <p className="mt-1 text-gray-600 bg-gray-50 p-3 rounded-md border border-gray-100">
                            {selectedReview.note || 'No note provided'}
                          </p>
                        </div>

                        {/* Supervisor Signature */}
                        {selectedReview.supervisorSignature && (
                          <div className="bg-white p-4 rounded-lg shadow-sm">
                            <p className="font-medium text-gray-700 mb-2 flex items-center">
                              <FaUserMd className={`h-4 w-4 mr-2 text-[${websiteColorPalette.primary}]`} />
                              Supervisor Signature:
                            </p>
                            <div className="mt-1 bg-gray-50 p-3 rounded-md border border-gray-100 flex justify-center">
                              {selectedReview.supervisorSignature.startsWith('data:image') ? (
                                <img
                                  src={selectedReview.supervisorSignature}
                                  alt="Supervisor Signature"
                                  className="max-h-20"
                                />
                              ) : (
                                <p className="font-signature text-lg text-gray-700">
                                  {selectedReview.supervisorSignature}
                                </p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Review Steps Section */}
                    <div className="mb-6">
                      <h4 className={`text-lg font-semibold text-[${websiteColorPalette.primary}] mb-3 flex items-center`}>
                        <FaClipboardList className={`mr-2 text-[${websiteColorPalette.primary}]`} /> Procedure Steps
                      </h4>
                      <div className={`bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100`}>
                        {selectedReview.reviewSteps && Array.isArray(selectedReview.reviewSteps) && selectedReview.reviewSteps.length > 0 ? (
                          <div className="bg-white rounded-lg overflow-hidden">
                            <div className="divide-y divide-gray-200">
                              {selectedReview.reviewSteps.map((step, index) => {
                                // Ensure step is an object
                                const safeStep = step || { completed: false };
                                return (
                                  <div
                                    key={index}
                                    className={`p-3 flex items-center justify-between ${
                                      safeStep.completed ? 'bg-green-50' : 'bg-white'
                                    }`}
                                  >
                                    <div className="flex items-center">
                                      <span className={`inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`}>{index + 1}.</span>
                                      <span className="text-sm font-medium text-gray-900">
                                        {safeStep.description || safeStep.name || `Step ${index + 1}`}
                                      </span>
                                    </div>
                                    <div>
                                      {safeStep.completed ? (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                          <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                          </svg>
                                          Completed
                                        </span>
                                      ) : (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                          <svg className="w-4 h-4 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                                          </svg>
                                          Not completed
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : selectedReview.steps && Array.isArray(selectedReview.steps) && selectedReview.steps.length > 0 ? (
                          <div className="bg-white rounded-lg overflow-hidden">
                            <div className="divide-y divide-gray-200">
                              {selectedReview.steps.map((step, index) => {
                                // Ensure step is an object
                                const safeStep = step || { completed: false };
                                return (
                                  <div
                                    key={index}
                                    className={`p-3 flex items-center justify-between ${
                                      safeStep.completed ? 'bg-green-50' : 'bg-white'
                                    }`}
                                  >
                                    <div className="flex items-center">
                                      <span className={`inline-block w-6 text-center mr-2 text-[${websiteColorPalette.primary}] font-bold`}>{index + 1}.</span>
                                      <span className="text-sm font-medium text-gray-900">
                                        {safeStep.description || safeStep.name || `Step ${index + 1}`}
                                      </span>
                                    </div>
                                    <div>
                                      {safeStep.completed ? (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                          <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                          </svg>
                                          Completed
                                        </span>
                                      ) : (
                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                          <svg className="w-4 h-4 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                                          </svg>
                                          Not completed
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <p className="text-gray-500 italic text-center py-4 bg-white rounded-lg">No procedure steps available</p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 p-5 flex justify-between items-center bg-gray-50">
                    <div className="text-sm text-gray-500">
                      <span className="font-medium">Submitted:</span> {new Date(selectedReview.submittedDate).toLocaleString()}
                      {selectedReview.reviewedDate && (
                        <span className="ml-4">
                          <span className="font-medium">Reviewed:</span> {new Date(selectedReview.reviewedDate).toLocaleString()}
                        </span>
                      )}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={() => setSelectedReview(null)}
                      className={`px-5 py-2.5 bg-[${websiteColorPalette.primary}] text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm flex items-center font-medium`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      Close
                    </motion.button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Reviews;