import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';
import { FaTooth, FaXRay, FaChartLine, FaUniversity, FaCalendarAlt } from 'react-icons/fa';
import { MdOutlineSupervisorAccount, MdSchool } from 'react-icons/md';

import { RiAiGenerate } from 'react-icons/ri';

const UniversityServices = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) return <Loader />;

  return (
    <div className="font-sans text-gray-800 bg-white min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-28 text-center relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-6 leading-tight"
          >
            {t('universityServices.title')}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
              {t('universityServices.withAI')}
            </span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-xl text-[#333333] max-w-3xl mx-auto mb-8"
          >
            {t('universityServices.subtitle')}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/try-ai')}
              className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-4 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg hover:shadow-xl flex items-center"
            >
              <RiAiGenerate className="mr-2" />
              {t('universityServices.tryAI')}
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/contact')}
              className="border-2 border-[#20B2AA] text-[#20B2AA] px-8 py-4 rounded-full font-medium hover:bg-[rgba(32,178,170,0.1)] transition-all duration-300 flex items-center"
            >
              <FaUniversity className="mr-2" />
              {t('universityServices.contactUs')}
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('universityServices.featuresTitle')}
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">
              {t('universityServices.featuresDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <FaXRay className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.aiXray'),
                desc: t('universityServices.aiXrayDesc')
              },
              {
                icon: <MdSchool className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.studentWorkflow'),
                desc: t('universityServices.studentWorkflowDesc')
              },
              {
                icon: <MdOutlineSupervisorAccount className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.supervisorReview'),
                desc: t('universityServices.supervisorReviewDesc')
              },
              {
                icon: <FaTooth className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.digitalCharting'),
                desc: t('universityServices.digitalChartingDesc')
              },
              {
                icon: <FaChartLine className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.analytics'),
                desc: t('universityServices.analyticsDesc')
              },
              {
                icon: <FaCalendarAlt className="h-6 w-6 text-[#0077B6]" />,
                title: t('universityServices.appointments'),
                desc: t('universityServices.appointmentsDesc')
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#20B2AA] group"
              >
                <div className="bg-[rgba(0,119,182,0.1)] w-14 h-14 rounded-lg flex items-center justify-center mb-6 group-hover:bg-[rgba(32,178,170,0.1)] transition-colors duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-[#0077B6] mb-3">{feature.title}</h3>
                <p className="text-[#333333]">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gradient-to-b from-[rgba(0,119,182,0.05)] to-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">
              {t('universityServices.benefitsTitle')}
            </h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">
              {t('universityServices.benefitsDescription')}
            </p>
          </motion.div>

          <div className="flex flex-col lg:flex-row items-center gap-12">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <ul className="space-y-6">
                {[
                  {
                    title: t('universityServices.improvedLearning'),
                    desc: t('universityServices.improvedLearningDesc'),
                    icon: <MdSchool className="h-6 w-6 text-[#0077B6]" />
                  },
                  {
                    title: t('universityServices.realTimeFeedback'),
                    desc: t('universityServices.realTimeFeedbackDesc'),
                    icon: <RiAiGenerate className="h-6 w-6 text-[#0077B6]" />
                  },
                  {
                    title: t('universityServices.efficientSupervision'),
                    desc: t('universityServices.efficientSupervisionDesc'),
                    icon: <MdOutlineSupervisorAccount className="h-6 w-6 text-[#0077B6]" />
                  }
                ].map((benefit, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start"
                  >
                    <div className="bg-[rgba(0,119,182,0.1)] rounded-lg p-3 mr-4 flex-shrink-0">
                      {benefit.icon}
                    </div>
                    <div>
                      <h4 className="font-bold text-[#0077B6]">{benefit.title}</h4>
                      <p className="text-[#333333]">{benefit.desc}</p>
                    </div>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:w-1/2 relative"
            >
             
                <div>
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl blur opacity-25 group-hover:opacity-50 transition duration-200"></div>
                    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden border border-[rgba(32,178,170,0.3)]">
                      <div className="workflow-image">
                        <img
                          src="/imgs/dash.jpg"
                          alt={t('howItWorks.forStudents')}
                          loading="lazy"
                        />
                      </div>
                    </div>
                  </div>
                </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#0077B6] to-[#20B2AA] text-white">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            {t('universityServices.ctaTitle')}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-xl text-white text-opacity-90 max-w-3xl mx-auto mb-8"
          >
            {t('universityServices.ctaDescription')}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/try-ai')}
              className="bg-white text-[#0077B6] px-8 py-4 rounded-full font-bold hover:bg-opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
            >
              <RiAiGenerate className="mr-2" />
              {t('universityServices.tryAI')}
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate('/contact')}
              className="border-2 border-white text-white px-8 py-4 rounded-full font-bold hover:bg-white hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center"
            >
              <FaUniversity className="mr-2" />
              {t('universityServices.contactUs')}
            </motion.button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default UniversityServices;