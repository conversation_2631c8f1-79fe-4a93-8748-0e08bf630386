const role = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    // If the user is a student trying to create a patient, deny access
    if (req.user.role === 'student' &&
        req.method === 'POST' &&
        req.originalUrl === '/api/patients' &&
        !allowedRoles.includes('assistant')) {
      return res.status(403).json({
        message: 'Students cannot directly add patients. Please use the procedure request feature instead.'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied: Insufficient role permissions' });
    }

    next();
  };
};

module.exports = role;