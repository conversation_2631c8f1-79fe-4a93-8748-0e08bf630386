import { useState } from 'react';
import { motion } from 'framer-motion';
import MedicalTab from './MedicalTab';
import { generateSheetPDF } from '../utils/pdfUtils';

const RemovableProsthodonticsSheet = ({ initialData, onSave }) => {
  const defaultFormData = {
    dentalHistory: {
      historyOfDentalExtraction: {
        pulpal: '',
        perio: '',
        trauma: '',
        congenital: ''
      },
      durationOfEdentulism: {
        maxilla: '',
        mandible: ''
      },
      previousDenture: {
        maxillary: '',
        mandibular: ''
      },
      experienceOfPreviousDenture: ''
    },
    extraOralExamination: {
      facialForm: '',
      muscleTone: '',
      lips: '',
      tmj: ''
    },
    intraOralExamination: {
      archForm: {
        maxillary: '',
        mandibular: ''
      },
      interArchSpace: '',
      ridgeParallelism: '',
      softPalate: '',
      mucosa: {
        health: ''
      },
      tongueSize: '',
      tonguePosition: '',
      muscularAttachment: '',
      saliva: {
        quantity: '',
        quality: ''
      }
    }
  };

  const mergedData = initialData && Object.keys(initialData).length > 0
    ? { ...defaultFormData, ...initialData }
    : defaultFormData;

  const [formData, setFormData] = useState(mergedData);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('medical');

  // Modified validation to make fields optional
  const validateForm = () => {
    // Since all fields are now optional, we're just returning true
    // You can add custom validation logic here if needed for specific fields
    return true;
  };

  const handleChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${field}`]: '' }));
  };

  const handleNestedChange = (section, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${field}`]: '' }));
  };

  const handleDeepNestedChange = (section, parent, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [parent]: {
          ...prev[section][parent],
          [subsection]: {
            ...prev[section][parent][subsection],
            [field]: value
          }
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${parent}.${subsection}.${field}`]: '' }));
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      alert('Please fill all required fields.');
      return;
    }

    const diagnosis = `Removable Prosthodontics: ${formData.intraOralExamination.archForm.maxillary} maxillary arch, ${formData.intraOralExamination.archForm.mandibular} mandibular arch`;

    const treatmentPlan = `Removable Prosthodontics treatment plan: ${
      formData.dentalHistory.previousDenture.maxillary === 'CD' ? 'Complete Denture' :
      formData.dentalHistory.previousDenture.maxillary === 'RPD' ? 'Removable Partial Denture' :
      'Not specified'
    } for maxilla, ${
      formData.dentalHistory.previousDenture.mandibular === 'CD' ? 'Complete Denture' :
      formData.dentalHistory.previousDenture.mandibular === 'RPD' ? 'Removable Partial Denture' :
      'Not specified'
    } for mandible`;

    if (onSave) {
      const success = await onSave(formData, diagnosis, treatmentPlan, formData.dentalHistory.experienceOfPreviousDenture || '');
      if (!success) {
        alert('Failed to save sheet. Please try again.');
      }
    } else {
      alert('Form submitted successfully!');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      const diagnosis = `Removable Prosthodontics: ${formData.intraOralExamination.archForm.maxillary} maxillary arch, ${formData.intraOralExamination.archForm.mandibular} mandibular arch`;

      const treatmentPlan = `Removable Prosthodontics treatment plan: ${
        formData.dentalHistory.previousDenture.maxillary === 'CD' ? 'Complete Denture' :
        formData.dentalHistory.previousDenture.maxillary === 'RPD' ? 'Removable Partial Denture' :
        'Not specified'
      } for maxilla, ${
        formData.dentalHistory.previousDenture.mandibular === 'CD' ? 'Complete Denture' :
        formData.dentalHistory.previousDenture.mandibular === 'RPD' ? 'Removable Partial Denture' :
        'Not specified'
      } for mandible`;

      // Create a mock sheet object for PDF generation
      const mockSheet = {
        type: 'Removable Prosthodontics',
        createdAt: new Date().toISOString(),
        details: {
          diagnosis,
          treatmentPlan,
          notes: formData.dentalHistory.experienceOfPreviousDenture || '',
          specificData: formData
        }
      };

      await generateSheetPDF(mockSheet, 'Current_Patient');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const renderRadioButtons = (label, value, onChange, options, error, required = false) => (
    <div className="mb-5">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {options.map((option) => (
          <div
            key={option}
            className={`flex items-center p-2 rounded-lg border transition-all ${
              value === option
                ? 'bg-[rgba(40,167,69,0.1)] border-[#28A745]'
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
          >
            <input
              type="radio"
              id={`${label.replace(/\s+/g, '')}-${option}`}
              name={label.replace(/\s+/g, '')}
              value={option}
              checked={value === option}
              onChange={onChange}
              className={`h-4 w-4 ${value === option ? 'text-[#28A745] focus:ring-[#28A745]' : 'text-[#0077B6] focus:ring-[#0077B6]'} border-gray-300`}
            />
            <label
              htmlFor={`${label.replace(/\s+/g, '')}-${option}`}
              className={`ml-2 text-sm cursor-pointer w-full ${value === option ? 'font-medium text-[#28A745]' : 'text-gray-700'}`}
            >
              {option}
            </label>
          </div>
        ))}
      </div>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  const renderInput = (label, value, onChange, error, required = false) => (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type="text"
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all ${error ? 'border-red-500' : 'border-gray-300'}`}
      />
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto mt-8 p-6 bg-white rounded-xl shadow-md border border-[rgba(0,119,182,0.1)]"
    >
      <h3 className="text-2xl font-bold text-[#0077B6] mb-6">Removable Prosthodontics Sheet</h3>

      {/* Tabs */}
      <div className="flex border-b border-[rgba(0,119,182,0.1)] mb-6 overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'medical' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('medical')}
        >
          Medical
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'dentalHistory' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('dentalHistory')}
        >
          Dental History
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'extraOralExamination' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('extraOralExamination')}
        >
          Extra-Oral Examination
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'intraOralExamination' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('intraOralExamination')}
        >
          Intra-Oral Examination
        </button>
      </div>

      {activeTab === 'medical' && (
        <div>
          <MedicalTab onSave={(medicalInfo) => {
            console.log('Medical info updated:', medicalInfo);
            // You can update the form data here if needed
          }} />
        </div>
      )}

      {activeTab === 'dentalHistory' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Dental History</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h5 className="text-base font-medium text-gray-600 mb-2">History of Dental Extraction</h5>
                {renderRadioButtons(
                  'Pulpal',
                  formData.dentalHistory.historyOfDentalExtraction.pulpal,
                  (e) => handleNestedChange('dentalHistory', 'historyOfDentalExtraction', 'pulpal', e.target.value),
                  ['Yes', 'No'],
                  errors['dentalHistory.historyOfDentalExtraction.pulpal']
                )}
                {renderRadioButtons(
                  'Perio',
                  formData.dentalHistory.historyOfDentalExtraction.perio,
                  (e) => handleNestedChange('dentalHistory', 'historyOfDentalExtraction', 'perio', e.target.value),
                  ['Yes', 'No'],
                  errors['dentalHistory.historyOfDentalExtraction.perio']
                )}
                {renderRadioButtons(
                  'Trauma',
                  formData.dentalHistory.historyOfDentalExtraction.trauma,
                  (e) => handleNestedChange('dentalHistory', 'historyOfDentalExtraction', 'trauma', e.target.value),
                  ['Yes', 'No'],
                  errors['dentalHistory.historyOfDentalExtraction.trauma']
                )}
                {renderRadioButtons(
                  'Congenital',
                  formData.dentalHistory.historyOfDentalExtraction.congenital,
                  (e) => handleNestedChange('dentalHistory', 'historyOfDentalExtraction', 'congenital', e.target.value),
                  ['Yes', 'No'],
                  errors['dentalHistory.historyOfDentalExtraction.congenital']
                )}
              </div>
              <div>
                <h5 className="text-base font-medium text-gray-600 mb-2">Duration of Edentulism</h5>
                {renderInput(
                  'Maxilla',
                  formData.dentalHistory.durationOfEdentulism.maxilla,
                  (e) => handleNestedChange('dentalHistory', 'durationOfEdentulism', 'maxilla', e.target.value),
                  errors['dentalHistory.durationOfEdentulism.maxilla']
                )}
                {renderInput(
                  'Mandible',
                  formData.dentalHistory.durationOfEdentulism.mandible,
                  (e) => handleNestedChange('dentalHistory', 'durationOfEdentulism', 'mandible', e.target.value),
                  errors['dentalHistory.durationOfEdentulism.mandible']
                )}
              </div>
            </div>
            <div className="mt-4">
              <h5 className="text-base font-medium text-gray-600 mb-2">Previous Denture</h5>
              {renderRadioButtons(
                'Maxillary',
                formData.dentalHistory.previousDenture.maxillary,
                (e) => handleNestedChange('dentalHistory', 'previousDenture', 'maxillary', e.target.value),
                ['CD', 'RPD'],
                errors['dentalHistory.previousDenture.maxillary']
              )}
              {renderRadioButtons(
                'Mandibular',
                formData.dentalHistory.previousDenture.mandibular,
                (e) => handleNestedChange('dentalHistory', 'previousDenture', 'mandibular', e.target.value),
                ['CD', 'RPD'],
                errors['dentalHistory.previousDenture.mandibular']
              )}
              {renderInput(
                'Experience of Previous Denture',
                formData.dentalHistory.experienceOfPreviousDenture,
                (e) => handleChange('dentalHistory', 'experienceOfPreviousDenture', e.target.value),
                errors['dentalHistory.experienceOfPreviousDenture']
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'extraOralExamination' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-md font-semibold text-[#0077B6] mb-3">Extra-Oral Examination</h4>
            {renderRadioButtons(
              'Facial Form',
              formData.extraOralExamination.facialForm,
              (e) => handleChange('extraOralExamination', 'facialForm', e.target.value),
              ['Square', 'Tapering', 'Ovoid'],
              errors['extraOralExamination.facialForm']
            )}
            {renderRadioButtons(
              'Muscle Tone',
              formData.extraOralExamination.muscleTone,
              (e) => handleChange('extraOralExamination', 'muscleTone', e.target.value),
              ['Normal', 'Flaccid', 'Tense'],
              errors['extraOralExamination.muscleTone']
            )}
            {renderRadioButtons(
              'Lips',
              formData.extraOralExamination.lips,
              (e) => handleChange('extraOralExamination', 'lips', e.target.value),
              ['Normal', 'Long', 'Short'],
              errors['extraOralExamination.lips']
            )}
            {renderRadioButtons(
              'Temporomandibular Joint',
              formData.extraOralExamination.tmj,
              (e) => handleChange('extraOralExamination', 'tmj', e.target.value),
              ['Normal', 'Clicking', 'Tenderness'],
              errors['extraOralExamination.tmj']
            )}
          </div>
        </div>
      )}

      {activeTab === 'intraOralExamination' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Intra-Oral Examination</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h5 className="text-base font-medium text-gray-600 mb-2">Arch Form</h5>
                {renderRadioButtons(
                  'Maxillary',
                  formData.intraOralExamination.archForm.maxillary,
                  (e) => handleNestedChange('intraOralExamination', 'archForm', 'maxillary', e.target.value),
                  ['Square', 'Tapering', 'Ovoid'],
                  errors['intraOralExamination.archForm.maxillary']
                )}
                {renderRadioButtons(
                  'Mandibular',
                  formData.intraOralExamination.archForm.mandibular,
                  (e) => handleNestedChange('intraOralExamination', 'archForm', 'mandibular', e.target.value),
                  ['Square', 'Tapering', 'Ovoid'],
                  errors['intraOralExamination.archForm.mandibular']
                )}
              </div>
              <div>
                {renderRadioButtons(
                  'Inter Arch Space',
                  formData.intraOralExamination.interArchSpace,
                  (e) => handleChange('intraOralExamination', 'interArchSpace', e.target.value),
                  ['Enough', 'Limited'],
                  errors['intraOralExamination.interArchSpace']
                )}
                {renderRadioButtons(
                  'Ridge Parallelism',
                  formData.intraOralExamination.ridgeParallelism,
                  (e) => handleChange('intraOralExamination', 'ridgeParallelism', e.target.value),
                  ['Parallel', 'Irregular'],
                  errors['intraOralExamination.ridgeParallelism']
                )}
                {renderRadioButtons(
                  'Soft Palate',
                  formData.intraOralExamination.softPalate,
                  (e) => handleChange('intraOralExamination', 'softPalate', e.target.value),
                  ['Class I', 'Class II', 'Class III'],
                  errors['intraOralExamination.softPalate']
                )}
              </div>
            </div>
            <div className="mt-4">
              <h5 className="text-base font-medium text-gray-600 mb-2">Mucosa</h5>
              {renderRadioButtons(
                'Health',
                formData.intraOralExamination.mucosa.health,
                (e) => handleNestedChange('intraOralExamination', 'mucosa', 'health', e.target.value),
                ['Healthy', 'Inflamed', 'Hyperplastic'],
                errors['intraOralExamination.mucosa.health']
              )}
              {renderRadioButtons(
                'Tongue Size',
                formData.intraOralExamination.tongueSize,
                (e) => handleChange('intraOralExamination', 'tongueSize', e.target.value),
                ['Normal', 'Enlarged'],
                errors['intraOralExamination.tongueSize']
              )}
              {renderRadioButtons(
                'Tongue Position',
                formData.intraOralExamination.tonguePosition,
                (e) => handleChange('intraOralExamination', 'tonguePosition', e.target.value),
                ['Normal', 'Retruded'],
                errors['intraOralExamination.tonguePosition']
              )}
              {renderRadioButtons(
                'Muscular Attachment',
                formData.intraOralExamination.muscularAttachment,
                (e) => handleChange('intraOralExamination', 'muscularAttachment', e.target.value),
                ['Normal attached mucosa', 'Loss of anterior attached mucosa', 'Loss of posterior attached mucosa', 'Loss of anterior and posterior attached mucosa'],
                errors['intraOralExamination.muscularAttachment']
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h6 className="text-base font-medium text-gray-600 mb-2">Saliva - Quantity</h6>
                  {renderRadioButtons(
                    'Quantity',
                    formData.intraOralExamination.saliva.quantity,
                    (e) => handleNestedChange('intraOralExamination', 'saliva', 'quantity', e.target.value),
                    ['Adequate', 'Inadequate'],
                    errors['intraOralExamination.saliva.quantity']
                  )}
                </div>
                <div>
                  <h6 className="text-base font-medium text-gray-600 mb-2">Saliva - Quality</h6>
                  {renderRadioButtons(
                    'Quality',
                    formData.intraOralExamination.saliva.quality,
                    (e) => handleNestedChange('intraOralExamination', 'saliva', 'quality', e.target.value),
                    ['Serous', 'Mucous'],
                    errors['intraOralExamination.saliva.quality']
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 flex justify-end gap-4">
        <button
          onClick={handleDownloadPDF}
          className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download PDF
        </button>
        <button
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#0077B6] text-white font-medium rounded-lg hover:bg-[#005f92] transition-all shadow-sm"
        >
          Save Sheet
        </button>
      </div>
    </motion.div>
  );
};

export default RemovableProsthodonticsSheet;