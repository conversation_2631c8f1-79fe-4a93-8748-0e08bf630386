import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaNewspaper } from 'react-icons/fa';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import { useAuth } from '../context/AuthContext';
import SuperAdminSidebar from './SuperAdminSidebar';

const News = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [news, setNews] = useState([]);
  const [universities, setUniversities] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [showGeneralModal, setShowGeneralModal] = useState(false);
  const [showSpecificModal, setShowSpecificModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  const [generalNewsForm, setGeneralNewsForm] = useState({
    title: { en: '', ar: '' },
    content: { en: '', ar: '' },
  });

  const [specificNewsForm, setSpecificNewsForm] = useState({
    title: { en: '', ar: '' },
    content: { en: '', ar: '' },
    recipientType: '',
    recipientId: '',
  });

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !token) {
        setError('Please log in to view news.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const [newsRes, universitiesRes, accountsRes] = await Promise.all([
          axios.get(`${process.env.REACT_APP_API_URL}/api/news`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/accounts`, config),
        ]);

        setNews(newsRes.data || []);
        setUniversities(universitiesRes.data || []);
        setAccounts(accountsRes.data || []);
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage = err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load data';
        setError(errorMessage);
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate]);

  const handleGeneralNewsSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('http://localhost:5000/api/news', generalNewsForm, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setNews([...news, response.data]);
      setShowGeneralModal(false);
      setGeneralNewsForm({ title: { en: '', ar: '' }, content: { en: '', ar: '' } });
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to send general news');
    }
  };

  const handleSpecificNewsSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('http://localhost:5000/api/news/specific', {
        ...specificNewsForm,
        recipientType: specificNewsForm.recipientType,
        recipientId: specificNewsForm.recipientId,
      }, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setNews([...news, response.data]);
      setShowSpecificModal(false);
      setSpecificNewsForm({ title: { en: '', ar: '' }, content: { en: '', ar: '' }, recipientType: '', recipientId: '' });
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to send specific news');
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">News & Announcements</h1>
                  <p className="text-[#333333]">Send announcements to universities and users</p>
                </div>
                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowGeneralModal(true)}
                    className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                  >
                    <FaNewspaper className="h-5 w-5 mr-2" />
                    Send General News
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowSpecificModal(true)}
                    className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                  >
                    <FaNewspaper className="h-5 w-5 mr-2" />
                    Send Specific News
                  </motion.button>
                </div>
              </div>

              <motion.div 
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-6">
                  <h2 className="text-xl font-bold text-[#0077B6] mb-6">Sent Announcements</h2>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title (EN)</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title (AR)</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {news.length === 0 ? (
                          <tr>
                            <td colSpan="4" className="px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <FaNewspaper className="h-12 w-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900">No news sent</h3>
                                <p className="mt-1 text-gray-500">Send a new announcement to get started.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          news.map((item) => (
                            <motion.tr 
                              key={item._id} 
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              className="hover:bg-gray-50"
                            >
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.title.en}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.title.ar}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {item.recipientType && item.recipientId 
                                  ? `${item.recipientType}: ${item.recipientId}` 
                                  : 'General'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(item.createdAt).toLocaleDateString('en-US', {
                                  weekday: 'short', month: 'short', day: 'numeric'
                                })}
                              </td>
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {showGeneralModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">Send General News</h2>
                <button onClick={() => setShowGeneralModal(false)} className="text-gray-400 hover:text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <form onSubmit={handleGeneralNewsSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title (English)*</label>
                  <input 
                    type="text" 
                    value={generalNewsForm.title.en} 
                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, title: { ...generalNewsForm.title, en: e.target.value } })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title (Arabic)*</label>
                  <input 
                    type="text" 
                    value={generalNewsForm.title.ar} 
                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, title: { ...generalNewsForm.title, ar: e.target.value } })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Content (English)*</label>
                  <textarea 
                    value={generalNewsForm.content.en} 
                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, content: { ...generalNewsForm.content, en: e.target.value } })} 
                    rows="4" 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Content (Arabic)*</label>
                  <textarea 
                    value={generalNewsForm.content.ar} 
                    onChange={(e) => setGeneralNewsForm({ ...generalNewsForm, content: { ...generalNewsForm.content, ar: e.target.value } })} 
                    rows="4" 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div className="flex justify-end space-x-4 pt-4">
                  <motion.button 
                    type="button" 
                    onClick={() => setShowGeneralModal(false)} 
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button 
                    type="submit" 
                    className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Send News
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}

      {showSpecificModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">Send Specific News</h2>
                <button onClick={() => setShowSpecificModal(false)} className="text-gray-400 hover:text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <form onSubmit={handleSpecificNewsSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title (English)*</label>
                  <input 
                    type="text" 
                    value={specificNewsForm.title.en} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, title: { ...specificNewsForm.title, en: e.target.value } })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Title (Arabic)*</label>
                  <input 
                    type="text" 
                    value={specificNewsForm.title.ar} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, title: { ...specificNewsForm.title, ar: e.target.value } })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Content (English)*</label>
                  <textarea 
                    value={specificNewsForm.content.en} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, content: { ...specificNewsForm.content, en: e.target.value } })} 
                    rows="4" 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Content (Arabic)*</label>
                  <textarea 
                    value={specificNewsForm.content.ar} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, content: { ...specificNewsForm.content, ar: e.target.value } })} 
                    rows="4" 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Type*</label>
                  <select 
                    value={specificNewsForm.recipientType} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, recipientType: e.target.value, recipientId: '' })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                  >
                    <option value="">Select Recipient Type</option>
                    <option value="account">Account</option>
                    <option value="university">University</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipient*</label>
                  <select 
                    value={specificNewsForm.recipientId} 
                    onChange={(e) => setSpecificNewsForm({ ...specificNewsForm, recipientId: e.target.value })} 
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    required
                    disabled={!specificNewsForm.recipientType}
                  >
                    <option value="">Select Recipient</option>
                    {specificNewsForm.recipientType === 'account' && accounts.map((account) => (
                      <option key={account.email} value={account.email}>{account.email}</option>
                    ))}
                    {specificNewsForm.recipientType === 'university' && universities.map((university) => (
                      <option key={university.universityId} value={university.universityId}>{university.name.en}</option>
                    ))}
                  </select>
                </div>
                <div className="flex justify-end space-x-4 pt-4">
                  <motion.button 
                    type="button" 
                    onClick={() => setShowSpecificModal(false)} 
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button 
                    type="submit" 
                    className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg font-medium transition-colors shadow-md hover:shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Send News
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default News;