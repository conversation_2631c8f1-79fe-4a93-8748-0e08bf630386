const mongoose = require('mongoose');
const University = require('./models/University'); // Adjust path to your University model
const config = require('./config/config');

// MongoDB connection using environment variables
const connectDB = async () => {
  try {
    await mongoose.connect(config.MONGO_URI, {
      dbName: 'dentlyzer', // Database name
    });
    console.log('✅ MongoDB connected');
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to generate time slots
const generateTimeSlots = () => {
  const slots = [];
  const startDate = new Date('2025-05-01T00:00:00Z');
  const hours = ['09:00', '11:30', '13:30']; // Adjusted for 120-minute slots (9 AM to 3 PM)

  hours.forEach((time) => {
    // Generate 5 slots per time period
    for (let i = 0; i < 5; i++) {
      slots.push({
        date: new Date(startDate),
        time: time,
        isAvailable: true,
        duration: 120, // 2 hours as specified
      });
    }
  });

  return slots;
};

// AIU data with bilingual fields and updated schema
const aiuData = {
  universityId: 'AIU',
  name: {
    en: 'Alamein International University',
    ar: 'جامعة العلمين الدولية',
  },
  description: {
    en: 'Alamein International University (AIU) is an Egyptian national, non-profit university inaugurated in 2020 in New Alamein City, Matrouh Governorate, Egypt. It offers over 44 programs across 13 fields, including dentistry, engineering, and public health sciences, with a focus on high-quality education, research, and international partnerships.',
    ar: 'جامعة العلمين الدولية (AIU) هي جامعة مصرية وطنية غير هادفة للربح، تم افتتاحها في عام 2020 في مدينة العلمين الجديدة، محافظة مطروح، مصر. تقدم أكثر من 44 برنامجًا في 13 مجالًا، بما في ذلك طب الأسنان، الهندسة، وعلوم الصحة العامة، مع التركيز على التعليم عالي الجودة، البحث، والشراكات الدولية.',
  },
  dentistryInfo: {
    en: 'The Faculty of Dentistry at AIU provides cutting-edge dental education and clinical training, equipped with modern clinics and research facilities.',
    ar: 'كلية طب الأسنان في جامعة العلمين الدولية تقدم تعليمًا متقدمًا في طب الأسنان والتدريب السريري، مزودة بعيادات حديthة ومرافق بحثية.',
  },
  facilities: {
    en: '150-acre campus featuring a central library, outdoor theater, sports and entertainment areas, university hospital, dentistry hospital, smart classrooms, state-of-the-art research laboratories, and modern student housing.',
    ar: 'حرم جامعي بمساحة 150 فدانًا يضم مكتبة مركزية، مسرحًا خارجيًا، مناطق رياضية وترفيهية، مستشفى جامعي، مستشفى طب الأسنان، فصول دراسية ذكية، مختبرات بحثية متطورة، وإسكان طلابي حديث.',
  },
  program: {
    en: 'Comprehensive academic programs including bachelor\'s and master\'s degrees in Dentistry, Pharmacy, Engineering, Computer Science, Business, and Public Health Sciences, with international dual-degree partnerships.',
    ar: 'برامج أكاديمية شاملة تشمل درجات البكالوريوس والماجستير في طب الأسنان، الصيدلة، الهندسة، علوم الحاسب، إدارة الأعمال، وعلوم الصحة العامة، مع شراكات دولية للحصول على درجات مزدوجة.',
  },
  dentistryServices: [
    { en: 'General Dentistry', ar: 'طب الأسنان العام' },
    { en: 'Orthodontics', ar: 'تقويم الأسنان' },
    { en: 'Oral Surgery', ar: 'جراحة الفم' },
    { en: 'Pediatric Dentistry', ar: 'طب أسنان الأطفال' },
    { en: 'Periodontics', ar: 'أمراض اللثة' },
    { en: 'Endodontics', ar: 'علاج جذور الأسنان' },
    { en: 'Prosthodontics', ar: 'تركيبات الأسنان' },
    { en: 'Cosmetic Dentistry', ar: 'طب الأسنان التجميلي' },
  ],
  address: {
    street: {
      en: 'New Alamein City, Coastal Road',
      ar: 'مدينة العلمين الجديدة، الطريق الساحلي',
    },
    city: {
      en: 'New Alamein City',
      ar: 'مدينة العلمين الجديدة',
    },
    country: {
      en: 'Egypt',
      ar: 'مصر',
    },
    postalCode: '51718',
  },
  contactInfo: {
    phone: '+20 3 502 5999',
    email: '<EMAIL>',
    website: 'https://aiu.edu.eg',
  },
  logo: 'https://wikiwandv2-19431.kxcdn.com/_next/image?url=https://upload.wikimedia.org/wikipedia/commons/thumb/9/99/AIU_New_Logo.png/640px-AIU_New_Logo.png&w=640&q=50',
  image: 'https://aiu.edu.eg/wp-content/uploads/2021/01/WhatsApp-Image-2021-01-03-at-1.14.34-PM-860x575.jpeg',
  mapUrl:
    'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg',
  timeSlots: generateTimeSlots(),
  slotBeginDate: new Date('2025-05-01T00:00:00Z'),
  slotEndDate: new Date('2025-12-31T23:59:59Z'),
  students: [],
  supervisors: [],
  admins: [],
  assistants: [],
};

// Seed function
const seedUniversity = async () => {
  await connectDB();

  try {
    // Clear existing AIU data
    await University.deleteMany({ universityId: 'AIU' });
    console.log('Existing AIU data cleared');

    // Insert AIU data
    const university = new University(aiuData);
    await university.save();
    console.log('Alamein International University seeded successfully');

    // Close connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('Error seeding university:', error);
    mongoose.connection.close();
    process.exit(1);
  }
};

// Run the seed function
seedUniversity();