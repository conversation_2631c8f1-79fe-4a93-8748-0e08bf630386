import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Loader from '../components/Loader';

const UniversityInfo = () => {
  const { t, i18n } = useTranslation();
  const { universityId } = useParams();
  const [loading, setLoading] = useState(true);
  const [university, setUniversity] = useState(null);
  const [error, setError] = useState(null);

  // Fetch university data from API
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        console.log(`Fetching university with ID: ${universityId}`); // Debug log
        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/universities/${universityId}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'University not found');
        }
        const data = await response.json();
        console.log('University data fetched:', data); // Debug log
        setUniversity(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching university:', err);
        setError(err.message);
        setLoading(false);
      }
    };
    fetchUniversity();
  }, [universityId]);

  // Static facilities data (shared across all universities, bilingual)
  const facilities = [
    {
      title: {
        en: 'Modern Clinics',
        ar: 'عيادات حديثة',
      },
      description: {
        en: 'State-of-the-art dental clinics equipped with the latest technology for patient care.',
        ar: 'عيادات أسنان متطورة مزودة بأحدث التقنيات للعناية بالمرضى.',
      },
      icon: (
        <svg className="h-8 w-8 text-blue-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
          />
        </svg>
      ),
    },
    {
      title: {
        en: 'Research Labs',
        ar: 'مختبرات البحث',
      },
      description: {
        en: 'Advanced research facilities dedicated to dental innovations and treatments.',
        ar: 'مرافق بحثية متقدمة مخصصة لابتكارات وعلاجات طب الأسنان.',
      },
      icon: (
        <svg className="h-8 w-8 text-blue-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
          />
        </svg>
      ),
    },
    {
      title: {
        en: 'Simulation Center',
        ar: 'مركز المحاكاة',
      },
      description: {
        en: 'Cutting-edge simulation centers for training and skill development.',
        ar: 'مراكز محاكاة متطورة للتدريب وتطوير المهارات.',
      },
      icon: (
        <svg className="h-8 w-8 text-blue-900" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
          />
        </svg>
      ),
    },
  ];

  // Animation variants
  const container = { hidden: { opacity: 0 }, show: { opacity: 1, transition: { staggerChildren: 0.1 } } };
  const item = { hidden: { opacity: 0, y: 20 }, show: { opacity: 1, y: 0 } };

  if (loading) return <Loader />;

  if (error || !university) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">{t('universityInfo.error')}</h2>
          <p className="text-[#333333] mb-6">{error || t('universityInfo.universityNotFound')}</p>
          <Link
            to="/universities"
            className="bg-[#0077B6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#0066A0] transition-all duration-300"
          >
            {t('universityInfo.backToUniversities')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`font-sans text-gray-800 bg-white min-h-screen ${
        document.documentElement.dir === 'rtl' ? 'text-right' : 'text-left'
      }`}
    >
      <Navbar />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[rgba(0,119,182,0.1)] to-[rgba(32,178,170,0.1)] overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 py-20 md:py-28 text-center relative z-10">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold text-[#0077B6] mb-6 leading-tight"
          >
            {university.name?.[i18n.language] || university.name || 'University'}{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#0077B6] to-[#20B2AA]">
              {t('universityInfo.subtitle')}
            </span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="text-xl text-[#333333] max-w-3xl mx-auto"
          >
            {university.description?.[i18n.language] || university.description || 'No description available'}
          </motion.p>
        </div>
      </section>

      {/* University Overview */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className={`flex flex-col lg:flex-row gap-12 items-center ${
              document.documentElement.dir === 'rtl' ? 'lg:flex-row-reverse' : ''
            }`}
          >
            <div className="lg:w-1/2">
              <div className="rounded-2xl overflow-hidden shadow-xl">
                <motion.img
                  src={university.image || '/imgs/default-campus.jpeg'}
                  alt={university.name?.[i18n.language] || university.name || 'University'}
                  className="w-full h-full object-cover"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
            <div className="lg:w-1/2">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-[#0077B6] mb-6"
              >
                {t('universityInfo.about')} {university.name?.[i18n.language] || university.name || 'University'}
              </motion.h2>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-4 text-[#333333] mb-8"
              >
                <p>{university.description?.[i18n.language] || university.description || 'No description available'}</p>
                <p>{university.dentistryInfo?.[i18n.language] || university.dentistryInfo || 'No dentistry information available'}</p>
                <p>{university.program?.[i18n.language] || university.program || 'No program information available'}</p>
                <p>
                  <strong>{t('universityInfo.slotPeriod')}:</strong>{' '}
                  {new Date(university.slotBeginDate).toLocaleDateString(i18n.language === 'ar' ? 'ar-EG' : 'en-US')} -{' '}
                  {new Date(university.slotEndDate).toLocaleDateString(i18n.language === 'ar' ? 'ar-EG' : 'en-US')}
                </p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="bg-white p-6 rounded-xl shadow-sm border border-[#20B2AA] border-opacity-30 mb-8"
              >
                <h3 className="text-xl font-bold text-[#0077B6] mb-4">{t('universityInfo.dentistryServices')}</h3>
                <motion.ul
                  variants={container}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  {Array.isArray(university.dentistryServices) && university.dentistryServices.length > 0 ? (
                    university.dentistryServices.map((service, index) => (
                      <motion.li key={index} variants={item} className="flex items-start">
                        <div className="bg-[#0077B6] bg-opacity-10 rounded-lg p-1 mr-3">
                          <svg
                            className="w-4 h-4 text-[#0077B6]"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </div>
                        <span className="text-[#333333]">{service?.[i18n.language] || service || 'Service'}</span>
                      </motion.li>
                    ))
                  ) : (
                    <motion.li variants={item} className="text-[#333333] text-opacity-70">
                      {t('universityInfo.noServices')}
                    </motion.li>
                  )}
                </motion.ul>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                viewport={{ once: true }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/university-book"
                    className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-3 rounded-full font-medium text-center transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center hover:from-[#0066A0] hover:to-[#1A9E98]"
                  >
                    {t('universityInfo.bookAppointment')}
                    <svg className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </Link>
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/contact"
                    className="border-2 border-[#0077B6] text-[#0077B6] hover:bg-[#0077B6] hover:bg-opacity-5 px-8 py-3 rounded-full font-medium text-center transition-all duration-300 flex items-center justify-center"
                  >
                    {t('universityInfo.contactUs')}
                    <svg className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </Link>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Facilities Section */}
      <section className="py-20 bg-[#0077B6] bg-opacity-5">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-4">{t('universityInfo.facilities')}</h2>
            <p className="text-xl text-[#333333] max-w-3xl mx-auto">{university.facilities?.[i18n.language] || university.facilities || 'Facilities information not available'}</p>
          </motion.div>
          <motion.div
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {facilities.map((facility, index) => (
              <motion.div
                key={index}
                variants={item}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[#20B2AA] border-opacity-30 hover:border-[#0077B6] group text-center"
                whileHover={{ y: -5 }}
              >
                <div className="bg-[#0077B6] bg-opacity-10 w-14 h-14 rounded-lg flex items-center justify-center mx-auto mb-6 group-hover:bg-[#0077B6] group-hover:bg-opacity-20 transition-colors duration-300">
                  {React.cloneElement(facility.icon, { className: "h-8 w-8 text-[#0077B6]" })}
                </div>
                <h3 className="text-xl font-bold text-[#0077B6] mb-3">{facility.title[i18n.language]}</h3>
                <p className="text-[#333333]">{facility.description[i18n.language]}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Location Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className={`flex flex-col lg:flex-row gap-12 items-center ${
              document.documentElement.dir === 'rtl' ? 'lg:flex-row-reverse' : ''
            }`}
          >
            <div className="lg:w-1/2">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-[#0077B6] mb-6"
              >
                {t('universityInfo.visitClinic')}
              </motion.h2>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-xl shadow-sm border border-[#20B2AA] border-opacity-30 mb-8"
              >
                <div className="flex items-start mb-6">
                  <div className="bg-[#0077B6] bg-opacity-10 p-2 rounded-lg mr-4">
                    <svg
                      className="h-6 w-6 text-[#0077B6]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-[#0077B6] mb-1">{t('universityInfo.address')}</h3>
                    <p className="text-[#333333]">{`${university.address?.street?.[i18n.language] || university.address?.street || 'Unknown Street'}, ${university.address?.city?.[i18n.language] || university.address?.city || 'Unknown City'}, ${university.address?.country?.[i18n.language] || university.address?.country || 'Unknown Country'}${university.address?.postalCode ? `, ${university.address.postalCode}` : ''}`}</p>
                  </div>
                </div>
                <div className="flex items-start mb-6">
                  <div className="bg-[#0077B6] bg-opacity-10 p-2 rounded-lg mr-4">
                    <svg
                      className="h-6 w-6 text-[#0077B6]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-[#0077B6] mb-1">{t('universityInfo.phone')}</h3>
                    <p className="text-[#333333]">{university.contactInfo?.phone || 'Phone not available'}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-[#0077B6] bg-opacity-10 p-2 rounded-lg mr-4">
                    <svg
                      className="h-6 w-6 text-[#0077B6]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 Dilemma0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-[#0077B6] mb-1">{t('universityInfo.email')}</h3>
                    <p className="text-[#333333]">{university.contactInfo?.email || 'Email not available'}</p>
                  </div>
                </div>
              </motion.div>
              <motion.a
                href={university.contactInfo?.website || '#'}
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-lg font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
              >
                {t('universityInfo.getDirections')}
                <svg className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z"
                    clipRule="evenodd"
                  />
                </svg>
              </motion.a>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="lg:w-1/2"
            >
              <div className="rounded-xl overflow-hidden shadow-xl border border-[#20B2AA] border-opacity-30">
                <iframe
                  src={
                    university.mapUrl ||
                    'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3433.567231276689!2d29.94658231512685!3d30.62898148166882!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14f85d6e7a7a2a9f%3A0x1a9a6c8f8c8f8c8f!2sAlamein%20International%20University!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg'
                  }
                  width="100%"
                  height="450"
                  style={{ border: 0 }}
                  allowFullScreen=""
                  loading="lazy"
                  title={`${university.name?.[i18n.language] || university.name || 'University'} ${t('universityInfo.locationMap')}`}
                ></iframe>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default UniversityInfo;