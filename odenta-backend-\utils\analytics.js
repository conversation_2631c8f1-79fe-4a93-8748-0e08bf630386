const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const getPatientCount = async (studentId) => {
  return await Patient.countDocuments({ drId: studentId });
};

const getCompletedAppointmentsCount = async (studentId) => {
  return await Appointment.countDocuments({ doctor: studentId, status: 'completed' });
};

module.exports = { getPatientCount, getCompletedAppointmentsCount };