const Joi = require('joi');
const { commonSchemas, COLLECTIONS } = require('./index');

// Activity Log validation schema for Firebase
const activityLogSchema = Joi.object({
  id: Joi.string().optional(), // Firestore document ID
  userId: Joi.string().required(),
  userEmail: Joi.string().email().required(),
  userName: Joi.string().required(),
  userRole: Joi.string().valid('student', 'supervisor', 'admin', 'superadmin', 'assistant').required(),
  action: Joi.string().required(), // e.g., 'login', 'logout', 'create_patient', 'update_review'
  resource: Joi.string().required(), // e.g., 'auth', 'patient', 'review', 'appointment'
  resourceId: Joi.string().allow(''), // ID of the affected resource
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').required(),
  endpoint: Joi.string().required(), // API endpoint
  ipAddress: Joi.string().allow(''),
  userAgent: Joi.string().allow(''),
  statusCode: Joi.number().required(),
  responseTime: Joi.number().optional(), // Response time in milliseconds
  details: Joi.object().default({}), // Additional details about the action
  university: Joi.string().allow(''),
  timestamp: Joi.date().default(() => new Date()),
  createdAt: Joi.date().default(() => new Date())
});

// Activity Log creation schema (without ID)
const createActivityLogSchema = activityLogSchema.fork(['id'], (schema) => schema.forbidden());

// Helper functions for activity log operations
const ActivityLogHelpers = {
  // Validate activity log data
  validateCreate: (data) => {
    return createActivityLogSchema.validate(data);
  },
  
  // Transform Mongoose-style data to Firebase format
  transformFromMongoDB: (mongoData) => {
    const transformed = { ...mongoData };
    
    // Convert ObjectId references to strings
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    
    if (transformed.__v !== undefined) {
      delete transformed.__v;
    }
    
    return transformed;
  },
  
  // Prepare data for Firestore storage
  prepareForFirestore: (data) => {
    const prepared = { ...data };
    
    // Ensure dates are proper Date objects
    if (prepared.timestamp && typeof prepared.timestamp === 'string') {
      prepared.timestamp = new Date(prepared.timestamp);
    }
    if (prepared.createdAt && typeof prepared.createdAt === 'string') {
      prepared.createdAt = new Date(prepared.createdAt);
    }
    
    return prepared;
  },
  
  // Format for client response
  formatForResponse: (logData) => {
    return {
      ...logData,
      timestamp: logData.timestamp?.toISOString(),
      createdAt: logData.createdAt?.toISOString()
    };
  },
  
  // Create activity log entry
  logActivity: async (activityData) => {
    try {
      const { FirestoreHelpers } = require('../../config/firebaseDb');
      
      // Validate the data
      const { error, value } = ActivityLogHelpers.validateCreate(activityData);
      if (error) {
        console.error('Activity log validation error:', error.details[0].message);
        return null;
      }
      
      // Prepare data for Firestore
      const preparedData = ActivityLogHelpers.prepareForFirestore(value);
      
      // Create the log entry
      const logEntry = await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, preparedData);
      return logEntry;
    } catch (error) {
      console.error('Error creating activity log:', error);
      return null;
    }
  },
  
  // Get activity logs with filters
  getActivityLogs: async (filters = {}) => {
    try {
      const { FirestoreHelpers } = require('../../config/firebaseDb');
      
      let logs;
      if (filters.userId) {
        logs = await FirestoreHelpers.getByField(COLLECTIONS.ACTIVITY_LOGS, 'userId', filters.userId);
      } else if (filters.userRole) {
        logs = await FirestoreHelpers.getByField(COLLECTIONS.ACTIVITY_LOGS, 'userRole', filters.userRole);
      } else if (filters.university) {
        logs = await FirestoreHelpers.getByField(COLLECTIONS.ACTIVITY_LOGS, 'university', filters.university);
      } else {
        logs = await FirestoreHelpers.getAll(COLLECTIONS.ACTIVITY_LOGS);
      }
      
      // Sort by timestamp descending
      logs.sort((a, b) => {
        const dateA = a.timestamp?.toDate ? a.timestamp.toDate() : new Date(a.timestamp);
        const dateB = b.timestamp?.toDate ? b.timestamp.toDate() : new Date(b.timestamp);
        return dateB - dateA;
      });
      
      return logs.map(log => ActivityLogHelpers.formatForResponse(log));
    } catch (error) {
      console.error('Error getting activity logs:', error);
      return [];
    }
  },
  
  // Clean old logs (older than specified days)
  cleanOldLogs: async (daysToKeep = 90) => {
    try {
      const { FirestoreHelpers } = require('../../config/firebaseDb');
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      const allLogs = await FirestoreHelpers.getAll(COLLECTIONS.ACTIVITY_LOGS);
      let deletedCount = 0;
      
      for (const log of allLogs) {
        const logDate = log.timestamp?.toDate ? log.timestamp.toDate() : new Date(log.timestamp);
        if (logDate < cutoffDate) {
          await FirestoreHelpers.delete(COLLECTIONS.ACTIVITY_LOGS, log.id);
          deletedCount++;
        }
      }
      
      console.log(`Cleaned ${deletedCount} old activity logs`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning old logs:', error);
      return 0;
    }
  }
};

module.exports = {
  activityLogSchema,
  createActivityLogSchema,
  ActivityLogHelpers,
  COLLECTION_NAME: COLLECTIONS.ACTIVITY_LOGS
};
