const express = require('express');
const router = express.Router();
const {
  getPendingReviews,
  getCompletedReviews,
  reviewCase,
  getPatientByNationalId,
  getSignature,
  saveSignature
} = require('../controllers/supervisorController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

router.get('/reviews/pending', auth, role('supervisor'), getPendingReviews);
router.get('/reviews/completed', auth, role('supervisor'), getCompletedReviews);
router.put('/reviews/:reviewId', auth, role('supervisor'), reviewCase);
router.get('/patient/:nationalId', auth, role('supervisor'), getPatientByNationalId);

// Signature routes
router.get('/signature', auth, role('supervisor'), getSignature);
router.post('/signature', auth, role('supervisor'), saveSignature);

module.exports = router;