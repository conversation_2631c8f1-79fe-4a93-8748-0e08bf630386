const multer = require('multer');
const path = require('path');
const fs = require('fs');
const config = require('../config/config');

// Check if Cloudinary is configured
const isCloudinaryConfigured = () => {
  return process.env.CLOUDINARY_CLOUD_NAME &&
         process.env.CLOUDINARY_API_KEY &&
         process.env.CLOUDINARY_API_SECRET;
};

// Use Cloudinary if configured, otherwise fall back to local storage
let upload;

if (isCloudinaryConfigured()) {
  // Use Cloudinary storage
  console.log('Using Cloudinary for file storage');
  const { uploadMultiple } = require('../config/cloudinary');
  upload = uploadMultiple;
} else {
  // Fall back to local storage
  console.log('Using local file storage (Cloudinary not configured)');

  // Ensure uploads directory exists
  const uploadDir = path.join(__dirname, '..', config.UPLOAD_PATH.replace('./', ''));
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  // Set storage engine
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      // Create a unique filename with timestamp and random string
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`);
    },
  });

  // Check file type
  const checkFileType = (file, cb) => {
    const filetypes = /jpeg|jpg|png/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Error: Images only (jpeg, jpg, png)!'));
    }
  };

  // Helper function to parse file size from string like "50mb" to bytes
  const parseFileSize = (sizeStr) => {
    if (typeof sizeStr === 'number') return sizeStr;
    if (!sizeStr) return 5000000; // Default 5MB

    const size = parseFloat(sizeStr);
    const unit = sizeStr.toLowerCase().replace(/[0-9.]/g, '');

    switch (unit) {
      case 'kb': return size * 1024;
      case 'mb': return size * 1024 * 1024;
      case 'gb': return size * 1024 * 1024 * 1024;
      default: return size; // Assume bytes if no unit
    }
  };

  // Initialize upload with configurable file size limit
  upload = multer({
    storage,
    limits: { fileSize: parseFileSize(config.MAX_FILE_SIZE) }, // Configurable limit, default 5MB
    fileFilter: (req, file, cb) => {
      checkFileType(file, cb);
    },
  }).fields([
    { name: 'xrays', maxCount: 10 }, // Multiple X-ray images
    { name: 'galleryImages', maxCount: 10 }, // Multiple gallery images
  ]);
}

module.exports = {
  upload,
  isCloudinaryConfigured
};