import React from 'react';
import { motion } from 'framer-motion';
import { FaClipboardList } from 'react-icons/fa';

const ProcedureRequestsWidget = ({ procedureRequests }) => {
  const formatDate = (dateValue) => {
    try {
      let date;
      
      // Handle different date formats from Firestore
      if (typeof dateValue === 'string') {
        date = new Date(dateValue);
      } else if (dateValue && dateValue.toDate) {
        // Firestore timestamp
        date = dateValue.toDate();
      } else if (dateValue instanceof Date) {
        date = dateValue;
      } else if (dateValue && dateValue._seconds) {
        // Firestore timestamp object
        date = new Date(dateValue._seconds * 1000);
      } else {
        console.warn('Invalid date format:', dateValue);
        return null;
      }
      
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date after parsing:', dateValue);
        return null;
      }
      
      return date;
    } catch (error) {
      console.error('Error parsing date:', error);
      return null;
    }
  };

  const formatDateString = (dateValue) => {
    const date = formatDate(dateValue);
    if (!date) return 'Date not available';
    return date.toLocaleDateString('en-US', {
      month: 'short', day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] overflow-hidden"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-[#0077B6]">Procedure Requests</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {procedureRequests.length === 0 ? (
                <tr>
                  <td colSpan="3" className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <FaClipboardList className="h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-[#0077B6]">No procedure requests</h3>
                      <p className="mt-1 text-[#333333]">You haven't submitted any procedure requests yet.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                procedureRequests.map((request) => (
                  <motion.tr
                    key={request._id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-50 cursor-pointer"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatDateString(request.requestDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{request.procedureType}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(request.status)}`}>
                        {request.status}
                      </span>
                    </td>
                  </motion.tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );
};

export default ProcedureRequestsWidget;
