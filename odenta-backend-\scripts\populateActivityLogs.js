const mongoose = require('mongoose');
const ActivityLog = require('../models/ActivityLog');
const Student = require('../models/Student');
const Supervisor = require('../models/Supervisor');
const Admin = require('../models/Admin');
const Superadmin = require('../models/Superadmin');
const config = require('../config/config');

// Connect to MongoDB
mongoose.connect(config.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const sampleActivities = [
  'User logged in',
  'User logged out',
  'Created patient record',
  'Updated patient record',
  'Created appointment',
  'Updated appointment',
  'Deleted appointment',
  'Created teeth chart',
  'Updated teeth chart',
  'Created review',
  'Updated review',
  'Created user account',
  'Updated user account',
  'Deleted user account',
  'Reset user password',
  'Created university',
  'Updated university',
  'Created news announcement',
  'Viewed analytics',
  'Exported data',
  'Generated report'
];

const sampleDetails = [
  'Email: <EMAIL>',
  'Patient: <PERSON>',
  'Appointment with <PERSON><PERSON>',
  'Procedure: Root Canal',
  'University: Sample University',
  'Role: Student',
  'Status: Completed',
  'Type: Cleaning',
  'Report: Monthly Analytics',
  'Export: CSV format'
];

async function populateActivityLogs() {
  try {
    console.log('Starting to populate activity logs...');

    // Get some existing users for realistic data
    const [students, supervisors, admins, superadmins] = await Promise.all([
      Student.find().limit(5),
      Supervisor.find().limit(3),
      Admin.find().limit(2),
      Superadmin.find().limit(1)
    ]);

    let allUsers = [
      ...students.map(u => ({ ...u.toObject(), role: 'student' })),
      ...supervisors.map(u => ({ ...u.toObject(), role: 'supervisor' })),
      ...admins.map(u => ({ ...u.toObject(), role: 'admin' })),
      ...superadmins.map(u => ({ ...u.toObject(), role: 'superadmin' }))
    ];

    // If no users found, create mock user data
    if (allUsers.length === 0) {
      console.log('No users found. Creating mock user data for activity logs.');
      allUsers = [
        { _id: new mongoose.Types.ObjectId(), name: 'John Doe', role: 'student' },
        { _id: new mongoose.Types.ObjectId(), name: 'Jane Smith', role: 'student' },
        { _id: new mongoose.Types.ObjectId(), name: 'Dr. Johnson', role: 'supervisor' },
        { _id: new mongoose.Types.ObjectId(), name: 'Admin User', role: 'admin' },
        { _id: new mongoose.Types.ObjectId(), name: 'Super Admin', role: 'superadmin' },
        { _id: new mongoose.Types.ObjectId(), name: 'Assistant User', role: 'assistant' }
      ];
    }

    console.log(`Using ${allUsers.length} users to create activities for.`);

    // Clear existing activity logs
    await ActivityLog.deleteMany({});
    console.log('Cleared existing activity logs.');

    const activityLogs = [];
    const now = new Date();

    // Generate activities for the last 30 days
    for (let i = 0; i < 100; i++) {
      const randomUser = allUsers[Math.floor(Math.random() * allUsers.length)];
      const randomActivity = sampleActivities[Math.floor(Math.random() * sampleActivities.length)];
      const randomDetail = sampleDetails[Math.floor(Math.random() * sampleDetails.length)];
      
      // Random date within last 30 days
      const randomDate = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);

      activityLogs.push({
        userId: randomUser._id,
        userName: randomUser.name,
        userRole: randomUser.role,
        action: randomActivity,
        details: randomDetail,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        timestamp: randomDate
      });
    }

    // Insert all activity logs
    await ActivityLog.insertMany(activityLogs);
    console.log(`Successfully created ${activityLogs.length} activity log entries.`);

    // Create some recent activities (last 24 hours)
    const recentActivities = [];
    for (let i = 0; i < 20; i++) {
      const randomUser = allUsers[Math.floor(Math.random() * allUsers.length)];
      const randomActivity = sampleActivities[Math.floor(Math.random() * sampleActivities.length)];
      const randomDetail = sampleDetails[Math.floor(Math.random() * sampleDetails.length)];
      
      // Random date within last 24 hours
      const randomDate = new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000);

      recentActivities.push({
        userId: randomUser._id,
        userName: randomUser.name,
        userRole: randomUser.role,
        action: randomActivity,
        details: randomDetail,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        timestamp: randomDate
      });
    }

    await ActivityLog.insertMany(recentActivities);
    console.log(`Successfully created ${recentActivities.length} recent activity log entries.`);

    console.log('Activity log population completed successfully!');
    
    // Show some statistics
    const stats = await ActivityLog.aggregate([
      {
        $group: {
          _id: '$userRole',
          count: { $sum: 1 }
        }
      }
    ]);

    console.log('\nActivity statistics by role:');
    stats.forEach(stat => {
      console.log(`${stat._id}: ${stat.count} activities`);
    });

  } catch (error) {
    console.error('Error populating activity logs:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the script
populateActivityLogs();
