import React, { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaTooth } from 'react-icons/fa';
import DOMPurify from 'dompurify';
import Loader from './Loader';

// Define valid routes to prevent open redirect attacks
const VALID_ROUTES = ['/', '/login'];

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Secure navigation handler
  const safeNavigate = useCallback((path) => {
    if (VALID_ROUTES.includes(path)) {
      navigate(path);
    } else {
      console.warn('Invalid navigation attempt:', path);
      navigate('/'); // Fallback to home
    }
  }, [navigate]);

  // Sanitize input to prevent XSS
  const sanitizeInput = (input) => {
    return DOMPurify.sanitize(input, { USE_PROFILES: { html: false } });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Sanitize email input
    const sanitizedEmail = sanitizeInput(email);

    if (!sanitizedEmail) {
      setError('Invalid email input.');
      setLoading(false);
      return;
    }

    try {
      // Retrieve CSRF token (assumed to be in meta tag)
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content || '';

      const response = await axios.post(
        'https://api.dentlyzer.com/api/auth/forgot-password',
        { email: sanitizedEmail },
        {
          headers: {
            'X-CSRF-Token': csrfToken,
            'Content-Type': 'application/json',
          },
          withCredentials: true,
        }
      );

      setSuccess(response.data.message || 'Password reset link sent to your email.');
      setEmail('');
    } catch (err) {
      // Generic error to prevent info leakage
      setError(err.response?.data?.message || 'Failed to send reset link. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Animation variants to match Home.jsx and Login.jsx
  const container = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100 p-4 sm:p-6 relative overflow-hidden">

      {/* Forgot Password Card */}
      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className="bg-white bg-opacity-95 rounded-2xl shadow-xl w-full max-w-md p-6 sm:p-8 border border-blue-100 z-10"
      >
        <motion.div variants={item} className="flex items-center justify-center mb-6">
          <motion.div
            className="w-10 h-10 flex items-center justify-center mr-2"
            whileHover={{ rotate: 10 }}
          >
            <FaTooth className="w-8 h-8 text-blue-600" />
          </motion.div>
          <h1 className="text-xl sm:text-2xl font-bold text-blue-900">
            DENT<span className="text-blue-500">LYZER</span>
          </h1>
        </motion.div>

        <motion.h2
          variants={item}
          className="text-2xl sm:text-3xl font-bold text-blue-900 text-center mb-2"
        >
          Reset Your Password
        </motion.h2>
        <motion.p
          variants={item}
          className="text-gray-600 text-center mb-6 text-sm sm:text-base"
        >
          Enter your email to receive a password reset link
        </motion.p>

        {error && (
          <motion.div
            variants={item}
            className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg text-sm border border-red-200"
          >
            {error}
          </motion.div>
        )}

        {success && (
          <motion.div
            variants={item}
            className="mb-6 p-4 bg-green-50 text-green-700 rounded-lg text-sm border border-green-200"
          >
            {success}
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <motion.div variants={item}>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors bg-gray-50 text-gray-900 text-sm sm:text-base"
              placeholder="<EMAIL>"
              aria-label="Email Address"
            />
          </motion.div>

          <motion.button
            variants={item}
            type="submit"
            disabled={loading}
            className={`w-full bg-gradient-to-r ${
              loading
                ? 'from-blue-400 to-blue-500 cursor-not-allowed'
                : 'from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900'
            } text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm sm:text-base`}
            whileHover={{ scale: loading ? 1 : 1.05 }}
            whileTap={{ scale: loading ? 1 : 0.95 }}
            aria-label={loading ? 'Sending...' : 'Send Reset Link'}
          >
            {loading ? 'Sending...' : 'Send Reset Link'}
          </motion.button>
        </form>

        <motion.div variants={item} className="mt-6 text-center">
          <Link
            to="/login"
            onClick={() => safeNavigate('/login')}
            className="text-blue-600 hover:text-blue-800 font-medium transition-colors flex items-center justify-center"
            aria-label="Back to Login"
          >
            <span className="mr-1">Back to Login</span>
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ForgotPassword;