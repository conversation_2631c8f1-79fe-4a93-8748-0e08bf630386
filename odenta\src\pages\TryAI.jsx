import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { jsPDF } from 'jspdf';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

// Animation variants
const container = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

const TryAI = () => {
  const [image, setImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [detectedImage, setDetectedImage] = useState(null);
  const language = 'en'; // Change to 'ar' for Arabic

  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setImage(file);
      setImagePreview(URL.createObjectURL(file));
      setCodeError('');
      console.log('Image uploaded:', file.name);
    } else {
      setCodeError(
        language === 'ar'
          ? 'يرجى تحميل صورة صالحة.'
          : 'Please upload a valid image.'
      );
    }
  };

  // Simulate bounding box generation
  const generateDetectedImage = () => {
    if (!imagePreview) {
      console.error('No image preview available');
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    img.src = imagePreview;
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Mock bounding boxes
      const classes = [
        'Caries',
        'Fracture',
        'Impacted Tooth',
        'Cyst',
        'Abscess',
      ];
      for (let i = 0; i < 3; i++) {
        const x = Math.random() * (img.width - 100);
        const y = Math.random() * (img.height - 100);
        const width = 50 + Math.random() * 50;
        const height = 50 + Math.random() * 50;
        const label = classes[Math.floor(Math.random() * classes.length)];

        ctx.strokeStyle = 'red';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);
        ctx.fillStyle = 'red';
        ctx.font = '14px Arial';
        ctx.fillText(label, x, y - 5);
      }

      setDetectedImage(canvas.toDataURL('image/png'));
      console.log('Detected image generated');
    };
  };

  // Generate PDF report
  const generatePDF = () => {
    const doc = new jsPDF();
    doc.setFontSize(18);
    doc.text('Dentlyzer AI Report', 20, 20);
    doc.setFontSize(12);
    doc.text('Generated on: ' + new Date().toLocaleDateString(), 20, 30);
    doc.text('Detected Classes:', 20, 40);
    doc.text('- Caries', 20, 50);
    doc.text('- Fracture', 20, 60);
    doc.text('- Impacted Tooth', 20, 70);
    doc.text('Summary: The AI detected multiple dental conditions.', 20, 90);
    doc.save('Dentlyzer_AI_Report.pdf');
    console.log('PDF generated');
  };

  // Handle code submission
  const handleCodeSubmit = (e) => {
    e.preventDefault();
    const correctCode = 'DENTLYZER2025'; // Replace with API validation
    if (code === correctCode) {
      setShowModal(false);
      setShowResults(true);
      setCodeError('');
      generateDetectedImage();
      console.log('Code validated successfully');
    } else {
      setCodeError(
        language === 'ar'
          ? 'الكود غير صحيح. حاول مرة أخرى.'
          : 'Incorrect code. Please try again.'
      );
      console.log('Invalid code entered:', code);
    }
  };

  // Handle Generate Report button
  const handleGenerateReport = () => {
    if (!image || !imagePreview) {
      setCodeError(
        language === 'ar'
          ? 'يرجى تحميل صورة أولاً.'
          : 'Please upload an image first.'
      );
      console.error('No image uploaded');
      return;
    }
    setShowModal(true);
    setCode('');
    setCodeError('');
    console.log('Generate Report button clicked, opening modal');
  };

  // Scroll to upload section
  const scrollToUpload = () => {
    document.getElementById('upload-section').scrollIntoView({ behavior: 'smooth' });
  };

  // Debug state changes
  useEffect(() => {
    console.log('Image state:', image);
    console.log('Image preview:', imagePreview);
  }, [image, imagePreview]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white relative overflow-hidden">
      <Navbar />

      {/* Hero Section */}
      <motion.section
        variants={container}
        initial="hidden"
        animate="show"
        className="pt-28 pb-20 text-center relative"
      >
        <div
          className="absolute inset-0 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] opacity-40"
          style={{ clipPath: 'polygon(0 0, 100% 0, 100% 90%, 50% 100%, 0 90%)' }}
        ></div>
        <motion.div
          variants={item}
          className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6"
        >
          <motion.h1
            className="text-5xl sm:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#0077B6] to-[#20B2AA] mb-6"
            whileHover={{ scale: 1.02 }}
            style={{ textShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}
          >
            {language === 'ar'
              ? 'ذكاء دينتلايزر الثوري'
              : 'Dentlyzer’s Revolutionary AI'}
          </motion.h1>
          <motion.p
            variants={item}
            className="text-xl sm:text-2xl text-[#333333] max-w-4xl mx-auto mb-10"
          >
            {language === 'ar'
              ? 'اكتشف ما يصل إلى 30 حالة في الأشعة السينية البانورامية بدقة لا مثيل لها، مع تقارير فورية وتصور مرئي.'
              : 'Detect up to 30 conditions in panoramic X-rays with unmatched precision, instant reports, and visual insights.'}
          </motion.p>
          <motion.div
            variants={item}
            className="relative flex justify-center mb-12"
          >
            <motion.img
              src="/imgs/x-ray-1.jpg" // Replace with actual X-ray mockup
              alt="X-Ray Detection"
              className="w-full max-w-5xl h-auto rounded-2xl shadow-2xl border border-[#20B2AA] border-opacity-30"
              style={{ maxHeight: '450px', objectFit: 'contain' }}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            />
            {/* Animated bounding box overlay */}
            <motion.div
              className="absolute top-1/4 left-1/3 w-24 h-24 border-2 border-red-500 rounded-md"
              animate={{ scale: [1, 1.05, 1], opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span className="absolute -top-6 left-0 text-red-500 text-sm font-semibold">
                {language === 'ar' ? 'التسوس' : 'Caries'}
              </span>
            </motion.div>
          </motion.div>
          <motion.div
            variants={item}
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            <motion.div
              className="bg-white bg-opacity-95 backdrop-blur-lg p-8 rounded-2xl shadow-xl border border-[#20B2AA] border-opacity-30 transform"
              whileHover={{ scale: 1.05, rotate: 1, y: -10 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <motion.div
                className="text-[#0077B6] mb-4"
                animate={{ y: [0, -5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <svg
                  className="w-10 h-10 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox=" Injecting SVG Path: M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                  </svg>
                </motion.div>
                <p className="text-[#0077B6] font-semibold text-xl" >
                  {language === 'ar' ? 'دقة فائقة' : 'Unmatched Accuracy'}
                </p>
                <p className="text-[#333333] text-sm mt-2">
                  {language === 'ar'
                    ? 'تحليل دقيق لـ 30 حالة.'
                    : 'Precise analysis of 30 conditions.'}
                </p>
              </motion.div>
              <motion.div
                className="bg-white bg-opacity-95 backdrop-blur-lg p-8 rounded-2xl shadow-xl border border-[#20B2AA] border-opacity-30 transform"
                whileHover={{ scale: 1.05, rotate: -1, y: -10 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <motion.div
                  className="text-[#0077B6] mb-4"
                  animate={{ y: [0, -5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <svg
                    className="w-10 h-10 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </motion.div>
                <p className="text-[#0077B6] font-semibold text-xl">
                  {language === 'ar' ? 'تقارير سريعة' : 'Rapid Reports'}
                </p>
                <p className="text-[#333333] text-sm mt-2">
                  {language === 'ar'
                    ? 'نتائج فورية في ثوان.'
                    : 'Instant results in seconds.'}
                </p>
              </motion.div>
              <motion.div
                className="bg-white bg-opacity-95 backdrop-blur-lg p-8 rounded-2xl shadow-xl border border-[#20B2AA] border-opacity-30 transform"
                whileHover={{ scale: 1.05, rotate: 1, y: -10 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <motion.div
                  className="text-[#0077B6] mb-4"
                  animate={{ y: [0, -5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <svg
                    className="w-10 h-10 mx-auto"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </motion.div>
                <p className="text-[#0077B6] font-semibold text-xl">
                  {language === 'ar' ? 'تصور بصري' : 'Visual Analysis'}
                </p>
                <p className="text-[#333333] text-sm mt-2">
                  {language === 'ar'
                    ? 'صناديق تحديد واضحة.'
                    : 'Clear bounding boxes.'}
                </p>
              </motion.div>
            </motion.div>
            <motion.button
              variants={item}
              onClick={scrollToUpload}
              className="mt-10 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-4 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 text-lg"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {language === 'ar' ? 'جرب الآن' : 'Try Now'}
            </motion.button>
          </motion.div>
        </motion.section>

        {/* Upload Section */}
        <motion.section
          variants={container}
          initial="hidden"
          animate="show"
          className="py-16 px-4 sm:px-6 max-w-4xl mx-auto relative"
          id="upload-section"
        >
          <motion.h2
            variants={item}
            className="text-3xl sm:text-4xl font-bold text-[#0077B6] text-center mb-8"
          >
            {language === 'ar'
              ? 'تحميل صورة الأشعة السينية'
              : 'Upload Your X-Ray Image'}
          </motion.h2>
          <motion.div
            variants={item}
            className="bg-white bg-opacity-90 backdrop-blur-lg p-8 sm:p-10 rounded-2xl shadow-2xl border border-[#20B2AA] border-opacity-30 relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] opacity-10"></div>
            <div
              className="border-2 border-dashed border-[#20B2AA] rounded-xl p-10 text-center relative z-10"
              onDragOver={(e) => e.preventDefault()}
              onDrop={(e) => {
                e.preventDefault();
                handleImageUpload({ target: { files: e.dataTransfer.files } });
              }}
            >
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                id="image-upload"
              />
              <label
                htmlFor="image-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <motion.div
                  className="p-4 rounded-full bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white mb-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-10 w-10"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16V8m0 0L3 12m4-4l4 4m6 4v6m-6-6l4-4m0 0l-4-4"
                    />
                  </svg>
                </motion.div>
                <p className="text-[#333333] text-lg">
                  {language === 'ar'
                    ? 'اسحب وأفلت صورة الأشعة أو انقر للتحميل'
                    : 'Drag and drop your X-ray or click to upload'}
                </p>
                <p className="text-[#333333] text-opacity-70 text-sm mt-2">
                  {language === 'ar'
                    ? 'يدعم صيغ PNG، JPG، وغيرها.'
                    : 'Supports PNG, JPG, and more.'}
                </p>
              </label>
            </div>
            {imagePreview && (
              <motion.div
                variants={item}
                className="mt-8 flex justify-center"
              >
                <img
                  src={imagePreview}
                  alt="Uploaded X-ray"
                  className="max-w-full h-auto rounded-xl shadow-lg max-h-72 object-contain"
                />
              </motion.div>
            )}
            {codeError && !showModal && (
              <motion.p
                variants={item}
                className="mt-4 text-red-600 text-center text-sm"
              >
                {codeError}
              </motion.p>
            )}
            <motion.button
              variants={item}
              onClick={handleGenerateReport}
              className={`mt-8 w-full bg-gradient-to-r ${
                !imagePreview
                  ? 'from-gray-400 to-gray-500 cursor-not-allowed'
                  : 'from-[#0077B6] to-[#20B2AA] hover:from-[#0066A0] hover:to-[#1A9E98]'
              } text-white px-6 py-4 rounded-full font-medium transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 text-base border-2 ${
                imagePreview ? 'border-[#20B2AA]' : 'border-gray-400'
              }`}
              whileHover={{ scale: imagePreview ? 1.05 : 1 }}
              whileTap={{ scale: imagePreview ? 0.95 : 1 }}
            >
              {language === 'ar' ? 'إنشاء تقرير الذكاء الاصطناعي' : 'Generate AI Report'}
            </motion.button>
          </motion.div>
        </motion.section>

        {/* Code Validation Modal */}
        {showModal && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <motion.div
              className="bg-white bg-opacity-95 backdrop-blur-lg p-8 sm:p-10 max-w-md w-full rounded-2xl shadow-2xl border border-[#20B2AA] border-opacity-30 relative overflow-hidden"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] opacity-10"></div>
              <h2 className="text-2xl sm:text-3xl font-bold text-[#0077B6] mb-6 text-center relative z-10">
                {language === 'ar' ? 'أدخل رمز الوصول' : 'Enter Access Code'}
              </h2>
              <form onSubmit={handleCodeSubmit} className="space-y-6 relative z-10">
                <input
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder={language === 'ar' ? 'أدخل رمز الوصول هنا' : 'Enter access code here'}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] outline-none transition-colors bg-gray-50 text-[#333333] text-sm sm:text-base"
                />
                {codeError && (
                  <p className="text-red-600 text-sm text-center">{codeError}</p>
                )}
                <div className="text-center text-sm text-[#333333]">
                  {language === 'ar'
                    ? 'ليس لديك رمز؟ '
                    : "Don't have a code? "}
                  <Link
                    to="/contact"
                    className="text-[#0077B6] hover:text-[#0066A0] font-medium"
                  >
                    {language === 'ar' ? 'اتصل بنا' : 'Contact Us'}
                  </Link>
                </div>
                <div className="flex gap-4">
                  <motion.button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-4 py-3 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {language === 'ar' ? 'إرسال' : 'Submit'}
                  </motion.button>
                  <motion.button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="flex-1 bg-gray-200 text-gray-700 px-4 py-3 rounded-full font-medium hover:bg-gray-300 transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {language === 'ar' ? 'إلغاء' : 'Cancel'}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}

        {/* Results Section with Placeholders */}
        <motion.section
          variants={container}
          initial="hidden"
          animate="show"
          className="py-16 px-4 sm:px-6 max-w-7xl mx-auto"
        >
          <motion.h2
            variants={item}
            className="text-3xl sm:text-4xl font-bold text-[#0077B6] text-center mb-8"
          >
            {language === 'ar' ? 'نتائج تحليل الذكاء الاصطناعي' : 'AI Analysis Results'}
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <motion.div variants={item} className="bg-white bg-opacity-90 backdrop-blur-lg rounded-xl shadow-lg p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] opacity-10"></div>
              <h3 className="text-lg sm:text-xl font-semibold text-[#0077B6] mb-4 relative z-10">
                {language === 'ar' ? 'الصورة الأصلية' : 'Original Image'}
              </h3>
              {showResults && imagePreview ? (
                <img
                  src={imagePreview}
                  alt="Original X-ray"
                  className="w-full h-auto rounded-lg max-h-96 object-contain relative z-10"
                />
              ) : (
                <div className="w-full h-64 bg-gray-100 rounded-lg animate-pulse flex items-center justify-center relative z-10">
                  <p className="text-[#333333] text-opacity-70 text-sm">
                    {language === 'ar'
                      ? 'سيظهر الصورة الأصلية هنا'
                      : 'Original image will appear here'}
                  </p>
                </div>
              )}
            </motion.div>
            <motion.div variants={item} className="bg-white bg-opacity-90 backdrop-blur-lg rounded-xl shadow-lg p-6 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] opacity-10"></div>
              <h3 className="text-lg sm:text-xl font-semibold text-[#0077B6] mb-4 relative z-10">
                {language === 'ar' ? 'الصورة المكتشفة' : 'Detected Image'}
              </h3>
              {showResults && detectedImage ? (
                <img
                  src={detectedImage}
                  alt="Detected X-ray"
                  className="w-full h-auto rounded-lg max-h-96 object-contain relative z-10"
                />
              ) : (
                <div className="w-full h-64 bg-gray-100 rounded-lg animate-pulse flex items-center justify-center relative z-10">
                  <p className="text-[#333333] text-opacity-70 text-sm">
                    {language === 'ar'
                      ? 'ستظهر الصورة المكتشفة هنا'
                      : 'Detected image will appear here'}
                  </p>
                </div>
              )}
            </motion.div>
          </div>
          <motion.div
            variants={item}
            className="mt-8 text-center"
          >
            {showResults ? (
              <motion.button
                onClick={generatePDF}
                className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-8 py-4 rounded-full font-medium hover:from-[#0066A0] hover:to-[#1A9E98] transition-all duration-300 shadow-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:ring-offset-2 text-base"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {language === 'ar' ? 'تنزيل تقرير PDF' : 'Download PDF Report'}
              </motion.button>
            ) : (
              <div className="bg-gray-100 h-12 w-64 mx-auto rounded-full animate-pulse flex items-center justify-center">
                <p className="text-[#333333] text-opacity-70 text-sm">
                  {language === 'ar'
                    ? 'سيظهر زر تنزيل PDF هنا'
                    : 'PDF download button will appear here'}
                </p>
              </div>
            )}
          </motion.div>
        </motion.section>

        <Footer />
      </div>
    );
};

export default TryAI;