import React, { useState, useEffect } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import './Chart.css';
import html2canvas from 'html2canvas';
import { useAuth } from '../context/AuthContext';
import { motion } from 'framer-motion';
import { FaSearch, FaChevronDown, FaFileExport, FaTimes, FaStickyNote } from 'react-icons/fa';

const DentalChart = () => {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedTooth, setSelectedTooth] = useState(null);
  const [toothData, setToothData] = useState({}); // Tracks unsaved changes
  const [selectedChart, setSelectedChart] = useState(null);
  const [activeTab, setActiveTab] = useState('procedures');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [currentNote, setCurrentNote] = useState('');
  const [isColorReferenceOpen, setIsColorReferenceOpen] = useState(false);
  const { nationalId } = useParams();

  const upperTeethNumbers = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28];
  const lowerTeethNumbers = [48, 47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37, 38];
  const surfaceTypes = ['occlusal', 'incisal', 'buccal', 'mesial', 'palatal', 'distal', 'lingual'];

  const procedureCategories = [
    {
      title: 'Restorative',
      items: [
        { id: 'composite', name: 'Composite', color: '#4CA3D8' },
        { id: 'amalgam', name: 'Amalgam', color: '#8C8C8C' },
        { id: 'filling', name: 'Filling', color: '#7CBEEB' },
      ],
    },
    {
      title: 'Prosthodontics',
      items: [
        { id: 'crown', name: 'Crown', color: '#FFD700' },
        { id: 'implant', name: 'Implant', color: '#FFA500' },
      ],
    },
    {
      title: 'Endodontics',
      items: [
        { id: 'rootCanal', name: 'Root Canal', color: '#FF4444' },
      ],
    },
    {
      title: 'Oral Surgery',
      items: [
        { id: 'extraction', name: 'Extraction', color: '#666666' },
      ],
    },
    {
      title: 'Periodontics',
      items: [
        { id: 'scaling', name: 'Scaling', color: '#20B2AA' },
      ],
    },
  ];

  const conditionCategories = [
    {
      title: 'Pathological',
      items: [
        { id: 'decayed', name: 'Decayed', color: '#FF4444' },
        { id: 'cracked', name: 'Cracked', color: '#8B0000' },
        { id: 'fractured', name: 'Fractured', color: '#800000' },
        { id: 'abscess', name: 'Abscess', color: '#FF0000' },
      ],
    },
    {
      title: 'Status',
      items: [
        { id: 'missing', name: 'Missing', color: '#666666' },
        { id: 'filled', name: 'Filled', color: '#FFD700' },
        { id: 'healthy', name: 'Healthy', color: '#32CD32' },
      ],
    },
  ];

  useEffect(() => {
    const fetchOrCreateChart = async () => {
      try {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/dental/patient/${nationalId}`);
        let charts = response.data.sort((a, b) => new Date(a.date) - new Date(b.date));

        if (charts.length === 0) {
          // Create the single chart for this patient
          const singleChart = {
            patient: nationalId,
            title: 'Dental Chart',
            date: new Date().toISOString(),
            teeth: [],
          };
          const createResponse = await axios.post(`${process.env.REACT_APP_API_URL}/api/dental`, singleChart);
          console.log('Created chart response:', createResponse.data);
          setSelectedChart(createResponse.data);
        } else {
          // Use the first (and should be only) chart
          console.log('Using existing chart:', charts[0]);
          setSelectedChart(charts[0]);
        }

        setToothData({}); // Initialize empty toothData for new changes
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to load or create dental chart');
        console.error('Fetch or create chart error:', err.response?.data);
      } finally {
        setLoading(false);
      }
    };
    fetchOrCreateChart();
  }, [nationalId]);



  const getToothColor = (tooth) => {
    if (!tooth) return null;

    if (tooth.procedure && tooth.procedure !== 'N/A') {
      const procedure = procedureCategories
        .flatMap((cat) => cat.items)
        .find((p) => p.name === tooth.procedure);
      return procedure?.color;
    } else if (tooth.condition && tooth.condition !== 'N/A') {
      const condition = conditionCategories
        .flatMap((cat) => cat.items)
        .find((c) => c.name === tooth.condition);
      return condition?.color;
    }
    return null;
  };

  const handleToothClick = (toothNumber) => {
    setSelectedTooth(toothNumber);
    setSearchTerm('');

    if (!toothData[toothNumber]) {
      setToothData((prev) => ({
        ...prev,
        [toothNumber]: { surfaces: [], notes: '', procedure: 'N/A', condition: 'N/A' },
      }));
    }
  };

  const handleSurfaceClick = (surface, toothNumber) => {
    setToothData((prev) => {
      const currentToothData = prev[toothNumber] || {};
      const currentSurfaces = Array.isArray(currentToothData.surfaces)
        ? currentToothData.surfaces
        : [];
      const newSurfaces = currentSurfaces.includes(surface)
        ? currentSurfaces.filter((s) => s !== surface)
        : [...currentSurfaces, surface];
      return {
        ...prev,
        [toothNumber]: {
          ...currentToothData,
          surfaces: newSurfaces,
        },
      };
    });
  };

  const generateChartPDF = async () => {
    try {
      if (!selectedChart) throw new Error('No chart selected');
      const doc = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' });

      const addHeader = (pageNum) => {
        // Primary color #0077B6 in RGB is 0, 119, 182
        doc.setFillColor(0, 119, 182);
        doc.rect(0, 0, 210, 20, 'F');
        doc.setFontSize(18);
        doc.setTextColor(255, 255, 255);
        doc.setFont('helvetica', 'bold');
        doc.text('DENTLYZER - Dental Chart', 105, 12, { align: 'center' });
        doc.setFontSize(10);
        doc.setTextColor(220, 220, 220);
        doc.text(`Generated: ${new Date().toLocaleString()}`, 10, 17);
        doc.text(`Page ${pageNum}`, 200, 17, { align: 'right' });
      };

      const chartElement = document.querySelector('.teeth-chart');
      let chartImage = null;
      if (chartElement) {
        const canvas = await html2canvas(chartElement, { scale: 2, backgroundColor: '#ffffff' });
        chartImage = canvas.toDataURL('image/png');
      }

      addHeader(1);
      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182); // #0077B6
      doc.text('Patient Overview', 20, 30);
      doc.setDrawColor(200);
      doc.line(20, 32, 190, 32);
      doc.setFontSize(11);
      doc.setTextColor(0);
      doc.setFont('helvetica', 'normal');
      const patientInfo = [
        `Patient ID: ${nationalId}`,
        `Chart Title: ${selectedChart.title}`,
        `Date: ${new Date(selectedChart.date).toLocaleDateString()}`,
      ];
      patientInfo.forEach((line, index) => doc.text(line, 20, 40 + index * 7));

      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182); // #0077B6
      doc.text('Dental Chart Visualization', 20, 75);
      doc.setDrawColor(200);
      doc.line(20, 77, 190, 77);
      if (chartImage) {
        const imgProps = doc.getImageProperties(chartImage);
        const pdfWidth = 170;
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
        doc.addImage(chartImage, 'PNG', 20, 85, pdfWidth, pdfHeight > 120 ? 120 : pdfHeight);
      } else {
        doc.setFontSize(10);
        doc.setTextColor(100);
        doc.text('Chart visualization unavailable', 20, 85);
      }

      const records = [
        ...selectedChart.teeth,
        ...Object.entries(toothData).map(([toothNumber, data]) => ({
          toothNumber: parseInt(toothNumber),
          procedure: data.procedure || 'N/A',
          condition: data.condition || 'N/A',
          surfaces: data.surfaces || [],
          notes: data.notes || 'No additional notes provided',
        })),
      ].filter((tooth) => tooth.procedure !== 'N/A' || tooth.condition !== 'N/A')
       .sort((a, b) => a.toothNumber - b.toothNumber);

      if (records.length > 0) {
        doc.addPage();
        addHeader(2);
        doc.setFontSize(14);
        doc.setTextColor(0, 119, 182); // #0077B6
        doc.setFont('helvetica', 'bold');
        doc.text('Treatment Records', 20, 30);
        doc.setDrawColor(200);
        doc.line(20, 32, 190, 32);
        let yPosition = 40;
        records.forEach((tooth, index) => {
          doc.setFillColor(245, 245, 245);
          doc.roundedRect(20, yPosition, 170, 35, 3, 3, 'F');
          const toothColor = getToothColor(tooth) || '#CCCCCC';
          doc.setFillColor(...hexToRgb(toothColor));
          doc.circle(25, yPosition + 8, 4, 'F');
          doc.setFontSize(12);
          doc.setTextColor(0);
          doc.setFont('helvetica', 'bold');
          doc.text(`Tooth ${tooth.toothNumber}`, 35, yPosition + 10);
          doc.setFontSize(10);
          doc.setFont('helvetica', 'normal');
          const details = [];
          if (tooth.condition !== 'N/A') details.push(`Condition: ${tooth.condition}`);
          if (tooth.procedure !== 'N/A') details.push(`Procedure: ${tooth.procedure}`);
          if (tooth.surfaces?.length > 0)
            details.push(
              `Surfaces: ${Array.isArray(tooth.surfaces) ? tooth.surfaces.join(', ') : tooth.surfaces}`
            );
          if (tooth.notes)
            details.push(`Notes: ${tooth.notes.substring(0, 50)}${tooth.notes.length > 50 ? '...' : ''}`);
          details.forEach((detail, i) => doc.text(detail, 35, yPosition + 15 + i * 5));
          yPosition += 40;
          if (yPosition > 250 && index < records.length - 1) {
            doc.addPage();
            addHeader(doc.internal.getNumberOfPages());
            yPosition = 30;
          }
        });
      }

      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setTextColor(150);
        doc.text(`Page ${i} of ${pageCount}`, 105, 287, { align: 'center' });
      }

      return doc.output('datauristring');
    } catch (err) {
      console.error('PDF generation failed:', err);
      throw new Error('Failed to generate PDF');
    }
  };

  const handleSave = async () => {
    if (!selectedTooth || !selectedChart || !user?.studentId) {
      setError('Cannot save: Invalid tooth or user not authenticated');
      return;
    }

    if (!selectedChart.id) {
      setError('Cannot save: Chart ID is missing. Please refresh the page and try again.');
      return;
    }

    const currentToothData = toothData[selectedTooth] || {};
    const record = {
      toothNumber: selectedTooth,
      procedure: currentToothData.procedure?.name || 'N/A',
      surfaces: Array.isArray(currentToothData.surfaces) ? currentToothData.surfaces : [],
      condition: currentToothData.condition?.name || 'N/A',
      notes: currentToothData.notes && currentToothData.notes.trim() !== '' ? currentToothData.notes : 'No additional notes provided',
    };

    try {
      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/dental/chart/${selectedChart.id}`, record);
      const updatedTooth = response.data;
      const updatedChart = {
        ...selectedChart,
        teeth: [...selectedChart.teeth.filter((t) => t.toothNumber !== selectedTooth), updatedTooth],
      };
      setSelectedChart(updatedChart);
      // Clear the saved tooth data from toothData
      setToothData((prev) => {
        const { [selectedTooth]: _, ...rest } = prev;
        return rest;
      });

      setSelectedTooth(null);
      setError('');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save tooth data');
      console.error('Save error:', err.response?.data);
    }
  };

  const handleSaveNote = async () => {
    if (!selectedTooth || !selectedChart || !user?.studentId) {
      setError('Cannot save note: Invalid tooth or user not authenticated');
      return;
    }

    if (!selectedChart.id) {
      setError('Cannot save note: Chart ID is missing. Please refresh the page and try again.');
      return;
    }

    try {
      const currentToothData = toothData[selectedTooth] || {};
      const updatedToothData = {
        toothNumber: selectedTooth,
        procedure: currentToothData.procedure?.name || 'N/A',
        surfaces: Array.isArray(currentToothData.surfaces) ? currentToothData.surfaces : [],
        condition: currentToothData.condition?.name || 'N/A',
        notes: currentNote && currentNote.trim() !== '' ? currentNote : 'No additional notes provided',
      };

      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/dental/chart/${selectedChart.id}`, updatedToothData);
      const updatedTooth = response.data;
      const updatedChart = {
        ...selectedChart,
        teeth: [...selectedChart.teeth.filter((t) => t.toothNumber !== selectedTooth), updatedTooth],
      };

      setSelectedChart(updatedChart);
      // Clear the saved tooth data from toothData
      setToothData((prev) => {
        const { [selectedTooth]: _, ...rest } = prev;
        return rest;
      });

      setShowNoteModal(false);
      setCurrentNote('');
      setError('');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save note');
      console.error('Save note error:', err.response?.data);
    }
  };



  const handleCancel = () => {
    setSelectedTooth(null);
    // Optionally clear unsaved changes for the selected tooth
    setToothData((prev) => {
      const { [selectedTooth]: _, ...rest } = prev;
      return rest;
    });
  };

  const filterItems = (items, term) => {
    if (!term) return items;
    return items.filter((item) => item.name.toLowerCase().includes(term.toLowerCase()));
  };

  const exportToPDF = async () => {
    try {
      const pdfDataUri = await generateChartPDF();
      const link = document.createElement('a');
      link.href = pdfDataUri;
      link.download = `DentalChart_${nationalId}_${selectedChart.title.replace(/[^a-z0-9]/gi, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
      link.click();
    } catch (err) {
      setError('Failed to generate PDF. Please try again.');
      const fallbackDoc = new jsPDF();
      fallbackDoc.setFontSize(14);
      fallbackDoc.text('Dental Chart Export Failed', 20, 20);
      fallbackDoc.setFontSize(10);
      fallbackDoc.text(`Patient: ${nationalId}`, 20, 30);
      fallbackDoc.text('Error: Unable to generate full report', 20, 40);
      fallbackDoc.text('Please try again or contact support', 20, 50);
      fallbackDoc.save('DentalChart_Fallback.pdf');
    }
  };

  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [200, 200, 200];
  };

  const getHistoricalRecords = () => {
    if (!selectedChart || !selectedChart.teeth) return [];

    // Return all saved teeth from the current chart
    return selectedChart.teeth
      .filter((tooth) => tooth.procedure !== 'N/A' || tooth.condition !== 'N/A')
      .map((tooth) => ({
        ...tooth,
        chartId: selectedChart.id,
        chartTitle: selectedChart.title,
        chartDate: selectedChart.date,
      }))
      .sort((a, b) => a.toothNumber - b.toothNumber);
  };

  const getCurrentRecords = () => {
    return Object.entries(toothData)
      .map(([toothNumber, data]) => ({
        toothNumber: parseInt(toothNumber),
        procedure: data.procedure?.name || 'N/A',
        condition: data.condition?.name || 'N/A',
        surfaces: data.surfaces || [],
        notes: data.notes || 'No additional notes provided',
      }))
      .filter((record) => record.procedure !== 'N/A' || record.condition !== 'N/A');
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  const ColorReference = () => (
    <motion.div
      variants={item}
      className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-[#0077B6]/10"
    >
      <button
        className="w-full flex items-center justify-between p-3 bg-[#0077B6]/5 rounded-lg hover:bg-[#0077B6]/10 transition-colors"
        onClick={() => setIsColorReferenceOpen(!isColorReferenceOpen)}
      >
        <span className="text-lg font-bold text-[#0077B6]">Color Reference Guide</span>
        <FaChevronDown className={`h-5 w-5 transform ${isColorReferenceOpen ? 'rotate-180' : ''} transition-transform`} />
      </button>
      {isColorReferenceOpen && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          <div>
            <h3 className="text-base font-semibold text-gray-700 mb-3">Procedures</h3>
            {procedureCategories.map((category) => (
              <div key={category.title} className="mb-4">
                <h4 className="text-sm font-medium text-gray-600 mb-2">{category.title}</h4>
                {category.items.map((item) => (
                  <div key={item.id} className="flex items-center mt-2">
                    <span className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: item.color }} />
                    <span className="text-sm text-gray-700">{item.name}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
          <div>
            <h3 className="text-base font-semibold text-gray-700 mb-3">Conditions</h3>
            {conditionCategories.map((category) => (
              <div key={category.title} className="mb-4">
                <h4 className="text-sm font-medium text-gray-600 mb-2">{category.title}</h4>
                {category.items.map((item) => (
                  <div key={item.id} className="flex items-center mt-2">
                    <span className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: item.color }} />
                    <span className="text-sm text-gray-700">{item.name}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </motion.div>
  );

  const ToothSurfaces = ({ position, toothNumber, inModal = false }) => {
    const isUpper = position === 'upper';
    const currentToothData = toothData[toothNumber] || {};
    const chartTooth = selectedChart?.teeth.find((t) => t.toothNumber === toothNumber);
    const selectedSurfaces = Array.isArray(currentToothData.surfaces)
      ? currentToothData.surfaces
      : Array.isArray(chartTooth?.surfaces)
      ? chartTooth.surfaces
      : [];
    const toothColor = currentToothData.procedure?.color || currentToothData.condition?.color || getToothColor(chartTooth || {});

    const getSurfaceColor = (surface) => {
      if (inModal && selectedSurfaces.includes(surface)) {
        return toothColor || '#ADD8E6';
      }
      return selectedSurfaces.includes(surface) && toothColor ? toothColor : 'white';
    };

    return (
      <div className="tooth-surfaces">
        <svg
          width={inModal ? '120' : '80'}
          height={inModal ? '120' : '80'}
          viewBox="0 0 300 300"
          style={{ maxWidth: '100%', height: 'auto' }}
        >
          <rect
            id={`${toothNumber}-occlusal`}
            x="75"
            y="75"
            width="150"
            height="150"
            stroke="black"
            strokeWidth="5"
            fill={getSurfaceColor(isUpper ? 'incisal' : 'occlusal')}
            onClick={inModal ? () => handleSurfaceClick(isUpper ? 'incisal' : 'occlusal', toothNumber) : undefined}
            style={{ cursor: !inModal ? 'default' : 'pointer' }}
          />
          <polygon
            id={`${toothNumber}-buccal`}
            points="0 0 300 0 225 75 75 75"
            stroke="black"
            strokeWidth="5"
            fill={getSurfaceColor('buccal')}
            onClick={inModal ? () => handleSurfaceClick('buccal', toothNumber) : undefined}
            style={{ cursor: !inModal ? 'default' : 'pointer' }}
          />
          <polygon
            id={`${toothNumber}-mesial`}
            points="300 0 300 300 225 225 225 75"
            stroke="black"
            strokeWidth="5"
            fill={getSurfaceColor('mesial')}
            onClick={inModal ? () => handleSurfaceClick('mesial', toothNumber) : undefined}
            style={{ cursor: !inModal ? 'default' : 'pointer' }}
          />
          <polygon
            id={`${toothNumber}-palatal`}
            points="300 300 0 300 75 225 225 225"
            stroke="black"
            strokeWidth="5"
            fill={getSurfaceColor(isUpper ? 'palatal' : 'lingual')}
            onClick={inModal ? () => handleSurfaceClick(isUpper ? 'palatal' : 'lingual', toothNumber) : undefined}
            style={{ cursor: !inModal ? 'default' : 'pointer' }}
          />
          <polygon
            id={`${toothNumber}-distal`}
            points="0 300 0 0 75 75 75 225"
            stroke="black"
            strokeWidth="5"
            fill={getSurfaceColor('distal')}
            onClick={inModal ? () => handleSurfaceClick('distal', toothNumber) : undefined}
            style={{ cursor: !inModal ? 'default' : 'pointer' }}
          />
        </svg>
      </div>
    );
  };

  const Tooth = ({ number, position }) => {
    const chartTooth = selectedChart?.teeth.find((t) => t.toothNumber === number);
    const data = toothData[number] || chartTooth || {};
    const toothColor = data.procedure?.color || data.condition?.color || getToothColor(data);

    return (
      <div className={`tooth-container ${position}`}>
        <ToothSurfaces position={position} toothNumber={number} />
        <div
          className={`tooth ${position} ${selectedTooth === number ? 'selected' : ''}`}
          onClick={() => handleToothClick(number)}
        >
          <img
            src={`${process.env.PUBLIC_URL}/imgs/teeth/${number}.png`}
            alt={`Tooth ${number}`}
            className="tooth-image"
          />
          <span className="tooth-number">{number}</span>
        </div>
      </div>
    );
  };

  const SelectionModal = () => {
    const [openCategory, setOpenCategory] = useState(null);
    if (!selectedTooth) return null;
    const currentCategories = activeTab === 'conditions' ? conditionCategories : procedureCategories;
    const currentToothData = toothData[selectedTooth] || {};
    const isUpper = upperTeethNumbers.includes(selectedTooth);

    const handleConditionProcedureSelect = (item) => {
      setToothData((prev) => ({
        ...prev,
        [selectedTooth]: {
          ...prev[selectedTooth],
          [activeTab === 'conditions' ? 'condition' : 'procedure']: item,
        },
      }));
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto shadow-2xl border border-[#0077B6]/10">
          <div className="p-6 border-b flex justify-between items-center bg-[#0077B6]/5">
            <h2 className="text-xl font-semibold text-[#0077B6]">Tooth {selectedTooth} - Dental Chart</h2>
            <button onClick={handleCancel} className="text-gray-500 hover:text-gray-700">
              <FaTimes className="h-6 w-6" />
            </button>
          </div>
          <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <div className="bg-gray-50 p-4 rounded-lg border border-[#0077B6]/10">
                <h3 className="text-sm font-medium text-[#0077B6] mb-3">Tooth Surfaces</h3>
                <div className="flex justify-center">
                  <ToothSurfaces position={isUpper ? 'upper' : 'lower'} toothNumber={selectedTooth} inModal={true} />
                </div>
                <div className="mt-4 grid grid-cols-2 gap-2">
                  {surfaceTypes.map((surface) => (
                    <button
                      key={surface}
                      onClick={() => handleSurfaceClick(surface, selectedTooth)}
                      className={`px-3 py-1.5 text-xs rounded-lg border ${
                        currentToothData.surfaces?.includes(surface)
                          ? 'bg-[#0077B6]/10 border-[#0077B6] text-[#0077B6]'
                          : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                      } transition-colors`}
                    >
                      {surface.charAt(0).toUpperCase() + surface.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <div className="md:col-span-1">
              <div className="flex border-b mb-4">
                <button
                  className={`flex-1 py-2 text-sm font-medium ${
                    activeTab === 'conditions'
                      ? 'border-b-2 border-[#0077B6] text-[#0077B6]'
                      : 'text-gray-500 hover:text-gray-700'
                  } transition-colors`}
                  onClick={() => setActiveTab('conditions')}
                >
                  Conditions
                </button>
                <button
                  className={`flex-1 py-2 text-sm font-medium ${
                    activeTab === 'procedures'
                      ? 'border-b-2 border-[#0077B6] text-[#0077B6]'
                      : 'text-gray-500 hover:text-gray-700'
                  } transition-colors`}
                  onClick={() => setActiveTab('procedures')}
                >
                  Procedures
                </button>
              </div>
              <div className="relative mb-4">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-full bg-white shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:bg-white sm:text-sm transition-all duration-200"
                />
              </div>
              <div className="max-h-64 overflow-y-auto">
                {currentCategories
                  .filter((category) => filterItems(category.items, searchTerm).length > 0)
                  .map((category) => (
                    <div key={category.title} className="mb-3">
                      <button
                        className="w-full flex items-center justify-between p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        onClick={() => setOpenCategory(openCategory === category.title ? null : category.title)}
                      >
                        <span className="text-sm font-medium text-gray-700">{category.title}</span>
                        <FaChevronDown
                          className={`h-4 w-4 transform ${openCategory === category.title ? 'rotate-180' : ''} transition-transform`}
                        />
                      </button>
                      {openCategory === category.title && (
                        <div className="mt-2 grid grid-cols-1 gap-1">
                          {filterItems(category.items, searchTerm).map((item) => (
                            <button
                              key={item.id}
                              onClick={() => handleConditionProcedureSelect(item)}
                              className={`p-2 rounded-lg border flex items-center ${
                                currentToothData[activeTab === 'conditions' ? 'condition' : 'procedure']?.id === item.id
                                  ? 'border-[#0077B6] bg-[#0077B6]/10 text-[#0077B6]'
                                  : 'border-gray-200 hover:bg-gray-50 text-gray-700'
                              } transition-colors`}
                            >
                              <span className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: item.color }} />
                              <span className="text-sm">{item.name}</span>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
            <div className="md:col-span-1">
              <div className="bg-gray-50 p-4 rounded-lg border border-[#0077B6]/10 h-full flex flex-col">
                <h3 className="text-sm font-medium text-[#0077B6] mb-3">Actions</h3>
                <div className="mt-4 space-y-3">
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Selected:</span>
                    {currentToothData.condition || currentToothData.procedure ? (
                      <span
                        className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                        style={{
                          backgroundColor: `${
                            currentToothData.condition?.color || currentToothData.procedure?.color || '#999'
                          }20`,
                          color: currentToothData.condition?.color || currentToothData.procedure?.color || '#666',
                        }}
                      >
                        {currentToothData.condition?.name || currentToothData.procedure?.name}
                      </span>
                    ) : (
                      ' None'
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">Surfaces:</span>
                    {currentToothData.surfaces?.length > 0 ? (
                      <span className="ml-2">{currentToothData.surfaces.join(', ')}</span>
                    ) : (
                      ' None'
                    )}
                  </div>
                </div>
                <button
                  onClick={() => {
                    setCurrentNote(currentToothData.notes || '');
                    setShowNoteModal(true);
                  }}
                  className="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <FaStickyNote className="h-5 w-5 mr-2" />
                  {currentToothData.notes ? 'Edit Note' : 'Add Note'}
                </button>
                <div className="mt-auto pt-6 flex justify-end space-x-3">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    className="px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg"
                    disabled={!currentToothData.condition && !currentToothData.procedure}
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center"
              >
                Loading dental charts...
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-4 p-4 bg-red-50 border-l-4 border-red-400 text-[#333333]"
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </motion.div>
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="text-center max-w-md bg-white p-8 rounded-xl shadow-sm border border-[#0077B6]/10"
              >
                <div className="text-[#0077B6] mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Error loading dental charts</h3>
                <p className="text-gray-600 mb-6">{error}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium shadow-md"
                >
                  Try Again
                </motion.button>
              </motion.div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-4 p-4 bg-red-50 border-l-4 border-red-400 text-[#333333]"
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </motion.div>
            )}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <ColorReference />
              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-[#0077B6]/10"
              >
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                  <div>
                    <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                      Dental Chart
                    </h1>
                    <p className="text-gray-600">Manage patient dental chart</p>
                  </div>
                  <div className="flex flex-wrap gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={exportToPDF}
                      className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg flex items-center"
                    >
                      <FaFileExport className="h-5 w-5 mr-2" />
                      Export Records
                    </motion.button>
                  </div>
                </div>
                <div className="teeth-chart">
                  <div className="upper-teeth">
                    {upperTeethNumbers.map((number) => (
                      <Tooth key={number} number={number} position="upper" />
                    ))}
                  </div>
                  <div className="lower-teeth">
                    {lowerTeethNumbers.map((number) => (
                      <Tooth key={number} number={number} position="lower" />
                    ))}
                  </div>
                </div>
              </motion.div>
              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-[#0077B6]/10"
              >
                <h2 className="text-xl font-bold text-[#0077B6] mb-4">Current Chart Records</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tooth
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Condition
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Procedure
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Surfaces
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Notes
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getCurrentRecords().length > 0 ? (
                        getCurrentRecords().map((record, index) => (
                          <motion.tr
                            key={`${record.toothNumber}-${index}`}
                            variants={item}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {record.toothNumber}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.condition !== 'N/A' ? (
                                <span
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  style={{
                                    backgroundColor: `${
                                      conditionCategories
                                        .flatMap((c) => c.items)
                                        .find((c) => c.name === record.condition)?.color || '#999'
                                    }20`,
                                    color:
                                      conditionCategories
                                        .flatMap((c) => c.items)
                                        .find((c) => c.name === record.condition)?.color || '#666',
                                  }}
                                >
                                  {record.condition}
                                </span>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.procedure !== 'N/A' ? (
                                <span
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  style={{
                                    backgroundColor: `${
                                      procedureCategories
                                        .flatMap((c) => c.items)
                                        .find((p) => p.name === record.procedure)?.color || '#999'
                                    }20`,
                                    color:
                                      procedureCategories
                                        .flatMap((c) => c.items)
                                        .find((p) => p.name === record.procedure)?.color || '#666',
                                  }}
                                >
                                  {record.procedure}
                                </span>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.surfaces.length > 0 ? (
                                <div className="flex flex-wrap gap-1">
                                  {record.surfaces.map((surface) => (
                                    <span
                                      key={surface}
                                      className="px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-700"
                                    >
                                      {surface}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-500 max-w-xs">
                              {record.notes ? (
                                <button
                                  onClick={() => {
                                    setSelectedTooth(record.toothNumber);
                                    setCurrentNote(record.notes);
                                    setShowNoteModal(true);
                                  }}
                                  className="text-[#0077B6] hover:text-[#0077B6]/80 text-left"
                                >
                                  {record.notes.length > 50 ? `${record.notes.substring(0, 50)}...` : record.notes}
                                </button>
                              ) : (
                                <button
                                  onClick={() => {
                                    setSelectedTooth(record.toothNumber);
                                    setCurrentNote('');
                                    setShowNoteModal(true);
                                  }}
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  Add Note
                                </button>
                              )}
                            </td>
                          </motion.tr>
                        ))
                      ) : (
                        <motion.tr variants={item}>
                          <td
                            colSpan="5"
                            className="px-6 py-4 text-center text-sm text-gray-500"
                          >
                            No unsaved changes in current chart. Select a tooth to add treatment information.
                          </td>
                        </motion.tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </motion.div>
              <motion.div
                variants={container}
                initial="hidden"
                animate="show"
                className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10"
              >
                <h2 className="text-xl font-bold text-[#0077B6] mb-4">Historical Records</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tooth
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Condition
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Procedure
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Surfaces
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Notes
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Chart
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getHistoricalRecords().length > 0 ? (
                        getHistoricalRecords().map((record, index) => (
                          <motion.tr
                            key={`${record.chartId}-${record.toothNumber}-${index}`}
                            variants={item}
                            className="hover:bg-gray-50 transition-colors"
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {record.toothNumber}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.condition !== 'N/A' ? (
                                <span
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  style={{
                                    backgroundColor: `${
                                      conditionCategories
                                        .flatMap((c) => c.items)
                                        .find((c) => c.name === record.condition)?.color || '#999'
                                    }20`,
                                    color:
                                      conditionCategories
                                        .flatMap((c) => c.items)
                                        .find((c) => c.name === record.condition)?.color || '#666',
                                  }}
                                >
                                  {record.condition}
                                </span>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.procedure !== 'N/A' ? (
                                <span
                                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  style={{
                                    backgroundColor: `${
                                      procedureCategories
                                        .flatMap((c) => c.items)
                                        .find((p) => p.name === record.procedure)?.color || '#999'
                                    }20`,
                                    color:
                                      procedureCategories
                                        .flatMap((c) => c.items)
                                        .find((p) => p.name === record.procedure)?.color || '#666',
                                  }}
                                >
                                  {record.procedure}
                                </span>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.surfaces && record.surfaces.length > 0 ? (
                                <div className="flex flex-wrap gap-1">
                                  {(typeof record.surfaces === 'string'
                                    ? record.surfaces.split(', ')
                                    : record.surfaces
                                  ).map((surface) => (
                                    <span
                                      key={surface}
                                      className="px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-700"
                                    >
                                      {surface}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-500 max-w-xs">
                              {record.notes ? (
                                <span>{record.notes.length > 50 ? `${record.notes.substring(0, 50)}...` : record.notes}</span>
                              ) : (
                                'N/A'
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {record.chartTitle}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(record.chartDate).toLocaleDateString()}
                            </td>
                          </motion.tr>
                        ))
                      ) : (
                        <motion.tr variants={item}>
                          <td
                            colSpan="7"
                            className="px-6 py-4 text-center text-sm text-gray-500"
                          >
                            No historical records available.
                          </td>
                        </motion.tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
      <SelectionModal />
      {showNoteModal && (
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        >
          <div className="bg-white rounded-xl w-full max-w-md shadow-2xl border border-[#0077B6]/10">
            <div className="p-6 border-b flex justify-between items-center bg-[#0077B6]/5">
              <h2 className="text-xl font-semibold text-[#0077B6]">
                {toothData[selectedTooth]?.notes ? 'Edit Note' : 'Add Note'} for Tooth {selectedTooth}
              </h2>
              <button
                onClick={() => {
                  setShowNoteModal(false);
                  setCurrentNote('');
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 space-y-6">
              <textarea
                value={currentNote}
                onChange={(e) => setCurrentNote(e.target.value)}
                placeholder="Enter clinical notes for this tooth..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                rows={6}
              />
              <div className="flex justify-end gap-3">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setShowNoteModal(false);
                    setCurrentNote('');
                  }}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 font-medium transition-colors"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSaveNote}
                  className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg"
                >
                  Save Note
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DentalChart;