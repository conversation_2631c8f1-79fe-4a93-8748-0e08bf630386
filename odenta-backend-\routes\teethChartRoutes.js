const express = require('express');
const router = express.Router();
const { createChart, getChartsByPatient, addToothToChart, lockChart, addReview } = require('../controllers/teethChartController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

router.post('/', auth, role('student'), createChart);
router.get('/patient/:nationalId', auth, role('student', 'supervisor', 'admin', 'superadmin'), getChartsByPatient);
router.post('/chart/:chartId', auth, role('student'), addToothToChart);
router.post('/chart/:chartId/review', auth, role('student'), addReview);
router.put('/chart/:chartId/lock', auth, role('student'), lockChart);

module.exports = router;