// Frontend Firebase Configuration for ODenta
import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getAnalytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAaJKFcxipd7SBS-GQK97ZIFr0oBBEKQOU",
  authDomain: "odenta-82359.firebaseapp.com",
  projectId: "odenta-82359",
  storageBucket: "odenta-82359.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:12ccba8515c9648b1d8941",
  measurementId: "G-HFXCKFE42Y"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Google Client ID for OAuth (from environment variable)
export const GOOGLE_CLIENT_ID = process.env.REACT_APP_GOOGLE_CLIENT_ID || "************-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com";

// Initialize Analytics (optional)
export const analytics = getAnalytics(app);

export default app;
