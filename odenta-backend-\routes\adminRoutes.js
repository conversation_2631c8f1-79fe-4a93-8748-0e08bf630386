const express = require('express');
const router = express.Router();
const { getAllStudents, getAllAssistants, getAllSupervisors, getAllPatients, getAllAppointments, getAllTreatmentSheets } = require('../controllers/adminController');
const auth = require('../middleware/auth');
const role = require('../middleware/role');

// Get all students
router.get('/students', auth, role('admin', 'superadmin', 'assistant'), getAllStudents);

// Get all assistants
router.get('/assistants', auth, role('admin', 'superadmin'), getAllAssistants);

// Get all supervisors
router.get('/supervisors', auth, role('admin', 'superadmin'), getAllSupervisors);

// Get all patients
router.get('/patients', auth, role('admin', 'superadmin', 'assistant'), getAllPatients);

// Get all appointments
router.get('/appointments', auth, role('admin', 'superadmin', 'assistant'), getAllAppointments);

// Get all treatment sheets
router.get('/treatment-sheets', auth, role('admin', 'superadmin'), getAllTreatmentSheets);

module.exports = router;