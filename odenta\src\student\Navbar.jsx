import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import { FaSearch, FaHeadset } from 'react-icons/fa';

const Navbar = ({ toggleSidebar }) => {
  const { user, logout } = useAuth();
  const location = useLocation();

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-white shadow-sm border-b border-[rgba(0,119,182,0.1)]"
    >
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleSidebar}
              className="text-[#333333] hover:text-[#0077B6] focus:outline-none"
              aria-label="Toggle sidebar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </motion.button>
            <div className="relative max-w-xs w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search patients..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/support"
                state={{ from: location.pathname }}
                className="flex items-center text-[#333333] hover:text-[#0077B6] transition-colors"
                title="Get Support"
              >
                <FaHeadset className="h-5 w-5" />
                <span className="ml-2 text-sm font-medium hidden md:inline">Support</span>
              </Link>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2"
            >
              <Link to="/profile" className="flex items-center">
                <div className="h-8 w-8 rounded-full bg-[rgba(0,119,182,0.1)] flex items-center justify-center text-[#0077B6]">
                  {user?.name?.charAt(0) || 'S'}
                </div>
                <span className="ml-2 text-sm font-medium text-[#333333] hidden md:inline">
                  {user?.name || 'Student'}
                </span>
              </Link>
            </motion.div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={logout}
              className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-4 py-2 rounded-full font-medium text-sm transition-all duration-300 shadow-md hover:shadow-lg"
            >
              Logout
            </motion.button>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default Navbar;