const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Supervisor = require('./models/Supervisor');
const config = require('./config/config');

// MongoDB connection using environment variables
const connectDB = async () => {
  try {
    await mongoose.connect(config.MONGO_URI, {
      dbName: 'dentlyzer', // Database name
    });
    console.log('✅ MongoDB connected');
    console.log(`🌐 Environment: ${config.NODE_ENV}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedSupervisor = async () => {
  await connectDB();
  
  try {
    // Check if supervisor already exists
    const existingSupervisor = await Supervisor.findOne({ email: '<EMAIL>' });
    if (existingSupervisor) {
      console.log('Supervisor already exists:', existingSupervisor.email);
      return;
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('TestPassword123!', salt);

    // Create new supervisor
    const supervisor = new Supervisor({
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Dr. Test Supervisor',
      role: 'supervisor',
      university: '',
    });

    await supervisor.save();
    console.log('Supervisor seeded successfully:', supervisor.email);
  } catch (error) {
    console.error('Error seeding supervisor:', error);
  } finally {
    mongoose.disconnect();
  }
};

seedSupervisor();