# ODenta Final Seed Documentation

## Overview
The `finalSeed.js` file provides comprehensive clinical data seeding for the ODenta university dental management system. This seed file **preserves existing user accounts** and only populates clinical data (patients, appointments, reviews, etc.) with realistic information.

## What Gets Seeded

### 🏥 Clinical Data Only
**Note: This seed preserves all existing user accounts and only adds clinical data.**

### 🏥 Clinical Data
- **6 Patients** with complete medical histories
- **5 Appointments** (various types and statuses)
- **4 Procedure Requests** (pending, approved, rejected)
- **3 Lab Requests** (university and outside lab)
- **3 Reviews** (student work evaluations)
- **4 News Items** (university announcements)

## How to Run

### Prerequisites
1. Ensure MongoDB is running
2. Environment variables are properly configured
3. All dependencies are installed (`npm install`)
4. **IMPORTANT: User accounts must already exist in the database**

### Running the Seed

```bash
# Navigate to backend directory
cd dentlyzer-backend

# Run the clinical data seed (preserves existing accounts)
npm run final-seed

# Alternative: Run directly with node
node finalSeed.js
```

## Account Requirements

**This seed file requires existing user accounts in the database.** It will:
- ✅ **Preserve** all existing user accounts
- ✅ **Use** existing students, supervisors, and assistants for data relationships
- ❌ **Not create** any new user accounts
- ✅ **Clear and repopulate** only clinical data (patients, appointments, etc.)

The seed will automatically detect and use your existing accounts to create realistic relationships between users and clinical data.

## Sample Data Features

### Patients
- Diverse demographics (ages 25-42)
- Various medical conditions and medications
- Different chief complaints and occupations
- Complete contact information

### Appointments
- Different appointment types (Consultation, Cleaning, Treatment, Surgery)
- Various statuses (pending, completed)
- Realistic scheduling across multiple days
- Student-patient assignments

### Procedure Requests
- All 6 dental specialties represented:
  - Periodontics
  - Endodontics
  - Oral Surgery
  - Fixed Prosthodontics
  - Removable Prosthodontics
  - Operative
- Different request statuses (pending, approved, rejected)
- Assistant responses with notes

### Lab Requests
- University and outside lab types
- Various statuses (pending, approved, completed)
- Realistic lab work descriptions
- Assistant management workflow

### Reviews
- Student work evaluations
- Supervisor ratings and feedback
- Multi-step review processes
- Quality and interaction scores

### News
- University-specific announcements
- Global dental education news
- Bilingual content (English/Arabic)
- Equipment and technology updates

## Testing Scenarios

After running the seed, you can test:

1. **Student Login** - Access patient management, appointments, lab requests
2. **Supervisor Login** - Review student work, provide feedback
3. **Admin Login** - View analytics, manage university data
4. **Assistant Login** - Handle procedure/lab requests, patient management
5. **Cross-role Interactions** - Procedure approvals, review workflows

## Database Reset

The seed file automatically clears existing **clinical data only** before seeding. User accounts are preserved. To reset and re-seed clinical data:

```bash
npm run final-seed
```

## Notes

- **User accounts are preserved** - no passwords are modified
- Clinical data is assigned to existing students dynamically
- University affiliation uses existing student university settings
- Dates are set to realistic future dates for testing
- Medical information includes realistic conditions and medications
- All data follows the application's validation schemas
- Relationships between users and clinical data are created automatically

## Troubleshooting

If seeding fails:
1. Check MongoDB connection
2. Verify environment variables
3. Ensure all required models are properly imported
4. Check console output for specific error messages

The seed script provides detailed console output showing progress and any errors encountered.
