const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

// Get all patients for a student
const getMyPatients = async (req, res) => {
  try {
    console.log('Getting patients for student:', req.user);

    // Use studentId instead of _id
    const patients = await FirestoreHelpers.find(
      COLLECTIONS.PATIENTS,
      { field: 'drId', operator: '==', value: req.user.studentId }
    );

    console.log(`Found ${patients.length} patients for student ${req.user.studentId}`);
    res.json(patients);
  } catch (error) {
    console.error('Error getting patients:', error);
    res.status(500).json({ message: error.message });
  }
};

// Messaging (Socket.io integration - basic emit)
const sendMessage = (io) => async (req, res) => {
  const { receiverId, message } = req.body;
  try {
    io.to(receiverId).emit('message', { sender: req.user.id, message });
    res.json({ message: 'Message sent' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Analytics (e.g., patient count, completed appointments)
const getAnalytics = async (req, res) => {
  try {
    console.log('Getting analytics for student:', req.user.studentId);

    const patientCount = await FirestoreHelpers.count(
      COLLECTIONS.PATIENTS,
      { field: 'drId', operator: '==', value: req.user.studentId }
    );

    // Count completed appointments for this student
    const allAppointments = await FirestoreHelpers.find(
      COLLECTIONS.APPOINTMENTS,
      { field: 'doctor', operator: '==', value: req.user.studentId }
    );
    const completedAppointments = allAppointments.filter(
      appointment => appointment.doctorModel === 'Student' && appointment.status === 'completed'
    ).length;

    console.log(`Analytics for student ${req.user.studentId}: ${patientCount} patients, ${completedAppointments} completed appointments`);
    res.json({ patientCount, completedAppointments });
  } catch (error) {
    console.error('Error getting analytics:', error);
    res.status(500).json({ message: error.message });
  }
};


const getAllStudents = async (req, res) => {
  try {
    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'role', operator: '==', value: 'student' }
    );

    if (!students || students.length === 0) {
      return res.status(404).json({ message: 'No students found' });
    }

    // Return only necessary fields
    const studentsData = students.map(student => ({
      id: student.id,
      name: student.name,
      studentId: student.studentId
    }));

    res.json(studentsData);
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({
      message: 'Server error while fetching students',
      error: error.message
    });
  }
};

// Get students by university
const getStudentsByUniversity = async (req, res) => {
  try {
    const { university } = req.params;
    
    if (!university) {
      return res.status(400).json({ message: 'University parameter is required' });
    }

    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'university', operator: '==', value: university }
    );

    if (!students || students.length === 0) {
      return res.status(404).json({ message: 'No students found for this university' });
    }

    // Return only necessary fields
    const studentsData = students.map(student => ({
      id: student.id,
      name: student.name,
      studentId: student.studentId,
      email: student.email
    }));

    res.json(studentsData);
  } catch (error) {
    console.error('Error fetching students by university:', error);
    res.status(500).json({
      message: 'Server error while fetching students',
      error: error.message
    });
  }
};

module.exports = { getMyPatients, sendMessage, getAnalytics, getAllStudents, getStudentsByUniversity };