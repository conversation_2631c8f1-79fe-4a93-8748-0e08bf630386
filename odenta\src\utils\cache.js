/**
 * Simple in-memory cache with TTL (Time To Live) support
 * Helps reduce database reads by caching frequently accessed data
 */

class Cache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  /**
   * Set a value in cache with optional TTL
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (default: 5 minutes)
   */
  set(key, value, ttl = 5 * 60 * 1000) {
    // Clear existing timer if any
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Set the value
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  /**
   * Get a value from cache
   * @param {string} key - Cache key
   * @returns {any|null} Cached value or null if not found/expired
   */
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * Check if a key exists in cache and is not expired
   * @param {string} key - Cache key
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null;
  }

  /**
   * Delete a key from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }
  }

  /**
   * Clear all cache
   */
  clear() {
    // Clear all timers
    this.timers.forEach(timer => clearTimeout(timer));
    
    // Clear cache and timers
    this.cache.clear();
    this.timers.clear();
  }

  /**
   * Get cache statistics
   * @returns {object} Cache stats
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Get or set pattern - if key exists return it, otherwise execute function and cache result
   * @param {string} key - Cache key
   * @param {function} fn - Function to execute if cache miss
   * @param {number} ttl - Time to live in milliseconds
   * @returns {Promise<any>} Cached or fresh value
   */
  async getOrSet(key, fn, ttl = 5 * 60 * 1000) {
    const cached = this.get(key);
    
    if (cached !== null) {
      console.log(`Cache HIT for key: ${key}`);
      return cached;
    }

    console.log(`Cache MISS for key: ${key}`);
    const value = await fn();
    this.set(key, value, ttl);
    return value;
  }
}

// Create a singleton instance
const cache = new Cache();

// Cache key generators for common data types
export const CacheKeys = {
  PATIENTS: (page = 1, limit = 20, search = '', studentFilter = 'all', sortBy = 'registrationDate', sortOrder = 'desc') => 
    `patients_${page}_${limit}_${search}_${studentFilter}_${sortBy}_${sortOrder}`,
  
  STUDENTS: (page = 1, limit = 50, search = '') => 
    `students_${page}_${limit}_${search}`,
  
  PROCEDURE_REQUESTS: () => 'procedure_requests',
  
  PATIENT_DETAILS: (nationalId) => `patient_details_${nationalId}`,
  
  PATIENT_APPOINTMENTS: (nationalId) => `patient_appointments_${nationalId}`,
  
  USER_PROFILE: (userId) => `user_profile_${userId}`
};

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 2 * 60 * 1000,      // 2 minutes
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
};

export default cache;
