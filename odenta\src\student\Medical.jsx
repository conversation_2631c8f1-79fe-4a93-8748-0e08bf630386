import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFileMedical, FaProcedures, FaUser, FaEdit, FaSave, FaTimes } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import PatientNav from './PatientNav';

const Medical = () => {
  const { nationalId } = useParams();
  const { token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  const [formData, setFormData] = useState({
    fullName: '',
    nationalId: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: '',
    },
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        console.log('Medical component - Fetching patient data for:', nationalId);
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`);
        const patient = response.data;
        console.log('Medical component - Fetched patient data:', patient);
        console.log('Medical component - Patient medicalInfo:', patient.medicalInfo);
        
        setPatientData(patient);
        setFormData({
          fullName: patient.fullName || '',
          nationalId: patient.nationalId || '',
          phoneNumber: patient.phoneNumber || '',
          gender: patient.gender || '',
          age: patient.age ? parseInt(patient.age) : '',
          address: patient.address || '',
          occupation: patient.occupation || '',
          medicalInfo: {
            chronicDiseases: patient.medicalInfo?.chronicDiseases || [],
            recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',
            currentMedications: patient.medicalInfo?.currentMedications || '',
            chiefComplaint: patient.medicalInfo?.chiefComplaint || '',
          },
        });
      } catch (err) {
        console.error('Medical component - Fetch error:', err.response?.status, err.response?.data);
        const message = err.response?.status === 401
          ? 'Unauthorized: Please log in again.'
          : err.response?.data?.message || 'Failed to load patient data';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [nationalId, token]);

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('medical-')) {
      const medicalField = name.replace('medical-', '');
      setFormData(prev => ({
        ...prev,
        medicalInfo: { ...prev.medicalInfo, [medicalField]: value },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleChronicDiseaseChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => {
      const updatedDiseases = checked
        ? [...prev.medicalInfo.chronicDiseases, value]
        : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value);

      return {
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chronicDiseases: updatedDiseases },
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      console.log('Medical component - Submitting patient update:', formData);
      
      // Prepare the update data with only the fields that have values
      const updateData = {};
      
      // Only include fields that have values
      if (formData.fullName) updateData.fullName = formData.fullName;
      if (formData.phoneNumber) updateData.phoneNumber = formData.phoneNumber;
      if (formData.gender) updateData.gender = formData.gender;
      if (formData.age) updateData.age = parseInt(formData.age);
      if (formData.address !== undefined) updateData.address = formData.address;
      if (formData.occupation !== undefined) updateData.occupation = formData.occupation;
      
      // Include medicalInfo if it has any data
      if (formData.medicalInfo) {
        updateData.medicalInfo = {
          chronicDiseases: formData.medicalInfo.chronicDiseases || [],
          recentSurgicalProcedures: formData.medicalInfo.recentSurgicalProcedures || '',
          currentMedications: formData.medicalInfo.currentMedications || '',
          chiefComplaint: formData.medicalInfo.chiefComplaint || ''
        };
      }
      
      // Include drId if available
      if (patientData.drId) {
        updateData.drId = patientData.drId;
      }
      
      console.log('Medical component - Update data being sent:', updateData);
      
      // Update the patient data with the new info
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`,
        updateData,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Medical component - Update response:', response.data);

      if (response.data && response.data.patient) {
        setPatientData(response.data.patient);
        setIsEditing(false);
        setError('');
      } else {
        throw new Error('Invalid patient data received from server');
      }
    } catch (err) {
      console.error('Medical component - Update error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to update patient data');
    }
  };

  const chronicDiseaseOptions = [
    'Diabetes',
    'Hypertension',
    'Heart Disease',
    'Asthma',
    'Thyroid Disorder',
    'Kidney Disease',
    'Liver Disease',
    'Arthritis',
    'Cancer',
    'Other'
  ];

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <PatientNav />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (error || !patientData) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <PatientNav />
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
            <div className="max-w-7xl mx-auto">
              <div className="text-red-500">{error || 'Patient data not found'}</div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <PatientNav />
        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[#0077B6]/5 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-4 p-4 bg-red-50 border-l-4 border-red-400 text-[#333333]"
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h1 className="text-3xl font-bold text-[#0077B6] mb-2">Medical Information</h1>
                  <p className="text-gray-600">Manage patient medical history and information</p>
                </div>
                <button
                  onClick={handleEditToggle}
                  className="flex items-center px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-[#005A8B] transition-colors"
                >
                  {isEditing ? <FaTimes className="h-4 w-4 mr-2" /> : <FaEdit className="h-4 w-4 mr-2" />}
                  {isEditing ? 'Cancel' : 'Edit'}
                </button>
              </div>

              {isEditing ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10"
                >
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Personal Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Full Name</label>
                          <input
                            type="text"
                            name="fullName"
                            value={formData.fullName}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">National ID</label>
                          <input
                            type="text"
                            name="nationalId"
                            value={formData.nationalId}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] bg-gray-50"
                            disabled
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                          <input
                            type="text"
                            name="phoneNumber"
                            value={formData.phoneNumber}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Gender</label>
                          <select
                            name="gender"
                            value={formData.gender}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          >
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Age</label>
                          <input
                            type="number"
                            name="age"
                            value={formData.age}
                            onChange={handleInputChange}
                            min="0"
                            max="120"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Address</label>
                          <input
                            type="text"
                            name="address"
                            value={formData.address}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Occupation</label>
                          <input
                            type="text"
                            name="occupation"
                            value={formData.occupation}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Medical Information</h3>
                      <div>
                        <h4 className="text-md font-medium text-gray-700 mb-2">Chronic Diseases</h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {chronicDiseaseOptions.map(disease => (
                            <div key={disease} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`disease-${disease}`}
                                value={disease}
                                checked={formData.medicalInfo.chronicDiseases.includes(disease)}
                                onChange={handleChronicDiseaseChange}
                                className="h-4 w-4 text-[#0077B6] focus:ring-[#0077B6] border-gray-300 rounded"
                              />
                              <label htmlFor={`disease-${disease}`} className="ml-2 text-sm text-gray-700">
                                {disease}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Recent Surgical Procedures</h4>
                        <textarea
                          name="medical-recentSurgicalProcedures"
                          value={formData.medicalInfo.recentSurgicalProcedures}
                          onChange={handleInputChange}
                          rows="3"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          placeholder="List any recent surgical procedures"
                        />
                      </div>

                      <div className="mt-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Current Medications</h4>
                        <textarea
                          name="medical-currentMedications"
                          value={formData.medicalInfo.currentMedications}
                          onChange={handleInputChange}
                          rows="3"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          placeholder="List any current medications"
                        />
                      </div>

                      <div className="mt-4">
                        <h4 className="text-md font-medium text-gray-700 mb-2">Chief Complaint</h4>
                        <textarea
                          name="medical-chiefComplaint"
                          value={formData.medicalInfo.chiefComplaint}
                          onChange={handleInputChange}
                          rows="3"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                          placeholder="Describe the patient's main complaint or reason for visit"
                          required
                        />
                      </div>
                    </div>

                    <div className="flex justify-end gap-3">
                      <button
                        type="button"
                        onClick={handleEditToggle}
                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:from-[#0077B6]/90 hover:to-[#20B2AA]/90 font-medium transition-colors shadow-md hover:shadow-lg flex items-center"
                      >
                        <FaSave className="h-4 w-4 mr-2" />
                        Save Changes
                      </button>
                    </div>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="space-y-6"
                >
                  <div className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-lg font-semibold text-[#0077B6] flex items-center">
                        <FaUser className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Personal Information
                      </h2>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Full Name</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.fullName}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">National ID</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.nationalId}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Phone Number</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.phoneNumber}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Gender</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.gender}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Age</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.age || 'Not provided'}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.address || 'Not provided'}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Occupation</h3>
                        <p className="mt-1 text-sm text-gray-900">{patientData.occupation || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-lg font-semibold text-[#0077B6] flex items-center">
                        <FaFileMedical className="h-5 w-5 mr-2 text-[#0077B6]" />
                        Medical History
                      </h2>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Chronic Diseases</h3>
                        <p className="mt-1 text-sm text-gray-900">
                          {patientData.medicalInfo?.chronicDiseases?.length > 0
                            ? patientData.medicalInfo.chronicDiseases.join(', ')
                            : 'None'}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Recent Surgical Procedures</h3>
                        <p className="mt-1 text-sm text-gray-900">
                          {patientData.medicalInfo?.recentSurgicalProcedures || 'None'}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Current Medications</h3>
                        <p className="mt-1 text-sm text-gray-900">
                          {patientData.medicalInfo?.currentMedications || 'None'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-xl shadow-sm p-6 border border-[#0077B6]/10">
                    <h2 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                      <FaProcedures className="h-5 w-5 mr-2 text-[#0077B6]" />
                      Chief Complaint
                    </h2>
                    <p className="text-sm text-gray-900">
                      {patientData.medicalInfo?.chiefComplaint || 'None'}
                    </p>
                  </div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Medical; 