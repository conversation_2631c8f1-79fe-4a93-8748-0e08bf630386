import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaCalendarAlt, FaSync } from 'react-icons/fa';
import { saveAs } from 'file-saver';
import { formatDate, parseDate, isToday, isFuture } from '../utils/dateUtils';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const Appointments = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [students, setStudents] = useState([]);
  const [dayFilter, setDayFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('');
  const [universityFilter, setUniversityFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const navigate = useNavigate();
  const { user, token } = useAuth();

  const fetchData = async () => {
    if (!user || !token) {
      setError('Please log in to view appointments.');
      setLoading(false);
      return;
    }

    if (!user.university) {
      setError('User profile incomplete. Missing university information.');
      setLoading(false);
      return;
    }

    try {
      setRefreshing(true);
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const [appointmentsRes, studentsRes] = await Promise.all([
        axios.get(`${process.env.REACT_APP_API_URL}/api/admin/appointments?university=${encodeURIComponent(user.university)}`, config),
        axios.get(`${process.env.REACT_APP_API_URL}/api/admin/students?university=${encodeURIComponent(user.university)}`, config),
      ]);

      setAppointments(appointmentsRes.data || []);
      setStudents(studentsRes.data || []);
      setError('');

      // Remove the error message for no appointments found
      // if (appointmentsRes.data.length === 0) {
      //   setError('No appointments found for your university.');
      // }

      console.log(`Fetched ${appointmentsRes.data.length} appointments for university: ${user.university}`);
    } catch (err) {
      console.error('Fetch error:', err.response?.data || err.message);
      const errorMessage =
        err.response?.status === 404
          ? 'Appointments endpoint not found.'
          : err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load appointments';
      setError(errorMessage);
      if (err.response?.status === 401) navigate('/login');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh appointments data
  const handleRefresh = () => {
    fetchData();
  };

  useEffect(() => {
    fetchData();
  }, [user, token, navigate]);

  const downloadAppointments = () => {
    const headers = ['Date', 'Time', 'Patient', 'Procedure', 'Status', 'Student', 'Student ID', 'University'];
    const data = filteredAppointments.map((appt) => {
      // Get student information
      let studentName = 'Not Assigned';
      let studentId = '';

      if (appt.studentName) {
        studentName = appt.studentName;
        studentId = appt.studentId || '';
      } else if (appt.doctorModel === 'Student' && appt.doctor) {
        // Find student by studentId (stored in doctor field)
        const student = students.find(s => s.studentId === appt.doctor);

        if (student) {
          studentName = student.name;
          studentId = student.studentId;
        } else {
          console.log(`No student found with studentId ${appt.doctor} for CSV export`);
        }
      } else if (appt.doctorModel === 'Dentist') {
        studentName = 'Dentist';
      }

      return [
        formatDate(appt.date),
        appt.time || 'N/A',
        appt.patient?.fullName || appt.fullName || 'Unknown',
        appt.treatment || appt.type || 'N/A',
        appt.status,
        studentName,
        studentId,
        appt.university || user.university || 'N/A',
      ];
    });

    const csvContent = [headers.join(','), ...data.map((row) => row.join(','))].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `appointments_${user.university}_${new Date().toISOString().split('T')[0]}.csv`);
  };

  const filterAppointments = () => {
    let filtered = appointments;

    // Filter by date
    if (dayFilter === 'today') {
      filtered = filtered.filter((a) => {
        const apptDate = new Date(a.date);
        const todayDate = new Date();
        return apptDate.toDateString() === todayDate.toDateString();
      });
    } else if (dayFilter === 'tomorrow') {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      filtered = filtered.filter((a) => {
        const apptDate = new Date(a.date);
        return apptDate.toDateString() === tomorrow.toDateString();
      });
    } else if (dayFilter === 'week') {
      const weekEnd = new Date();
      weekEnd.setDate(weekEnd.getDate() + 7);
      filtered = filtered.filter((a) => {
        const apptDate = new Date(a.date);
        return apptDate >= new Date() && apptDate <= weekEnd;
      });
    }

    // Filter by status
    if (statusFilter) {
      filtered = filtered.filter((a) => a.status === statusFilter);
    }

    // Filter by university
    if (universityFilter) {
      filtered = filtered.filter((a) => a.university === universityFilter);
    }

    // Sort by date and time
    return filtered.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      if (dateA.getTime() !== dateB.getTime()) return dateA - dateB;
      return (a.time || '').localeCompare(b.time || '');
    });
  };

  // Get unique universities from appointments
  const getUniqueUniversities = () => {
    const universities = appointments
      .map(appt => appt.university)
      .filter(university => university); // Filter out undefined/null values

    return [...new Set(universities)];
  };

  const filteredAppointments = filterAppointments();

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>Appointments</h1>
                <p className={`text-[${websiteColorPalette.text}]`}>Manage appointments in your university</p>
              </div>
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-4 sm:p-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 sm:mb-6 gap-4">
                    <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] flex items-center`}>
                      <FaCalendarAlt className={`h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[${websiteColorPalette.primary}]`} />
                      Appointment List
                    </h2>
                    <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                      <div className="flex flex-wrap gap-2">
                        <select
                          value={dayFilter}
                          onChange={(e) => setDayFilter(e.target.value)}
                          className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                        >
                          <option value="all">All Dates</option>
                          <option value="today">Today</option>
                          <option value="tomorrow">Tomorrow</option>
                          <option value="week">This Week</option>
                        </select>
                        <select
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                          className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                        >
                          <option value="">All Statuses</option>
                          <option value="pending">Pending</option>
                          <option value="completed">Completed</option>
                          <option value="cancelled">Cancelled</option>
                        </select>
                        {getUniqueUniversities().length > 1 && (
                          <select
                            value={universityFilter}
                            onChange={(e) => setUniversityFilter(e.target.value)}
                            className={`px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[${websiteColorPalette.primary}] focus:border-[${websiteColorPalette.primary}] bg-white text-xs sm:text-sm`}
                          >
                            <option value="">All Universities</option>
                            {getUniqueUniversities().map((uni) => (
                              <option key={uni} value={uni}>
                                {uni}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleRefresh}
                          disabled={refreshing}
                          className={`px-3 sm:px-4 py-2 rounded-lg flex items-center text-white font-medium text-xs sm:text-sm ${
                            refreshing ? 'bg-blue-400 cursor-not-allowed' : `bg-[${websiteColorPalette.primary}] hover:bg-blue-700`
                          }`}
                        >
                          <FaSync className={`mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                          {refreshing ? 'Refreshing...' : 'Refresh'}
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={downloadAppointments}
                          className={`px-3 sm:px-4 py-2 bg-gradient-to-r from-[${websiteColorPalette.primary}] to-[${websiteColorPalette.secondary}] text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg text-xs sm:text-sm`}
                        >
                          Download CSV
                        </motion.button>
                      </div>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                          <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">University</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredAppointments.length === 0 ? (
                          <tr>
                            <td colSpan="7" className="px-3 sm:px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <div className={`bg-blue-50 rounded-full p-4 mb-4`}>
                                  <FaCalendarAlt className={`h-8 w-8 sm:h-10 sm:w-10 text-[${websiteColorPalette.primary}]`} />
                                </div>
                                <h3 className="text-base sm:text-lg font-medium text-gray-900">No appointments found</h3>
                                <p className="mt-1 text-gray-500 text-center text-sm">No appointments scheduled for this period in your university.</p>
                                <p className={`text-xs sm:text-sm text-[${websiteColorPalette.primary}] mt-2`}>Try changing the filters or check back later.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          filteredAppointments.map((appt) => (
                            <motion.tr
                              key={appt._id}
                              variants={item}
                              className="hover:bg-gray-50"
                            >
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                {formatDate(appt.date, {
                                  weekday: 'short',
                                  month: 'short',
                                  day: 'numeric',
                                })}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{appt.time || 'N/A'}</td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.patient?.fullName || appt.fullName || 'Unknown'}
                                {appt.patient?.nationalId && <div className="text-xs text-gray-400">ID: {appt.patient.nationalId}</div>}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.treatment || appt.type || 'N/A'}
                                {appt.chiefComplaint && <div className="text-xs text-gray-400 mt-1">Chief complaint: {appt.chiefComplaint}</div>}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                                <span
                                  className={`px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    appt.status === 'completed'
                                      ? `bg-green-100 text-[${websiteColorPalette.accent}]`
                                      : appt.status === 'cancelled'
                                      ? 'bg-red-100 text-red-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {appt.status}
                                </span>
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {(() => {
                                  // First check if studentName is already in the appointment data
                                  if (appt.studentName) {
                                    return (
                                      <>
                                        <div className="font-medium">{appt.studentName}</div>
                                        {appt.studentId && <div className="text-xs text-gray-400">ID: {appt.studentId}</div>}
                                      </>
                                    );
                                  }

                                  // If not, try to find the student by doctor field (which contains studentId)
                                  if (appt.doctorModel === 'Student' && appt.doctor) {
                                    // Find student by studentId (stored in doctor field)
                                    const student = students.find(s => s.studentId === appt.doctor);

                                    if (student) {
                                      return (
                                        <>
                                          <div className="font-medium">{student.name}</div>
                                          <div className="text-xs text-gray-400">ID: {student.studentId}</div>
                                        </>
                                      );
                                    } else {
                                      console.log(`No student found with studentId ${appt.doctor}`);
                                    }
                                  }

                                  // If it's a dentist, show that
                                  if (appt.doctorModel === 'Dentist') {
                                    return 'Dentist';
                                  }

                                  // Default case
                                  return 'Not Assigned';
                                })()}
                              </td>
                              <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                {appt.university || user.university || 'N/A'}
                              </td>
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Appointments;