# 🚀 Complete ODenta Setup Guide

## Current Issues & Solutions

### Issue 1: Google Sign-In Client ID Missing
**Error**: `[GSI_LOGGER]: Missing required parameter: client_id`

**Solution**: Set up Google OAuth credentials

### Issue 2: Firebase Authentication for Seeding
**Error**: Firebase Admin SDK needs proper credentials

**Solution**: Get Firebase service account key

---

## 🎯 **STEP-BY-STEP SOLUTION**

### Step 1: Set up Google OAuth (Fix GSI_LOGGER error)

1. **Go to Google Cloud Console**:
   - Visit: https://console.cloud.google.com/apis/credentials?project=odenta-82359

2. **Create OAuth 2.0 Client ID**:
   - Click "Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: "Web application"
   - Name: "ODenta Frontend"
   - Authorized origins: 
     - `http://localhost:3000`
     - `http://localhost:3001` (if needed)
   - Authorized redirect URIs:
     - `http://localhost:3000/auth/callback`

3. **Copy the Client ID** and add to your frontend environment variables:
   ```env
   REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id_here
   ```

### Step 2: Get Firebase Service Account Key

1. **Go to Firebase Console**:
   - Visit: https://console.firebase.google.com/project/odenta-82359/settings/serviceaccounts/adminsdk

2. **Generate Private Key**:
   - Click "Generate new private key"
   - Download the JSON file
   - Keep it secure!

3. **Add to Backend .env**:
   ```env
   FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"odenta-82359",...}
   ```
   (Copy the entire JSON content as one line)

### Step 3: Enable Firebase Authentication

1. **Go to Authentication**:
   - Visit: https://console.firebase.google.com/project/odenta-82359/authentication/providers

2. **Enable Providers**:
   - ✅ Email/Password
   - ✅ Google (use the OAuth client ID from Step 1)

3. **Add Authorized Domains**:
   - `localhost` (for development)
   - Your production domain (when deploying)

---

## 🔥 **QUICK START (Alternative)**

If you want to get started immediately without Google Auth:

### Option A: Manual Account Creation

1. **Go to Firebase Authentication**:
   - Visit: https://console.firebase.google.com/project/odenta-82359/authentication/users

2. **Add Users Manually**:
   ```
   Email: <EMAIL>
   Password: Aaiu-2025
   
   Email: <EMAIL>  
   Password: STaiu-2025
   
   Email: <EMAIL>
   Password: SVaiu-2025
   ```

3. **Test Login**: Use these credentials in your frontend

### Option B: Disable Google Auth Temporarily

In your frontend, comment out Google Auth initialization:
```javascript
// Temporarily disable Google Auth
// import { GoogleAuthProvider } from "firebase/auth";
// export const googleProvider = new GoogleAuthProvider();
```

---

## 📋 **COMPLETE ACCOUNT LIST**

Once authentication is set up, these accounts will be available:

### 👨‍🎓 Students
- `<EMAIL>` / `STaiu-2025` (Ahmed Hassan - AIU001)
- `<EMAIL>` / `STaiu-2025` (Sara Mohamed - AIU002)
- `<EMAIL>` / `STaiu-2025` (Omar Ali - AIU003)

### 👨‍⚕️ Supervisors  
- `<EMAIL>` / `SVaiu-2025` (Dr. Mahmoud Farouk)
- `<EMAIL>` / `SVaiu-2025` (Dr. Fatma Abdel Rahman)

### 👨‍💼 Admins
- `<EMAIL>` / `Aaiu-2025` (Dr. Khaled Ibrahim)
- `<EMAIL>` / `Aaiu-2025` (Dr. Nadia Saleh)

### 👑 Superadmins
- `<EMAIL>` / `superadmin-2025`
- `<EMAIL>` / `superadmin-2025`

### 👩‍💼 Assistants
- `<EMAIL>` / `ASTaiu-2025` (Mona Youssef)
- `<EMAIL>` / `ASTaiu-2025` (Heba Mostafa)

---

## 🚀 **DEPLOYMENT TO FIREBASE**

Once everything is working locally:

### Firebase Functions Deployment

1. **Install Firebase CLI**:
   ```bash
   npm install -g firebase-tools
   ```

2. **Login and Initialize**:
   ```bash
   firebase login
   firebase init functions
   ```

3. **Deploy**:
   ```bash
   firebase deploy --only functions
   ```

Your API will be available at:
`https://us-central1-odenta-82359.cloudfunctions.net/api`

### Update Frontend API URL

In your frontend, update the API base URL:
```javascript
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://us-central1-odenta-82359.cloudfunctions.net/api'
  : 'http://localhost:5002/api';
```

---

## ✅ **CURRENT STATUS**

- ✅ **Backend**: Fully migrated to Firebase
- ✅ **Database**: Firestore connected and configured  
- ✅ **Routes**: All API endpoints working
- ✅ **Models**: All Firebase models created
- ✅ **Server**: Ready to run (just need to fix port conflicts)
- ⚠️ **Authentication**: Needs Google OAuth client ID
- ⚠️ **Seeding**: Needs Firebase service account key

---

## 🎯 **NEXT IMMEDIATE ACTIONS**

1. **Fix Google OAuth**: Get client ID from Google Cloud Console
2. **Get Firebase Key**: Download service account JSON
3. **Start Server**: On a free port (5002)
4. **Test Login**: With manual accounts or seeded accounts
5. **Deploy**: To Firebase Functions

The backend is 100% ready - just need these authentication credentials! 🚀
