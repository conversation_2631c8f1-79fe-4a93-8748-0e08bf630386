const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');

const accountSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6),
  name: Joi.string().required(),
  role: Joi.string().valid('student', 'supervisor', 'admin', 'assistant').required(),
  studentId: Joi.string().when('role', { is: 'student', then: Joi.required(), otherwise: Joi.forbidden() }),
  universityId: Joi.string().when('role', {
    is: Joi.string().valid('student', 'supervisor', 'admin'),
    then: Joi.required(),
    otherwise: Joi.when('role', {
      is: 'assistant',
      then: Joi.when('affiliationType', {
        is: 'university',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      otherwise: Joi.forbidden()
    })
  }),
});

const googleAccountSchema = Joi.object({
  email: Joi.string().email().required(),
  name: Joi.string().required(),
  role: Joi.string().valid('student', 'supervisor', 'admin', 'assistant').required(),
  studentId: Joi.string().when('role', { is: 'student', then: Joi.required(), otherwise: Joi.forbidden() }),
  universityId: Joi.string().when('role', {
    is: Joi.string().valid('student', 'supervisor', 'admin'),
    then: Joi.required(),
    otherwise: Joi.when('role', {
      is: 'assistant',
      then: Joi.when('affiliationType', {
        is: 'university',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      otherwise: Joi.forbidden()
    })
  }),
  isGoogleAccount: Joi.boolean().default(true),
  dentistId: Joi.string().when('role', { is: 'assistant', then: Joi.optional(), otherwise: Joi.forbidden() }),
  affiliationType: Joi.string().valid('university', 'dentist').when('role', { is: 'assistant', then: Joi.required(), otherwise: Joi.optional() }),
});

const updateAccountSchema = Joi.object({
  email: Joi.string().email(),
  password: Joi.string().min(6),
  name: Joi.string(),
  universityId: Joi.string().when('role', {
    is: Joi.string().valid('student', 'supervisor', 'admin'),
    then: Joi.required(),
    otherwise: Joi.when('role', {
      is: 'assistant',
      then: Joi.when('affiliationType', {
        is: 'university',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      otherwise: Joi.forbidden()
    })
  }),
  dentistId: Joi.string().when('role', { is: 'assistant', then: Joi.optional(), otherwise: Joi.forbidden() }),
  affiliationType: Joi.string().valid('university', 'dentist').when('role', { is: 'assistant', then: Joi.optional() }),
}).min(1);

exports.getAllAccounts = async (req, res) => {
  try {
    // Get all accounts from Firebase collections
    const [students, supervisors, admins, assistants, superadmins] = await Promise.all([
      FirestoreHelpers.find(COLLECTIONS.STUDENTS),
      FirestoreHelpers.find(COLLECTIONS.SUPERVISORS),
      FirestoreHelpers.find(COLLECTIONS.ADMINS),
      FirestoreHelpers.find(COLLECTIONS.ASSISTANTS),
      FirestoreHelpers.find(COLLECTIONS.CONFIGS, { field: 'role', operator: '==', value: 'superadmin' })
    ]);

    const accounts = [
      ...students.map(s => ({
        id: s.id,
        email: s.email,
        name: s.name,
        role: s.role || 'student',
        universityId: s.university,
        studentId: s.studentId,
        isGoogleAccount: s.isGoogleAccount || false,
        googleId: s.googleId || null,
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      })),
      ...supervisors.map(s => ({
        id: s.id,
        email: s.email,
        name: s.name,
        role: s.role || 'supervisor',
        universityId: s.university,
        isGoogleAccount: s.isGoogleAccount || false,
        googleId: s.googleId || null,
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      })),
      ...admins.map(a => ({
        id: a.id,
        email: a.email,
        name: a.name,
        role: a.role || 'admin',
        universityId: a.university,
        isGoogleAccount: a.isGoogleAccount || false,
        googleId: a.googleId || null,
        createdAt: a.createdAt,
        updatedAt: a.updatedAt
      })),
      ...assistants.map(a => ({
        id: a.id,
        email: a.email,
        name: a.name,
        role: a.role || 'assistant',
        universityId: a.university,
        isGoogleAccount: a.isGoogleAccount || false,
        googleId: a.googleId || null,
        createdAt: a.createdAt,
        updatedAt: a.updatedAt
      })),
      ...superadmins.map(s => ({
        id: s.id,
        email: s.email,
        name: s.name,
        role: s.role || 'superadmin',
        isGoogleAccount: s.isGoogleAccount || false,
        googleId: s.googleId || null,
        createdAt: s.createdAt,
        updatedAt: s.updatedAt
      }))
    ];

    res.status(200).json(accounts);
  } catch (error) {
    console.error('Error fetching accounts:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.createAccount = async (req, res) => {
  const { error } = accountSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, password, name, role, studentId, universityId } = req.body;

  try {
    // Check if email already exists in any collection
    const collections = [COLLECTIONS.STUDENTS, COLLECTIONS.SUPERVISORS, COLLECTIONS.ADMINS, COLLECTIONS.ASSISTANTS];

    for (const collection of collections) {
      const existingUser = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: email });
      if (existingUser) {
        return res.status(400).json({ message: 'Email already exists' });
      }
    }

    // Check superadmin collection
    const existingSuperadmin = await FirestoreHelpers.findOne(COLLECTIONS.CONFIGS, { field: 'email', operator: '==', value: email });
    if (existingSuperadmin) {
      return res.status(400).json({ message: 'Email already exists' });
    }

    // Check university ID for roles that require it
    if (['student', 'supervisor', 'admin', 'assistant'].includes(role)) {
      if (!universityId) {
        return res.status(400).json({
          message: 'University ID is required for this role'
        });
      }
      const university = await FirestoreHelpers.findOne(COLLECTIONS.UNIVERSITIES, { field: 'universityId', operator: '==', value: universityId });
      if (!university) {
        return res.status(400).json({ message: 'Invalid university ID' });
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    let newUser;
    let targetCollection;

    // Create user data based on role
    const userData = {
      email,
      password: hashedPassword,
      plainPassword: password, // Store plain password for compatibility
      name,
      role,
      university: universityId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    if (role === 'student') {
      // Check if student ID already exists
      if (studentId) {
        const existingStudent = await FirestoreHelpers.findOne(COLLECTIONS.STUDENTS, { field: 'studentId', operator: '==', value: studentId });
        if (existingStudent) {
          return res.status(400).json({ message: 'Student ID already exists' });
        }
        userData.studentId = studentId;
      }
      userData.patients = [];
      userData.reviews = [];
      userData.appointments = [];
      targetCollection = COLLECTIONS.STUDENTS;
    } else if (role === 'supervisor') {
      targetCollection = COLLECTIONS.SUPERVISORS;
    } else if (role === 'admin') {
      targetCollection = COLLECTIONS.ADMINS;
    } else if (role === 'assistant') {
      targetCollection = COLLECTIONS.ASSISTANTS;
    }

    // Create the user in Firebase
    newUser = await FirestoreHelpers.create(targetCollection, userData);

    // Log account creation activity
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Created user account',
        details: `Role: ${role}, Email: ${email}, Name: ${name}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging account creation:', logError);
    }

    res.status(201).json({ message: 'Account created successfully' });
  } catch (error) {
    console.error('Error creating account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.createGoogleAccount = async (req, res) => {
  const { error } = googleAccountSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { email, name, role, studentId, universityId } = req.body;

  try {
    // Check if email already exists in any collection
    const collections = [COLLECTIONS.STUDENTS, COLLECTIONS.SUPERVISORS, COLLECTIONS.ADMINS, COLLECTIONS.ASSISTANTS];

    for (const collection of collections) {
      const existingUser = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: email });
      if (existingUser) {
        return res.status(400).json({ message: 'Email already exists' });
      }
    }

    // Check superadmin collection
    const existingSuperadmin = await FirestoreHelpers.findOne(COLLECTIONS.CONFIGS, { field: 'email', operator: '==', value: email });
    if (existingSuperadmin) {
      return res.status(400).json({ message: 'Email already exists' });
    }

    // Validate university exists if required
    if (universityId) {
      const university = await FirestoreHelpers.findOne(COLLECTIONS.UNIVERSITIES, { field: 'universityId', operator: '==', value: universityId });
      if (!university) {
        return res.status(400).json({ message: 'University not found' });
      }
    }

    let newUser;
    let targetCollection;

    // Create user data based on role (no password for Google accounts)
    const userData = {
      email,
      name,
      role,
      university: universityId,
      isGoogleAccount: true,
      googleId: '', // Will be set when user first logs in with Google
      picture: '', // Will be set when user first logs in with Google
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add studentId for students
    if (role === 'student' && studentId) {
      userData.studentId = studentId;
      userData.patients = [];
      userData.reviews = [];
      userData.appointments = [];
    }

    // Determine target collection
    if (role === 'student') {
      targetCollection = COLLECTIONS.STUDENTS;
    } else if (role === 'supervisor') {
      targetCollection = COLLECTIONS.SUPERVISORS;
      userData.students = [];
    } else if (role === 'admin') {
      targetCollection = COLLECTIONS.ADMINS;
    } else if (role === 'assistant') {
      targetCollection = COLLECTIONS.ASSISTANTS;
    }

    // Create the user in Firebase
    newUser = await FirestoreHelpers.create(targetCollection, userData);

    // Log account creation activity
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Created Google user account',
        details: `Role: ${role}, Email: ${email}, Name: ${name}`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging Google account creation:', logError);
    }

    res.status(201).json({ message: 'Google account created successfully' });
  } catch (error) {
    console.error('Error creating Google account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.updateAccount = async (req, res) => {
  const { error } = updateAccountSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ message: error.details[0].message });
  }

  const { id } = req.params;
  const updates = req.body;

  try {
    // Find user in all collections
    let user = null;
    let userCollection = null;

    const collections = [COLLECTIONS.STUDENTS, COLLECTIONS.SUPERVISORS, COLLECTIONS.ADMINS, COLLECTIONS.ASSISTANTS];

    for (const collection of collections) {
      user = await FirestoreHelpers.findById(collection, id);
      if (user) {
        userCollection = collection;
        break;
      }
    }

    // Check superadmin collection
    if (!user) {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, id);
      if (user && user.role === 'superadmin') {
        userCollection = COLLECTIONS.CONFIGS;
      }
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    // Check email uniqueness if email is being updated
    if (updates.email && updates.email !== user.email) {
      for (const collection of collections) {
        const existingUser = await FirestoreHelpers.findOne(collection, { field: 'email', operator: '==', value: updates.email });
        if (existingUser && existingUser.id !== id) {
          return res.status(400).json({ message: 'Email already exists' });
        }
      }
    }

    // Validate university if provided
    if (updates.universityId) {
      const university = await FirestoreHelpers.findOne(COLLECTIONS.UNIVERSITIES, { field: 'universityId', operator: '==', value: updates.universityId });
      if (!university) {
        return res.status(400).json({ message: 'Invalid university ID' });
      }
    }

    // Hash password if provided
    if (updates.password) {
      updates.password = await bcrypt.hash(updates.password, 10);
      updates.plainPassword = req.body.password; // Store plain password for compatibility
    }

    updates.updatedAt = new Date();

    // Update the user
    await FirestoreHelpers.update(userCollection, id, updates);

    res.status(200).json({ message: 'Account updated successfully' });
  } catch (error) {
    console.error('Error updating account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.deleteAccount = async (req, res) => {
  const { id } = req.params;

  try {
    // Find user in all collections
    let user = null;
    let userCollection = null;

    const collections = [COLLECTIONS.STUDENTS, COLLECTIONS.SUPERVISORS, COLLECTIONS.ADMINS, COLLECTIONS.ASSISTANTS];

    for (const collection of collections) {
      user = await FirestoreHelpers.findById(collection, id);
      if (user) {
        userCollection = collection;
        break;
      }
    }

    // Check superadmin collection
    if (!user) {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, id);
      if (user && user.role === 'superadmin') {
        userCollection = COLLECTIONS.CONFIGS;
      }
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    // Store user details for logging before deletion
    const userEmail = user.email || 'Unknown';
    const userName = user.name || 'Unknown';
    const userRole = user.role || 'Unknown';

    // Delete the user
    await FirestoreHelpers.delete(userCollection, id);

    // Log account deletion activity
    try {
      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Deleted user account',
        details: `Deleted ${userRole}: ${userName} (${userEmail})`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging account deletion:', logError);
    }

    res.status(200).json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reset password for an account
exports.resetPassword = async (req, res) => {
  const { id } = req.params;
  const { newPassword } = req.body;

  if (!newPassword || newPassword.length < 6) {
    return res.status(400).json({ message: 'Password must be at least 6 characters' });
  }

  try {
    // Find user in all collections
    let user = null;
    let userCollection = null;

    const collections = [COLLECTIONS.STUDENTS, COLLECTIONS.SUPERVISORS, COLLECTIONS.ADMINS, COLLECTIONS.ASSISTANTS];

    for (const collection of collections) {
      user = await FirestoreHelpers.findById(collection, id);
      if (user) {
        userCollection = collection;
        break;
      }
    }

    // Check superadmin collection
    if (!user) {
      user = await FirestoreHelpers.findById(COLLECTIONS.CONFIGS, id);
      if (user && user.role === 'superadmin') {
        userCollection = COLLECTIONS.CONFIGS;
      }
    }

    if (!user) {
      return res.status(404).json({ message: 'Account not found' });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    await FirestoreHelpers.update(userCollection, id, {
      password: hashedPassword,
      plainPassword: newPassword,
      updatedAt: new Date()
    });

    // Log password reset activity
    try {
      const userEmail = user.email || 'Unknown';
      const userName = user.name || 'Unknown';

      await FirestoreHelpers.create(COLLECTIONS.ACTIVITY_LOGS, {
        userId: req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Reset user password',
        details: `Reset password for: ${userName} (${userEmail})`,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('Error logging password reset:', logError);
    }

    res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({ message: 'Server error' });
  }
};