import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import './components/i18n';
//main imports
import Home from './pages/Home';
import About  from './pages/About';
import UniversityServices from './pages/UniversityServices';
import Contact from './pages/Contact';
import Login from './pages/Login';
import TryAI from './pages/TryAI'
import Universities from './pages/Universities';
import UniversityInfo from './pages/UniversityInfo';
import UniversityBook from './pages/UniversityBook';
import UniversityConfirmation from './pages/UniversityConfirmation';
import Profile from './components/Profile'
import ForgotPassword from './components/ForgotPassword';
import Support from './components/Support';
//student
import StudentDashboard from './student/Dashboard';
import StudentCalendar from './student/Calendar';
import StudentPatients from './student/Patients';
import StudentAnalytics from './student/Analytics';
import StudentReviews from './student/Reviews';
import StudentPatientProfile from './student/PatientProfile';
import StudentGallery from './student/Gallery';
// import StudentXRay from './student/XRay'; // No longer needed after merging with Gallery
import StudentToothChart from './student/ToothChart';
import StudentAppointments from './student/Appointments';
import StudentReviewSteps from './student/ReviewSteps'
import StudentSheets from './student/Sheets'
import StudentHistory from './student/History'
import StudentConsent from './student/Consent'
import StudentLab from './student/Lab'
import StudentMedical from './student/Medical'
//supervisor
import SupervisorDashboard from './supervisor/Dashboard';
//admin
import AdminDashboard from './admin/Dashboard';
import AdminPeople from './admin/People';
import AdminAppointments from './admin/Appointments';
import AdminAnalytics from './admin/Analytics';
import AdminNews from './admin/News';
import AdminReviews from './admin/Reviews';
import AdminLabRequests from './admin/LabRequests';
//superadmin
import SuperadminDashboard from './superadmin/Dashboard';
import SuperadminUniversities from './superadmin/Universities';
import SuperadminAnalytics from './superadmin/Analytics';
import SuperadminNews from './superadmin/News';
import SuperadminAccounts from './superadmin/Accounts';
import SuperadminActivity from './superadmin/Activity';
//dentist
// Dentist routes not currently used in the application
// import DentistDashboard from './dentist/Dashboard';
// import DentistCalendar from './dentist/Calendar';
// import DentistPatients from './dentist/Patients';
// import DentistMessages from './dentist/Messages';
// import DentistAnalytics from './dentist/Analytics';
// import DentistPatientProfile from './dentist/PatientProfile';
// import DentistGallery from './dentist/Gallery';
// import DentistXRay from './dentist/XRay';
// import DentistToothChart from './dentist/ToothChart';
// import DentistAppointments from './dentist/Appointments';
//assistant
import AssistantDashboard from './assistant/Dashboard';
import AssistantAppointments from './assistant/Appointments';
import AssistantAnalytics from './assistant/Analytics';
import AssistantProcedureRequests from './assistant/ProcedureRequests';
import AssistantPatients from './assistant/Patients';
import AssistantLabRequests from './assistant/LabRequests';


// Generic ProtectedRoute component
const ProtectedRoute = ({ children }) => {
  const { user } = useAuth();
  if (!user) return <Navigate to="/login" replace />;
  return children;
};

// Role-specific wrappers
const StudentRoute = ({ children }) => <ProtectedRoute><StudentCheck>{children}</StudentCheck></ProtectedRoute>;
const SupervisorRoute = ({ children }) => <ProtectedRoute><SupervisorCheck>{children}</SupervisorCheck></ProtectedRoute>;
const AdminRoute = ({ children }) => <ProtectedRoute><AdminCheck>{children}</AdminCheck></ProtectedRoute>;
const SuperadminRoute = ({ children }) => <ProtectedRoute><SuperadminCheck>{children}</SuperadminCheck></ProtectedRoute>;
const AssistantRoute = ({ children }) => <ProtectedRoute><AssistantCheck>{children}</AssistantCheck></ProtectedRoute>;

// Role-specific checks
const StudentCheck = ({ children }) => {
  const { user } = useAuth();
  return user.role === 'student' ? children : <Navigate to="/dashboard" replace />;
};
const SupervisorCheck = ({ children }) => {
  const { user } = useAuth();
  return user.role === 'supervisor' ? children : <Navigate to="/dashboard" replace />;
};
const AdminCheck = ({ children }) => {
  const { user } = useAuth();
  return user.role === 'admin' ? children : <Navigate to="/dashboard" replace />;
};
const SuperadminCheck = ({ children }) => {
  const { user } = useAuth();
  return user.role === 'superadmin' ? children : <Navigate to="/dashboard" replace />;
};
const AssistantCheck = ({ children }) => {
  const { user } = useAuth();
  return user.role === 'assistant' ? children : <Navigate to="/dashboard" replace />;
};

// Dynamic dashboard redirect based on role
const DashboardRedirect = () => {
  const { user } = useAuth();
  if (!user) return <Navigate to="/login" replace />;

  switch (user.role) {
    case 'student':
      return <Navigate to="/student/dashboard" replace />;
    case 'supervisor':
      return <Navigate to="/supervisor/dashboard" replace />;
    case 'admin':
      return <Navigate to="/admin/dashboard" replace />;
    case 'superadmin':
      return <Navigate to="/superadmin/dashboard" replace />;
    case 'assistant':
      return <Navigate to="/assistant/dashboard" replace />;

    default:
      return <Navigate to="/" replace />; // Fallback for unexpected roles
  }
};

const AppRoutes = () => (
  <Routes>
    {/* Public Routes */}
    <Route path="/" element={<Home />} />
      <Route path="/about" element={<About />} />
      <Route path="/universityServices" element={<UniversityServices />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/login" element={<Login />} />
      <Route path="/try-ai" element={<TryAI />} />
      <Route path="/universities" element={<Universities />} />
      <Route path="/university-info/:universityId" element={<UniversityInfo />} />
      <Route path="/university-book" element={<UniversityBook />} />
      <Route path="/university-confirmation" element={<UniversityConfirmation />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />


    {/* Dynamic Dashboard Redirect */}
    <Route path="/dashboard" element={<DashboardRedirect />} />

    {/* Student Routes */}
    <Route path="/student/dashboard" element={<StudentRoute><StudentDashboard /></StudentRoute>} />
    <Route path="/calendar" element={<StudentRoute><StudentCalendar /></StudentRoute>} />
    <Route path="/patients" element={<StudentRoute><StudentPatients /></StudentRoute>} />
    {/* <Route path="/messages" element={<StudentRoute><StudentMessages /></StudentRoute>} /> */}
    <Route path="/analytics" element={<StudentRoute><StudentAnalytics /></StudentRoute>} />
    <Route path="/reviews" element={<StudentRoute><StudentReviews /></StudentRoute>} />
    <Route path="/lab" element={<StudentRoute><StudentLab /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId" element={<StudentRoute><StudentPatientProfile /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/gallery" element={<StudentRoute><StudentGallery /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/xrays" element={<Navigate to="../gallery" replace />} />
    <Route path="/patientprofile/:nationalId/toothchart" element={<StudentRoute><StudentToothChart /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/appointments" element={<StudentRoute><StudentAppointments /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/reviewsteps" element={<StudentRoute><StudentReviewSteps /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/sheets" element={<StudentRoute><StudentSheets /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/medical" element={<StudentRoute><StudentMedical /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/history" element={<StudentRoute><StudentHistory /></StudentRoute>} />
    <Route path="/patientprofile/:nationalId/consent" element={<StudentRoute><StudentConsent /></StudentRoute>} />

    <Route path="/profile" element={<Profile />} />
    <Route path="/support" element={<ProtectedRoute><Support /></ProtectedRoute>} />



    {/* Supervisor Routes */}
    <Route path="/supervisor/dashboard" element={<SupervisorRoute><SupervisorDashboard /></SupervisorRoute>} />


    {/* Admin Routes */}
    <Route path="/admin/dashboard" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
    <Route path="/admin/people" element={<AdminRoute><AdminPeople /></AdminRoute>} />
    <Route path="/admin/appointments" element={<AdminRoute><AdminAppointments /></AdminRoute>} />
    <Route path="/admin/analytics" element={<AdminRoute><AdminAnalytics /></AdminRoute>} />
    <Route path="/admin/news" element={<AdminRoute><AdminNews /></AdminRoute>} />
    <Route path="/admin/reviews" element={<AdminRoute><AdminReviews /></AdminRoute>} />
    <Route path="/admin/lab-requests" element={<AdminRoute><AdminLabRequests /></AdminRoute>} />

     {/* SuperAdmin Routes */}
    <Route path="/superadmin/dashboard" element={<SuperadminRoute><SuperadminDashboard /></SuperadminRoute>} />
    <Route path="/superadmin/universities" element={<SuperadminRoute><SuperadminUniversities /></SuperadminRoute>} />
    <Route path="/superadmin/analytics" element={<SuperadminRoute><SuperadminAnalytics /></SuperadminRoute>} />
    <Route path="/superadmin/activity" element={<SuperadminRoute><SuperadminActivity /></SuperadminRoute>} />
    <Route path="/superadmin/news" element={<SuperadminRoute><SuperadminNews /></SuperadminRoute>} />
    <Route path="/superadmin/accounts" element={<SuperadminRoute><SuperadminAccounts /></SuperadminRoute>} />

    {/* Assistant Routes */}
    <Route path="/assistant/dashboard" element={<AssistantRoute><AssistantDashboard /></AssistantRoute>} />
    <Route path="/assistant/appointments" element={<AssistantRoute><AssistantAppointments /></AssistantRoute>} />
    <Route path="/assistant/procedure-requests" element={<AssistantRoute><AssistantProcedureRequests /></AssistantRoute>} />
    <Route path="/assistant/lab-requests" element={<AssistantRoute><AssistantLabRequests /></AssistantRoute>} />
    <Route path="/assistant/analytics" element={<AssistantRoute><AssistantAnalytics /></AssistantRoute>} />
    <Route path="/assistant/patients" element={<AssistantRoute><AssistantPatients /></AssistantRoute>} />

    {/* 404 */}
    <Route path="*" element={<Navigate to="/" replace />} />
  </Routes>
);

export default AppRoutes;