import React, { useEffect, useState } from 'react';
import { useData } from '../context/DataContext';
import { useAuth } from '../context/AuthContext';

/**
 * Higher-order component that provides data optimization for components
 * that frequently fetch patient, student, or procedure request data
 */
const withDataOptimization = (WrappedComponent, options = {}) => {
  const {
    // Data types to preload
    preloadPatients = false,
    preloadStudents = false,
    preloadProcedureRequests = false,
    
    // Pagination options for preloading
    patientsOptions = { page: 1, limit: 20 },
    studentsOptions = { page: 1, limit: 50 },
    
    // Auto-refresh intervals (in milliseconds)
    autoRefreshInterval = null,
    
    // Component name for debugging
    componentName = 'UnknownComponent'
  } = options;

  return function OptimizedComponent(props) {
    const { user, token } = useAuth();
    const {
      loading,
      error,
      getPatients,
      getStudents,
      getProcedureRequests,
      getPatientDetails,
      invalidateData
    } = useData();

    const [isInitialized, setIsInitialized] = useState(false);
    const [localError, setLocalError] = useState(null);

    // Preload data when component mounts
    useEffect(() => {
      const preloadData = async () => {
        if (!user || !token) return;

        try {
          setLocalError(null);
          console.log(`[${componentName}] Preloading data...`);

          const promises = [];

          if (preloadPatients) {
            promises.push(getPatients(patientsOptions));
          }

          if (preloadStudents) {
            promises.push(getStudents(studentsOptions));
          }

          if (preloadProcedureRequests) {
            promises.push(getProcedureRequests());
          }

          if (promises.length > 0) {
            await Promise.all(promises);
            console.log(`[${componentName}] Data preloaded successfully`);
          }

          setIsInitialized(true);
        } catch (err) {
          console.error(`[${componentName}] Error preloading data:`, err);
          setLocalError(err.message);
          setIsInitialized(true); // Still allow component to render
        }
      };

      preloadData();
    }, [user, token, getPatients, getStudents, getProcedureRequests]);

    // Auto-refresh data at specified intervals
    useEffect(() => {
      if (!autoRefreshInterval || !isInitialized) return;

      const interval = setInterval(async () => {
        try {
          console.log(`[${componentName}] Auto-refreshing data...`);
          
          const promises = [];

          if (preloadPatients) {
            promises.push(getPatients({ ...patientsOptions, forceRefresh: true }));
          }

          if (preloadStudents) {
            promises.push(getStudents({ ...studentsOptions, forceRefresh: true }));
          }

          if (preloadProcedureRequests) {
            promises.push(getProcedureRequests(true));
          }

          if (promises.length > 0) {
            await Promise.all(promises);
            console.log(`[${componentName}] Auto-refresh completed`);
          }
        } catch (err) {
          console.error(`[${componentName}] Auto-refresh error:`, err);
        }
      }, autoRefreshInterval);

      return () => clearInterval(interval);
    }, [isInitialized, autoRefreshInterval, getPatients, getStudents, getProcedureRequests]);

    // Enhanced props with data optimization utilities
    const enhancedProps = {
      ...props,
      
      // Data fetching functions (cached)
      getPatients,
      getStudents,
      getProcedureRequests,
      getPatientDetails,
      
      // Cache management
      invalidateData,
      
      // Loading states
      isDataLoading: loading,
      isDataInitialized: isInitialized,
      
      // Error handling
      dataError: error || localError,
      
      // Utility functions
      refreshAllData: async () => {
        console.log(`[${componentName}] Refreshing all data...`);
        invalidateData('all');
        
        const promises = [];
        if (preloadPatients) promises.push(getPatients({ ...patientsOptions, forceRefresh: true }));
        if (preloadStudents) promises.push(getStudents({ ...studentsOptions, forceRefresh: true }));
        if (preloadProcedureRequests) promises.push(getProcedureRequests(true));
        
        if (promises.length > 0) {
          await Promise.all(promises);
        }
      },
      
      refreshPatients: () => {
        console.log(`[${componentName}] Refreshing patients data...`);
        invalidateData('patients');
        return getPatients({ ...patientsOptions, forceRefresh: true });
      },
      
      refreshStudents: () => {
        console.log(`[${componentName}] Refreshing students data...`);
        invalidateData('students');
        return getStudents({ ...studentsOptions, forceRefresh: true });
      },
      
      refreshProcedureRequests: () => {
        console.log(`[${componentName}] Refreshing procedure requests data...`);
        invalidateData('procedure-requests');
        return getProcedureRequests(true);
      }
    };

    return <WrappedComponent {...enhancedProps} />;
  };
};

// Preset configurations for common use cases
export const withPatientsOptimization = (Component, customOptions = {}) => {
  return withDataOptimization(Component, {
    preloadPatients: true,
    preloadStudents: true, // Often needed for patient assignments
    componentName: Component.displayName || Component.name || 'PatientsComponent',
    ...customOptions
  });
};

export const withStudentsOptimization = (Component, customOptions = {}) => {
  return withDataOptimization(Component, {
    preloadStudents: true,
    componentName: Component.displayName || Component.name || 'StudentsComponent',
    ...customOptions
  });
};

export const withProcedureRequestsOptimization = (Component, customOptions = {}) => {
  return withDataOptimization(Component, {
    preloadProcedureRequests: true,
    preloadStudents: true, // Often needed for procedure requests
    componentName: Component.displayName || Component.name || 'ProcedureRequestsComponent',
    ...customOptions
  });
};

export const withFullDataOptimization = (Component, customOptions = {}) => {
  return withDataOptimization(Component, {
    preloadPatients: true,
    preloadStudents: true,
    preloadProcedureRequests: true,
    componentName: Component.displayName || Component.name || 'FullDataComponent',
    ...customOptions
  });
};

export default withDataOptimization;
